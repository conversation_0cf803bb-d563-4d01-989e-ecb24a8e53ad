{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common\";\nfunction HomeComponent_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26);\n    i0.ɵɵelement(1, \"img\", 27);\n    i0.ɵɵelementStart(2, \"div\", 28)(3, \"h5\");\n    i0.ɵɵtext(4, \"Sale\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 29);\n    i0.ɵɵelement(6, \"i\", 30);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 31);\n    i0.ɵɵelement(8, \"i\", 32)(9, \"i\", 32)(10, \"i\", 32)(11, \"i\", 32)(12, \"i\", 33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 34)(14, \"h4\");\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"h4\");\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"p\");\n    i0.ɵɵtext(19, \"50dt-80dt\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r1 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate(\"alt\", item_r1.imgSrc);\n    i0.ɵɵproperty(\"src\", item_r1.imgSrc, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(14);\n    i0.ɵɵtextInterpolate(item_r1.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r1.description);\n  }\n}\nexport class HomeComponent {\n  constructor(router) {\n    this.router = router;\n    this.items = Array.from({\n      length: 7\n    }, (_, i) => ({\n      id: i + 1,\n      title: `Item ${i + 1}`,\n      description: `Description ${i + 1}`,\n      imgSrc: `assets/${i + 1}.jpg`\n    }));\n  }\n  navigateToShop() {\n    this.router.navigate(['/shop']);\n  }\n  static {\n    this.ɵfac = function HomeComponent_Factory(t) {\n      return new (t || HomeComponent)(i0.ɵɵdirectiveInject(i1.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HomeComponent,\n      selectors: [[\"app-home\"]],\n      decls: 76,\n      vars: 1,\n      consts: [[1, \"section\"], [3, \"click\"], [1, \"trending-product\"], [1, \"center-text\"], [1, \"grid-Products\"], [\"class\", \"grid-row\", 4, \"ngFor\", \"ngForOf\"], [1, \"client-reviews\"], [1, \"reviews\"], [\"src\", \"assets/woman.jpg\", \"alt\", \"\"], [1, \"contact\"], [1, \"contact-info\"], [1, \"first-info\"], [\"src\", \"assets/women's wear.png\", \"alt\", \"\"], [1, \"social-icon\"], [\"href\", \"https://www.facebook.com/facebook/\"], [1, \"bx\", \"bxl-facebook-circle\"], [\"href\", \"https://x.com/\"], [1, \"bx\", \"bxl-twitter\"], [\"href\", \"https://www.instagram.com/instagram/\"], [1, \"bx\", \"bxl-instagram\"], [\"href\", \"https://www.youtube.com/@YouTube\"], [1, \"bx\", \"bxl-youtube\"], [\"href\", \"https://www.linkedin.com/feed/\"], [1, \"bx\", \"bxl-linkedin\"], [1, \"second-info\"], [1, \"third-info\"], [1, \"grid-row\"], [3, \"src\", \"alt\"], [1, \"product-text\"], [1, \"heart-icon\"], [1, \"bx\", \"bx-heart\"], [1, \"ratting\"], [1, \"bx\", \"bxs-star\"], [1, \"bx\", \"bxs-star-half\"], [1, \"price\"]],\n      template: function HomeComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"h5\");\n          i0.ɵɵtext(2, \"2024 collection\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"h1\");\n          i0.ɵɵtext(4, \"New\");\n          i0.ɵɵelement(5, \"br\");\n          i0.ɵɵtext(6, \"collection 2024\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"h6\");\n          i0.ɵɵtext(8, \"There's nothing like trend\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"p\", 1);\n          i0.ɵɵlistener(\"click\", function HomeComponent_Template_p_click_9_listener() {\n            return ctx.navigateToShop();\n          });\n          i0.ɵɵtext(10, \"Shop now\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(11, \"div\", 2)(12, \"div\", 3)(13, \"h2\");\n          i0.ɵɵtext(14, \"Our tranding \");\n          i0.ɵɵelementStart(15, \"span\");\n          i0.ɵɵtext(16, \"Products\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(17, \"div\", 4);\n          i0.ɵɵtemplate(18, HomeComponent_div_18_Template, 20, 4, \"div\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(19, \"br\")(20, \"br\");\n          i0.ɵɵelementStart(21, \"section\", 6)(22, \"div\", 7)(23, \"h3\");\n          i0.ɵɵtext(24, \"Client Reviews\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(25, \"img\", 8);\n          i0.ɵɵelementStart(26, \"p\");\n          i0.ɵɵtext(27, \"Hello, my name is Sarah, and I\\u2019ve been shopping for stylish and comfortable women\\u2019s clothing online for years. I\\u2019m always looking for outfits that reflect my personality\\u2014elegant yet practical for my busy lifestyle. I appreciate a website that's easy to navigate with clear size guides, diverse collections, and quality fabrics. Fast delivery and excellent customer service are also a must for me. A good online shopping experience keeps me coming back for more!\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"h2\");\n          i0.ɵɵtext(29, \"Sarah\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"p\");\n          i0.ɵɵtext(31, \"CEO of Addle\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(32, \"section\", 9)(33, \"div\", 10)(34, \"div\", 11);\n          i0.ɵɵelement(35, \"img\", 12);\n          i0.ɵɵelementStart(36, \"p\");\n          i0.ɵɵtext(37, \"123 Olive Street, Sidi Bouzid, 9100\");\n          i0.ɵɵelement(38, \"br\");\n          i0.ɵɵtext(39, \" Tunisia\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"p\");\n          i0.ɵɵtext(41, \"<EMAIL>\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"div\", 13)(43, \"a\", 14);\n          i0.ɵɵelement(44, \"i\", 15);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"a\", 16);\n          i0.ɵɵelement(46, \"i\", 17);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"a\", 18);\n          i0.ɵɵelement(48, \"i\", 19);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"a\", 20);\n          i0.ɵɵelement(50, \"i\", 21);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"a\", 22);\n          i0.ɵɵelement(52, \"i\", 23);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(53, \"div\", 24)(54, \"h4\");\n          i0.ɵɵtext(55, \"Support\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(56, \"p\");\n          i0.ɵɵtext(57, \"About us\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(58, \"p\");\n          i0.ɵɵtext(59, \"Contact us\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(60, \"p\");\n          i0.ɵɵtext(61, \"size guide\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(62, \"p\");\n          i0.ɵɵtext(63, \"Privacy\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(64, \"p\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(65, \"div\", 25)(66, \"h4\");\n          i0.ɵɵtext(67, \"Company\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(68, \"p\");\n          i0.ɵɵtext(69, \"About\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(70, \"p\");\n          i0.ɵɵtext(71, \"Blog\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(72, \"p\");\n          i0.ɵɵtext(73, \"Affiliate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(74, \"p\");\n          i0.ɵɵtext(75, \"Login\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(18);\n          i0.ɵɵproperty(\"ngForOf\", ctx.items);\n        }\n      },\n      dependencies: [i2.NgForOf],\n      styles: [\"*[_ngcontent-%COMP%]{\\n    margin: 0;\\n    padding: 0;\\n    box-sizing: border-box;\\n    scroll-behavior: smooth;\\n    font-family: 'sans-serif';\\n    list-style: none;\\n    text-decoration: none;\\n}\\n.section[_ngcontent-%COMP%]{\\n    padding: 5% 10%;\\n    width: 100%;\\n    height: 100vh;\\n    background-image:url('banner-3.png');\\n    background-position: center;\\n    background-size:cover ;\\n    display: grid;\\n    grid-template-columns: repeat(1,1fr);\\n    align-items: center;\\n    h5{\\n        color: #a4936d;\\n        font-size: 16px;\\n        text-transform: capitalize;\\n        font-weight: 500;\\n    }\\n    h1{\\n        color: #000;\\n        font-size: 65px;\\n        text-transform: capitalize;\\n        line-height: 1.1;\\n        font-weight: 600;\\n        margin: 6px 0 10px;\\n    }\\n    h6{\\n        color: #333c65;\\n        font-size: 20px;\\n        font-style: italic;\\n        margin-bottom: 20px;\\n    }\\n    p{\\n        display: inline-block;\\n        color: #111;\\n        font-size: 16px;\\n        font-weight: 500;\\n        text-transform: capitalize;\\n        border: 2px solid #111;\\n        width: 130px;\\n        height: 50px;\\n        padding: 12px 25px;\\n        transition: all .42s ease;\\n        cursor: pointer;\\n    }\\n    p:hover{\\n        background-color: #000;\\n        color: white;\\n    }\\n}\\n.center-text[_ngcontent-%COMP%]{\\n    h2{\\n        color:#111;\\n        font-size: 28px;\\n        text-transform: capitalize;\\n        text-align: center;\\n        margin-bottom: 30px;\\n    }\\n    span{\\n        color: #a4936d;\\n\\n    }\\n}\\n.grid-Products[_ngcontent-%COMP%] {\\n    display: grid;\\n    grid-template-columns: repeat(4, 1fr);\\n    gap: 20px; \\n\\n    justify-content: space-between; \\n\\n}\\n\\n.grid-row[_ngcontent-%COMP%] {\\n  text-align: center; \\n\\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1); \\n\\n  padding: 10px; \\n\\n  border: 1px solid #ccc; \\n\\n}\\n\\n.grid-row[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    width: 100%;\\n    height: auto;\\n    border-radius: 5px; \\n\\n}\\n\\n.grid-row[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]:hover {\\n    transform: scale(0.9); \\n\\n}\\n.product-text[_ngcontent-%COMP%]{\\n    h5{\\n        position: absolute;\\n        top: 13px;\\n        left: 13px;\\n        color: #fff;\\n        font-size: 12px;\\n        font-weight: 500;\\n    }\\n}\\n.client-reviews[_ngcontent-%COMP%]{\\n    background-color: #F3F4F6;\\n}\\n.reviews[_ngcontent-%COMP%]{\\n    text-align: center;\\n    h3{\\n        color: #111;\\n        font-size: 25px;\\n        text-transform: capitalize;\\n        text-align: center;\\n        font-weight: 700;\\n    }\\n    img{\\n        width: 100px;\\n        height: auto;\\n        border-radius: 3000px;\\n        margin: 10px 0;\\n    }\\n    p{\\n        color: #707070;\\n        font-size: 16px;\\n        font-weight: 400;\\n        line-height: 25px;\\n        margin-bottom: 10px;\\n    }\\n    h2{\\n        font-size: 22px;\\n        color: #000;\\n        font-weight: 400;\\n        text-transform: capitalize;\\n        margin-bottom: 2px;\\n    }\\n}\\n.contact[_ngcontent-%COMP%]{\\n    background-color: #fff;\\n    border: #F3F4F6,double;\\n}\\n.contact-info[_ngcontent-%COMP%]{\\n    display: grid;\\n    grid-template-columns: repeat(auto-fit,minmax(160px,auto));\\n    gap:3rem\\n}\\n.contact-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{\\n    color:#212529;\\n    font-size: 14px;\\n    text-transform: uppercase;\\n    margin-bottom: 10px;\\n}\\n.first-info[_ngcontent-%COMP%]{\\n    img{\\n        width: 140px;\\n        height: auto;\\n\\n    }\\n}\\n.contact-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{\\n    color:#565656;\\n    font-size: 14px;\\n    font-weight: 400;\\n    text-transform: capitalize;\\n    line-height: 1.5;\\n    margin-bottom: 10px;\\n    cursor: pointer;\\n    transition: all .42s;\\n}\\n.contact-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:hover{\\n    color: #a4936d;\\n}\\n.social-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{\\n    \\n}\\n\\n\\n        \\n\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵpropertyInterpolate", "item_r1", "imgSrc", "ɵɵproperty", "ɵɵsanitizeUrl", "ɵɵtextInterpolate", "title", "description", "HomeComponent", "constructor", "router", "items", "Array", "from", "length", "_", "i", "id", "navigateToShop", "navigate", "ɵɵdirectiveInject", "i1", "Router", "selectors", "decls", "vars", "consts", "template", "HomeComponent_Template", "rf", "ctx", "ɵɵlistener", "HomeComponent_Template_p_click_9_listener", "ɵɵtemplate", "HomeComponent_div_18_Template"], "sources": ["C:\\Users\\<USER>\\Desktop\\projet angular\\Projet\\src\\home\\home.component.ts", "C:\\Users\\<USER>\\Desktop\\projet angular\\Projet\\src\\home\\home.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { Router } from '@angular/router';\n\n@Component({\n  selector: 'app-home',\n  templateUrl: './home.component.html',\n  styleUrls: ['./home.component.css']\n})\nexport class HomeComponent {\n  constructor(private router:Router){}\n  items = Array.from({ length: 7 }, (_, i) => ({\n    id: i + 1,\n    title: `Item ${i + 1}`,\n    description: `Description ${i + 1}`,\n    imgSrc: `assets/${i + 1}.jpg`\n  }));  \n  navigateToShop() {\n    this.router.navigate(['/shop']);\n}\n}\n", "<div class=\"section\"><h5>2024 collection</h5>\n    <h1>New<br>collection 2024</h1>\n    <h6 >There's nothing like trend</h6>\n    <p (click)=\"navigateToShop()\">Shop now</p></div>\n    <div class=\"trending-product\">\n        <div class=\"center-text\">\n            <h2>Our tranding <span>Products</span></h2>\n        </div>\n        <div class=\"grid-Products\">\n            <div class=\"grid-row\" *ngFor=\"let item of items\">\n                <img [src]=\"item.imgSrc\" alt={{item.imgSrc}} >\n                <div class=\"product-text\">\n                    <h5>Sale</h5>\n                </div>\n                <div class=\"heart-icon\">\n                    <i class='bx bx-heart'></i>\n                </div>\n                <div class=\"ratting\">\n                    <i class='bx bxs-star'></i>\n                    <i class='bx bxs-star'></i>\n                    <i class='bx bxs-star'></i>\n                    <i class='bx bxs-star'></i>\n                    <i class='bx bxs-star-half'></i>\n                </div>\n                <div class=\"price\">\n                    <h4>{{ item.title }}</h4>\n                    <h4>{{ item.description }}</h4>\n                    <p>50dt-80dt</p>\n                </div>\n            </div>\n        </div>\n        <br><br>\n        <section class=\"client-reviews\">\n            <div class=\"reviews\">\n                <h3>Client Reviews</h3>\n                <img src=\"assets/woman.jpg\"  alt=\"\">\n                <p>Hello, my name is Sarah, and I’ve been shopping for stylish and comfortable women’s clothing online for years. I’m always looking for outfits that reflect my personality—elegant yet practical for my busy lifestyle. I appreciate a website that's easy to navigate with clear size guides, diverse collections, and quality fabrics. Fast delivery and excellent customer service are also a must for me. A good online shopping experience keeps me coming back for more!</p>\n                <h2>Sarah</h2>\n                <p>CEO of Addle</p>\n            </div>\n        </section>\n        <section class=\"contact\">\n            <div class=\"contact-info\">\n                <div class=\"first-info\">\n                    <img src=\"assets/women's wear.png\" alt=\"\">\n                    <p>123 Olive Street, Sidi Bouzid, 9100<br> Tunisia</p>\n                    <p><EMAIL></p>\n                    <div class=\"social-icon\">\n                        <a href=\"https://www.facebook.com/facebook/\"><i class='bx bxl-facebook-circle'></i></a>\n                        <a href=\"https://x.com/\"><i class='bx bxl-twitter'></i></a>\n                        <a href=\"https://www.instagram.com/instagram/\"><i class='bx bxl-instagram'></i></a>\n                        <a href=\"https://www.youtube.com/@YouTube\"><i class='bx bxl-youtube'></i></a>\n                        <a href=\"https://www.linkedin.com/feed/\"><i class='bx bxl-linkedin'></i></a>\n                    </div>\n                </div>\n                <div class=\"second-info\">\n                    <h4>Support</h4>\n                    <p>About us</p>\n                    <p>Contact us</p>\n                    <p>size guide</p>\n                    <p>Privacy</p>\n                    <p></p>\n                </div>\n                <div class=\"third-info\">\n                    <h4>Company</h4>\n                    <p>About</p>\n                    <p>Blog</p>\n                    <p>Affiliate</p>\n                    <p>Login</p>\n                </div>\n            </div>\n        </section>\n    </div>\n"], "mappings": ";;;;;ICSYA,EAAA,CAAAC,cAAA,cAAiD;IAC7CD,EAAA,CAAAE,SAAA,cAA8C;IAC9CF,EAAA,CAAAC,cAAA,cAA0B;IAClBD,EAAA,CAAAG,MAAA,WAAI;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAEjBJ,EAAA,CAAAC,cAAA,cAAwB;IACpBD,EAAA,CAAAE,SAAA,YAA2B;IAC/BF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,cAAqB;IACjBD,EAAA,CAAAE,SAAA,YAA2B;IAK/BF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,eAAmB;IACXD,EAAA,CAAAG,MAAA,IAAgB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACzBJ,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAG,MAAA,IAAsB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC/BJ,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAG,MAAA,iBAAS;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;IAjBKJ,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAM,qBAAA,QAAAC,OAAA,CAAAC,MAAA,CAAmB;IAAvCR,EAAA,CAAAS,UAAA,QAAAF,OAAA,CAAAC,MAAA,EAAAR,EAAA,CAAAU,aAAA,CAAmB;IAehBV,EAAA,CAAAK,SAAA,IAAgB;IAAhBL,EAAA,CAAAW,iBAAA,CAAAJ,OAAA,CAAAK,KAAA,CAAgB;IAChBZ,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAAW,iBAAA,CAAAJ,OAAA,CAAAM,WAAA,CAAsB;;;ADlB9C,OAAM,MAAOC,aAAa;EACxBC,YAAoBC,MAAa;IAAb,KAAAA,MAAM,GAANA,MAAM;IAC1B,KAAAC,KAAK,GAAGC,KAAK,CAACC,IAAI,CAAC;MAAEC,MAAM,EAAE;IAAC,CAAE,EAAE,CAACC,CAAC,EAAEC,CAAC,MAAM;MAC3CC,EAAE,EAAED,CAAC,GAAG,CAAC;MACTV,KAAK,EAAE,QAAQU,CAAC,GAAG,CAAC,EAAE;MACtBT,WAAW,EAAE,eAAeS,CAAC,GAAG,CAAC,EAAE;MACnCd,MAAM,EAAE,UAAUc,CAAC,GAAG,CAAC;KACxB,CAAC,CAAC;EANgC;EAOnCE,cAAcA,CAAA;IACZ,IAAI,CAACR,MAAM,CAACS,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;EACnC;;;uBAVaX,aAAa,EAAAd,EAAA,CAAA0B,iBAAA,CAAAC,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAbd,aAAa;MAAAe,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCR1BnC,EAAA,CAAAC,cAAA,aAAqB;UAAID,EAAA,CAAAG,MAAA,sBAAe;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACzCJ,EAAA,CAAAC,cAAA,SAAI;UAAAD,EAAA,CAAAG,MAAA,UAAG;UAAAH,EAAA,CAAAE,SAAA,SAAI;UAAAF,EAAA,CAAAG,MAAA,sBAAe;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAC/BJ,EAAA,CAAAC,cAAA,SAAK;UAAAD,EAAA,CAAAG,MAAA,iCAA0B;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACpCJ,EAAA,CAAAC,cAAA,WAA8B;UAA3BD,EAAA,CAAAqC,UAAA,mBAAAC,0CAAA;YAAA,OAASF,GAAA,CAAAZ,cAAA,EAAgB;UAAA,EAAC;UAACxB,EAAA,CAAAG,MAAA,gBAAQ;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAC1CJ,EAAA,CAAAC,cAAA,cAA8B;UAElBD,EAAA,CAAAG,MAAA,qBAAa;UAAAH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAG,MAAA,gBAAQ;UAAAH,EAAA,CAAAI,YAAA,EAAO;UAE1CJ,EAAA,CAAAC,cAAA,cAA2B;UACvBD,EAAA,CAAAuC,UAAA,KAAAC,6BAAA,kBAoBM;UACVxC,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAE,SAAA,UAAI;UACJF,EAAA,CAAAC,cAAA,kBAAgC;UAEpBD,EAAA,CAAAG,MAAA,sBAAc;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACvBJ,EAAA,CAAAE,SAAA,cAAoC;UACpCF,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,yeAA6c;UAAAH,EAAA,CAAAI,YAAA,EAAI;UACpdJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,aAAK;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACdJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,oBAAY;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAG3BJ,EAAA,CAAAC,cAAA,kBAAyB;UAGbD,EAAA,CAAAE,SAAA,eAA0C;UAC1CF,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,2CAAmC;UAAAH,EAAA,CAAAE,SAAA,UAAI;UAACF,EAAA,CAAAG,MAAA,gBAAO;UAAAH,EAAA,CAAAI,YAAA,EAAI;UACtDJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,qCAA6B;UAAAH,EAAA,CAAAI,YAAA,EAAI;UACpCJ,EAAA,CAAAC,cAAA,eAAyB;UACwBD,EAAA,CAAAE,SAAA,aAAsC;UAAAF,EAAA,CAAAI,YAAA,EAAI;UACvFJ,EAAA,CAAAC,cAAA,aAAyB;UAAAD,EAAA,CAAAE,SAAA,aAA8B;UAAAF,EAAA,CAAAI,YAAA,EAAI;UAC3DJ,EAAA,CAAAC,cAAA,aAA+C;UAAAD,EAAA,CAAAE,SAAA,aAAgC;UAAAF,EAAA,CAAAI,YAAA,EAAI;UACnFJ,EAAA,CAAAC,cAAA,aAA2C;UAAAD,EAAA,CAAAE,SAAA,aAA8B;UAAAF,EAAA,CAAAI,YAAA,EAAI;UAC7EJ,EAAA,CAAAC,cAAA,aAAyC;UAAAD,EAAA,CAAAE,SAAA,aAA+B;UAAAF,EAAA,CAAAI,YAAA,EAAI;UAGpFJ,EAAA,CAAAC,cAAA,eAAyB;UACjBD,EAAA,CAAAG,MAAA,eAAO;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAChBJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,gBAAQ;UAAAH,EAAA,CAAAI,YAAA,EAAI;UACfJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,kBAAU;UAAAH,EAAA,CAAAI,YAAA,EAAI;UACjBJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,kBAAU;UAAAH,EAAA,CAAAI,YAAA,EAAI;UACjBJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,eAAO;UAAAH,EAAA,CAAAI,YAAA,EAAI;UACdJ,EAAA,CAAAE,SAAA,SAAO;UACXF,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAC,cAAA,eAAwB;UAChBD,EAAA,CAAAG,MAAA,eAAO;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAChBJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,aAAK;UAAAH,EAAA,CAAAI,YAAA,EAAI;UACZJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,YAAI;UAAAH,EAAA,CAAAI,YAAA,EAAI;UACXJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,iBAAS;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAChBJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,aAAK;UAAAH,EAAA,CAAAI,YAAA,EAAI;;;UA3DmBJ,EAAA,CAAAK,SAAA,IAAQ;UAARL,EAAA,CAAAS,UAAA,YAAA2B,GAAA,CAAAnB,KAAA,CAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}