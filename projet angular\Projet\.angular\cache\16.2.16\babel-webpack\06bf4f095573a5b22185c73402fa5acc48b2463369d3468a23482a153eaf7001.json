{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\nimport { FormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\nimport { HomeComponent } from '../home/<USER>';\nimport { ShopComponent } from '../shop/shop.component';\nimport { LoginComponent } from '../login/login.component';\nimport { LognComponent } from '../logn/logn.component';\nexport let AppModule = class AppModule {};\nAppModule = __decorate([NgModule({\n  declarations: [AppComponent, HomeComponent, ShopComponent, LoginComponent, LognComponent],\n  imports: [BrowserModule, AppRoutingModule, FormsModule, RouterModule],\n  providers: [],\n  bootstrap: [AppComponent]\n})], AppModule);", "map": {"version": 3, "names": ["NgModule", "BrowserModule", "AppRoutingModule", "AppComponent", "FormsModule", "RouterModule", "HomeComponent", "ShopComponent", "LoginComponent", "LognComponent", "AppModule", "__decorate", "declarations", "imports", "providers", "bootstrap"], "sources": ["C:\\Users\\<USER>\\Desktop\\projet angular\\Projet\\src\\app\\app.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { BrowserModule } from '@angular/platform-browser';\n\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\nimport { FormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\nimport { HomeComponent } from '../home/<USER>';\nimport { ShopComponent } from '../shop/shop.component';\nimport { LoginComponent } from '../login/login.component';\nimport { LognComponent } from '../logn/logn.component';\n\n@NgModule({\n  declarations: [\n    AppComponent,\n    HomeComponent,\n    ShopComponent,\n    LoginComponent,\n    LognComponent\n  ],\n  imports: [\n    BrowserModule,\n    AppRoutingModule,\n    FormsModule,\n    RouterModule\n  ],\n  providers: [],\n  bootstrap: [AppComponent]\n})\nexport class AppModule { }\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,aAAa,QAAQ,2BAA2B;AAEzD,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,0BAA0B;AACzD,SAASC,aAAa,QAAQ,wBAAwB;AAmB/C,WAAMC,SAAS,GAAf,MAAMA,SAAS,GAAI;AAAbA,SAAS,GAAAC,UAAA,EAjBrBX,QAAQ,CAAC;EACRY,YAAY,EAAE,CACZT,YAAY,EACZG,aAAa,EACbC,aAAa,EACbC,cAAc,EACdC,aAAa,CACd;EACDI,OAAO,EAAE,CACPZ,aAAa,EACbC,gBAAgB,EAChBE,WAAW,EACXC,YAAY,CACb;EACDS,SAAS,EAAE,EAAE;EACbC,SAAS,EAAE,CAACZ,YAAY;CACzB,CAAC,C,EACWO,SAAS,CAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}