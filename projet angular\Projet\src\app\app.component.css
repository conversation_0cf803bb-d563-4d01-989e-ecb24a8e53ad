nav {
  background-color: var(--white);
  box-shadow: var(--shadow);
  position: sticky;
  top: 0;
  z-index: 1000;
  width: 100%;
  border-bottom: 3px solid var(--primary);
}

nav .content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
}

nav img {
  width: 130px;
  transition: var(--transition);
  filter: drop-shadow(0 2px 5px rgba(240, 98, 146, 0.3));
}

nav img:hover {
  transform: scale(1.05);
}

nav .navmenu {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
}

nav li {
  margin: 0 20px;
  font-weight: 500;
  position: relative;
  cursor: pointer;
  transition: var(--transition);
  font-size: 16px;
  letter-spacing: 0.5px;
  color: var(--dark-pink);
}

nav li::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--primary);
  transition: var(--transition);
}

nav li:hover {
  color: var(--accent);
}

nav li:hover::after {
  width: 100%;
}

nav li:nth-child(1)::before {
  content: '♥';
  position: absolute;
  left: -15px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--primary);
  opacity: 0;
  transition: var(--transition);
}

nav li:nth-child(1):hover::before {
  opacity: 1;
  left: -20px;
}

nav li:nth-child(2)::before {
  content: '♥';
  position: absolute;
  left: -15px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--secondary);
  opacity: 0;
  transition: var(--transition);
}

nav li:nth-child(2):hover::before {
  opacity: 1;
  left: -20px;
}

nav ul:last-child li {
  background-color: var(--primary);
  padding: 8px 20px;
  border-radius: 30px;
  color: var(--white);
  transition: all 0.3s ease;
}

nav ul:last-child li:hover {
  background-color: var(--accent);
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(240, 98, 146, 0.3);
}

nav ul:last-child li::after {
  display: none;
}

.end-text {
  background-color: var(--white);
  text-align: center;
  padding: 20px;
  font-size: 14px;
  color: var(--dark-pink);
  border-top: 2px dashed var(--primary);
}

@media(max-width: 768px) {
  nav .content {
    flex-direction: column;
    padding: 15px 0;
  }
  
  nav .navmenu {
    margin-top: 15px;
  }
  
  nav li {
    margin: 0 10px;
  }
  
  nav ul:last-child {
    margin-top: 15px;
  }
}

