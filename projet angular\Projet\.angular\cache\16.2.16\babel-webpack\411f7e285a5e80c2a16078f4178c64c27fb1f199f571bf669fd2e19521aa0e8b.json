{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class HeaderComponent {\n  static {\n    this.ɵfac = function HeaderComponent_Factory(t) {\n      return new (t || HeaderComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HeaderComponent,\n      selectors: [[\"app-header\"]],\n      decls: 18,\n      vars: 0,\n      consts: [[1, \"container\"], [1, \"content\"], [\"src\", \"assets/logo.jpg\", \"alt\", \"\", \"routerLink\", \"/home\", 1, \"logo\"], [\"routerLink\", \"/home\"], [\"routerLink\", \"/shop\"], [\"routerLink\", \"/contact\"], [\"routerLink\", \"/about\"], [\"routerLink\", \"/cart\"], [\"routerLink\", \"/log\"]],\n      template: function HeaderComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"nav\")(1, \"div\", 0)(2, \"div\", 1);\n          i0.ɵɵelement(3, \"img\", 2);\n          i0.ɵɵelementStart(4, \"ul\")(5, \"li\", 3);\n          i0.ɵɵtext(6, \"Home\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"li\", 4);\n          i0.ɵɵtext(8, \"Shop\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"li\", 5);\n          i0.ɵɵtext(10, \"Contact\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"li\", 6);\n          i0.ɵɵtext(12, \"About\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(13, \"ul\")(14, \"li\", 7);\n          i0.ɵɵtext(15, \"Carts\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"li\", 8);\n          i0.ɵɵtext(17, \"Login\");\n          i0.ɵɵelementEnd()()()()();\n        }\n      },\n      dependencies: [i1.RouterLink],\n      styles: [\"nav[_ngcontent-%COMP%]{\\n    color:#fff;\\n    position: fixed;\\n    top: 0;\\n    z-index: 1000;\\n    width: 100%;\\n    img{\\n        width: 100px;\\n    }\\n    .content{\\n        display: flex;\\n        justify-content: space-between;\\n        align-items: center;\\n        padding:10px 0;\\n        ul{\\n            list-style: none;\\n            display: flex;\\n            justify-content: space-between;\\n            width: auto;\\n            margin: 0;\\n            padding: 0;\\n            li{\\n                cursor: pointer;\\n                color:#000;\\n                text-decoration: none;\\n                margin: 0 10px;\\n                \\n            }\\n            li:hover{\\n                color: rgb(238, 54, 66);\\n                transform: scale(1.1);\\n            }\\n        }\\n    }\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9oZWFkZXIvaGVhZGVyLmNvbXBvbmVudC5jc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7SUFDSSxVQUFVO0lBQ1YsZUFBZTtJQUNmLE1BQU07SUFDTixhQUFhO0lBQ2IsV0FBVztJQUNYO1FBQ0ksWUFBWTtJQUNoQjtJQUNBO1FBQ0ksYUFBYTtRQUNiLDhCQUE4QjtRQUM5QixtQkFBbUI7UUFDbkIsY0FBYztRQUNkO1lBQ0ksZ0JBQWdCO1lBQ2hCLGFBQWE7WUFDYiw4QkFBOEI7WUFDOUIsV0FBVztZQUNYLFNBQVM7WUFDVCxVQUFVO1lBQ1Y7Z0JBQ0ksZUFBZTtnQkFDZixVQUFVO2dCQUNWLHFCQUFxQjtnQkFDckIsY0FBYzs7WUFFbEI7WUFDQTtnQkFDSSx1QkFBdUI7Z0JBQ3ZCLHFCQUFxQjtZQUN6QjtRQUNKO0lBQ0o7QUFDSiIsInNvdXJjZXNDb250ZW50IjpbIm5hdntcclxuICAgIGNvbG9yOiNmZmY7XHJcbiAgICBwb3NpdGlvbjogZml4ZWQ7XHJcbiAgICB0b3A6IDA7XHJcbiAgICB6LWluZGV4OiAxMDAwO1xyXG4gICAgd2lkdGg6IDEwMCU7XHJcbiAgICBpbWd7XHJcbiAgICAgICAgd2lkdGg6IDEwMHB4O1xyXG4gICAgfVxyXG4gICAgLmNvbnRlbnR7XHJcbiAgICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47XHJcbiAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICAgICAgICBwYWRkaW5nOjEwcHggMDtcclxuICAgICAgICB1bHtcclxuICAgICAgICAgICAgbGlzdC1zdHlsZTogbm9uZTtcclxuICAgICAgICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgICAgICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xyXG4gICAgICAgICAgICB3aWR0aDogYXV0bztcclxuICAgICAgICAgICAgbWFyZ2luOiAwO1xyXG4gICAgICAgICAgICBwYWRkaW5nOiAwO1xyXG4gICAgICAgICAgICBsaXtcclxuICAgICAgICAgICAgICAgIGN1cnNvcjogcG9pbnRlcjtcclxuICAgICAgICAgICAgICAgIGNvbG9yOiMwMDA7XHJcbiAgICAgICAgICAgICAgICB0ZXh0LWRlY29yYXRpb246IG5vbmU7XHJcbiAgICAgICAgICAgICAgICBtYXJnaW46IDAgMTBweDtcclxuICAgICAgICAgICAgICAgIFxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIGxpOmhvdmVye1xyXG4gICAgICAgICAgICAgICAgY29sb3I6IHJnYigyMzgsIDU0LCA2Nik7XHJcbiAgICAgICAgICAgICAgICB0cmFuc2Zvcm06IHNjYWxlKDEuMSk7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICB9XHJcbn1cclxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["HeaderComponent", "selectors", "decls", "vars", "consts", "template", "HeaderComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd"], "sources": ["C:\\Users\\<USER>\\Desktop\\projet angular\\Projet\\src\\header\\header.component.ts", "C:\\Users\\<USER>\\Desktop\\projet angular\\Projet\\src\\header\\header.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-header',\n  templateUrl: './header.component.html',\n  styleUrls: ['./header.component.css']\n})\nexport class HeaderComponent {\n\n}\n", "<nav>\n    <div class=\"container\">\n        <div class=\"content\">\n            <img class=\"logo\" src=\"assets/logo.jpg\" alt=\"\"routerLink=\"/home\">\n            <ul>\n                <li routerLink=\"/home\">Home</li>\n                <li routerLink=\"/shop\" >Shop</li>\n                <li routerLink=\"/contact\">Contact</li>\n                <li routerLink=\"/about\">About</li>\n            </ul>\n            <ul> \n                <li routerLink=\"/cart\">Carts</li>\n                <li routerLink=\"/log\">Login</li>\n            </ul>\n        </div>\n    </div>\n</nav>\n"], "mappings": ";;AAOA,OAAM,MAAOA,eAAe;;;uBAAfA,eAAe;IAAA;EAAA;;;YAAfA,eAAe;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCP5BE,EAAA,CAAAC,cAAA,UAAK;UAGOD,EAAA,CAAAE,SAAA,aAAiE;UACjEF,EAAA,CAAAC,cAAA,SAAI;UACuBD,EAAA,CAAAG,MAAA,WAAI;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAChCJ,EAAA,CAAAC,cAAA,YAAwB;UAAAD,EAAA,CAAAG,MAAA,WAAI;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACjCJ,EAAA,CAAAC,cAAA,YAA0B;UAAAD,EAAA,CAAAG,MAAA,eAAO;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACtCJ,EAAA,CAAAC,cAAA,aAAwB;UAAAD,EAAA,CAAAG,MAAA,aAAK;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAEtCJ,EAAA,CAAAC,cAAA,UAAI;UACuBD,EAAA,CAAAG,MAAA,aAAK;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACjCJ,EAAA,CAAAC,cAAA,aAAsB;UAAAD,EAAA,CAAAG,MAAA,aAAK;UAAAH,EAAA,CAAAI,YAAA,EAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}