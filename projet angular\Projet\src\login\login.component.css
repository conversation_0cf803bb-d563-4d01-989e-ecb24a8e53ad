.auth-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 85vh;
  padding: 50px 20px;
  background: linear-gradient(135deg, var(--light-pink), var(--medium-pink));
}

.auth-card {
  width: 100%;
  max-width: 500px;
  background-color: white;
  border-radius: 20px;
  box-shadow: 0 15px 35px rgba(240, 98, 146, 0.2);
  overflow: hidden;
  position: relative;
  border: 2px solid var(--primary);
}

.auth-card::before {
  content: '';
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  background: linear-gradient(45deg, var(--primary), var(--secondary), var(--accent));
  z-index: -1;
  border-radius: 25px;
  opacity: 0.5;
}

.auth-header {
  text-align: center;
  padding: 35px 20px 25px;
  background: linear-gradient(to bottom, var(--light-pink), white);
  border-bottom: 2px dashed var(--primary);
}

.auth-header img {
  width: 140px;
  height: auto;
  margin-bottom: 25px;
  filter: drop-shadow(0 5px 10px rgba(240, 98, 146, 0.3));
  transition: all 0.3s ease;
}

.auth-header img:hover {
  transform: scale(1.05);
}

.auth-header legend {
  font-size: 32px;
  color: var(--dark-pink);
  font-weight: 700;
  font-family: var(--font-cursive);
  margin-bottom: 10px;
  position: relative;
  display: inline-block;
}

.auth-header legend::before,
.auth-header legend::after {
  content: '♥';
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  color: var(--primary);
  font-size: 20px;
}

.auth-header legend::before {
  left: -30px;
}

.auth-header legend::after {
  right: -30px;
}

.auth-header p {
  color: var(--secondary);
  font-size: 16px;
  font-style: italic;
  margin-top: 10px;
}

.auth-form {
  padding: 35px;
}

.form-group {
  margin-bottom: 25px;
}

.form-group label {
  display: block;
  margin-bottom: 10px;
  font-weight: 600;
  color: var(--dark-pink);
  font-size: 16px;
}

.form-control {
  width: 100%;
  padding: 14px 18px;
  border: 2px solid #f8bbd0;
  border-radius: 15px;
  font-size: 16px;
  transition: all 0.3s ease;
  background-color: rgba(252, 228, 236, 0.3);
}

.form-control:focus {
  border-color: var(--accent);
  outline: none;
  box-shadow: 0 0 0 4px rgba(255, 128, 171, 0.2);
  background-color: white;
}

select.form-control {
  appearance: none;
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23ad1457' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M6 9l6 6 6-6'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 18px center;
  background-size: 16px;
}

input[type="radio"] {
  margin-right: 8px;
  accent-color: var(--primary);
  transform: scale(1.2);
}

input[type="radio"] + span {
  margin-right: 20px;
  color: var(--dark-pink);
  font-weight: 500;
}

.btn-auth {
  width: 100%;
  padding: 14px;
  background-color: var(--primary);
  color: white;
  border: none;
  border-radius: 15px;
  font-size: 18px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  z-index: 1;
  margin-top: 10px;
  box-shadow: 0 5px 15px rgba(240, 98, 146, 0.3);
}

.btn-auth::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 100%;
  background-color: var(--accent);
  transition: all 0.4s ease;
  z-index: -1;
  border-radius: 15px;
}

.btn-auth:hover::before {
  width: 100%;
}

.btn-auth:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(240, 98, 146, 0.4);
}

.btn-auth:disabled {
  background-color: #ccc;
  cursor: not-allowed;
  box-shadow: none;
}

.btn-auth:disabled:hover {
  transform: none;
}

.auth-footer {
  text-align: center;
  padding: 20px;
  border-top: 2px dashed #f8bbd0;
  background: linear-gradient(to top, var(--light-pink), white);
}

.auth-footer a {
  color: var(--accent);
  font-weight: 600;
  transition: all 0.3s ease;
}

.auth-footer a:hover {
  color: var(--dark-pink);
  text-decoration: underline;
}

.error-message {
  color: #e91e63;
  font-size: 14px;
  margin-top: 5px;
  font-weight: 500;
}
.register img{
    width: 200px;
    height: 200px;
}
@media (max-width: 576px) {
  .auth-card {
    max-width: 100%;
  }
  
  .auth-header img {
    width: 120px;
  }
  
  .auth-header legend {
    font-size: 28px;
  }
  
  .auth-form {
    padding: 25px;
  }
}
    
