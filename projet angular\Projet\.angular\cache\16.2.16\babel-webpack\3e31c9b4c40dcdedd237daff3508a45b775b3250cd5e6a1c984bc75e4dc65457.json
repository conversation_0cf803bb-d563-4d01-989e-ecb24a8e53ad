{"ast": null, "code": "import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@angular/core';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { GlobalErrorHandler } from './global-error-handler';\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\nimport { FormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\nimport { HomeComponent } from '../home/<USER>';\nimport { ShopComponent } from '../shop/shop.component';\nimport { LoginComponent } from '../login/login.component';\nimport { LognComponent } from '../logn/logn.component';\nimport { HttpClientModule } from '@angular/common/http';\nimport * as i0 from \"@angular/core\";\nexport class AppModule {\n  static {\n    this.ɵfac = function AppModule_Factory(t) {\n      return new (t || AppModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppModule,\n      bootstrap: [AppComponent]\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      providers: [{\n        provide: ErrorHand<PERSON>,\n        useClass: GlobalErrorHandler\n      }],\n      imports: [BrowserModule, AppRoutingModule, FormsModule, RouterModule, HttpClientModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppModule, {\n    declarations: [AppComponent, HomeComponent, ShopComponent, LoginComponent, LognComponent],\n    imports: [BrowserModule, AppRoutingModule, FormsModule, RouterModule, HttpClientModule]\n  });\n})();", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "BrowserModule", "GlobalErrorHandler", "AppRoutingModule", "AppComponent", "FormsModule", "RouterModule", "HomeComponent", "ShopComponent", "LoginComponent", "LognComponent", "HttpClientModule", "AppModule", "bootstrap", "provide", "useClass", "imports", "declarations"], "sources": ["C:\\Users\\<USER>\\Desktop\\projet angular\\Projet\\src\\app\\app.module.ts"], "sourcesContent": ["import { NgMod<PERSON>, <PERSON><PERSON>r<PERSON><PERSON><PERSON> } from '@angular/core';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { GlobalErrorHandler } from './global-error-handler';\n\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\nimport { FormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\nimport { HomeComponent } from '../home/<USER>';\nimport { ShopComponent } from '../shop/shop.component';\nimport { LoginComponent } from '../login/login.component';\nimport { LognComponent } from '../logn/logn.component';\nimport { HttpClientModule } from '@angular/common/http';\n@NgModule({\n  declarations: [\n    AppComponent,\n    HomeComponent,\n    ShopComponent,\n    LoginComponent,\n    LognComponent\n  ],\n  imports: [\n    BrowserModule,\n    AppRoutingModule,\n    FormsModule,\n    RouterModule,\n    HttpClientModule\n  ],\n  providers: [\n    { provide: Error<PERSON><PERSON><PERSON>, useClass: GlobalErrorHandler }\n  ],\n  bootstrap: [AppComponent]\n})\nexport class AppModule { }\n\n"], "mappings": "AAAA,SAAmBA,YAAY,QAAQ,eAAe;AACtD,SAASC,aAAa,QAAQ,2BAA2B;AACzD,SAASC,kBAAkB,QAAQ,wBAAwB;AAE3D,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,0BAA0B;AACzD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,gBAAgB,QAAQ,sBAAsB;;AAqBvD,OAAM,MAAOC,SAAS;;;uBAATA,SAAS;IAAA;EAAA;;;YAATA,SAAS;MAAAC,SAAA,GAFRT,YAAY;IAAA;EAAA;;;iBAHb,CACT;QAAEU,OAAO,EAAEd,YAAY;QAAEe,QAAQ,EAAEb;MAAkB,CAAE,CACxD;MAAAc,OAAA,GARCf,aAAa,EACbE,gBAAgB,EAChBE,WAAW,EACXC,YAAY,EACZK,gBAAgB;IAAA;EAAA;;;2EAOPC,SAAS;IAAAK,YAAA,GAlBlBb,YAAY,EACZG,aAAa,EACbC,aAAa,EACbC,cAAc,EACdC,aAAa;IAAAM,OAAA,GAGbf,aAAa,EACbE,gBAAgB,EAChBE,WAAW,EACXC,YAAY,EACZK,gBAAgB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}