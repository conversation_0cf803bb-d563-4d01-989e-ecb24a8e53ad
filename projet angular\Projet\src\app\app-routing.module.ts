import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ShopComponent } from '../shop/shop.component';
import { HomeComponent } from '../home/<USER>';
import { LognComponent } from '../logn/logn.component';
import { LoginComponent } from '../login/login.component';

const routes: Routes = [
  {path:"home",component:HomeComponent},
  {path:"shop",component:ShopComponent},
  {path:"login",component:LognComponent},
  {path:"signup",component:LoginComponent},
  {path:"**",redirectTo:"home",pathMatch:"full"}
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule]
})
export class AppRoutingModule { }
