{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class AppComponent {\n  constructor() {\n    this.title = 'Projet';\n  }\n  static {\n    this.ɵfac = function AppComponent_Factory(t) {\n      return new (t || AppComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppComponent,\n      selectors: [[\"app-root\"]],\n      decls: 18,\n      vars: 0,\n      consts: [[1, \"container\"], [1, \"content\"], [\"src\", \"assets/women's wear.png\", \"alt\", \"\", \"routerLink\", \"/home\", 1, \"logo\"], [1, \"navmenu\"], [\"routerLink\", \"/home\"], [\"routerLink\", \"/shop\"], [\"routerLink\", \"/login\"], [1, \"end-text\"]],\n      template: function AppComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"nav\")(1, \"div\", 0)(2, \"div\", 1);\n          i0.ɵɵelement(3, \"img\", 2);\n          i0.ɵɵelementStart(4, \"ul\", 3)(5, \"li\", 4);\n          i0.ɵɵtext(6, \"Home\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"li\", 5);\n          i0.ɵɵtext(8, \"Shop\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"ul\")(10, \"li\", 6);\n          i0.ɵɵtext(11, \"Log in\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(12, \"div\", 0);\n          i0.ɵɵelement(13, \"router-outlet\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(14, \"br\");\n          i0.ɵɵelementStart(15, \"div\", 7)(16, \"p\");\n          i0.ɵɵtext(17, \"Copyright \\u00A9 2025 .All Rights Reserved.Designed By ... \");\n          i0.ɵɵelementEnd()();\n        }\n      },\n      dependencies: [i1.RouterOutlet, i1.RouterLink],\n      styles: [\"nav[_ngcontent-%COMP%] {\\n  background-color: var(--white);\\n  box-shadow: var(--shadow);\\n  position: sticky;\\n  top: 0;\\n  z-index: 1000;\\n  width: 100%;\\n  border-bottom: 3px solid var(--primary);\\n}\\n\\nnav[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 15px 0;\\n}\\n\\nnav[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 130px;\\n  transition: var(--transition);\\n  filter: drop-shadow(0 2px 5px rgba(240, 98, 146, 0.3));\\n}\\n\\nnav[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n}\\n\\nnav[_ngcontent-%COMP%]   .navmenu[_ngcontent-%COMP%] {\\n  display: flex;\\n  list-style: none;\\n  margin: 0;\\n  padding: 0;\\n}\\n\\nnav[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  margin: 0 20px;\\n  font-weight: 500;\\n  position: relative;\\n  cursor: pointer;\\n  transition: var(--transition);\\n  font-size: 16px;\\n  letter-spacing: 0.5px;\\n  color: var(--dark-pink);\\n}\\n\\nnav[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]::after {\\n  content: '';\\n  position: absolute;\\n  bottom: -5px;\\n  left: 0;\\n  width: 0;\\n  height: 2px;\\n  background: var(--primary);\\n  transition: var(--transition);\\n}\\n\\nnav[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:hover {\\n  color: var(--accent);\\n}\\n\\nnav[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:hover::after {\\n  width: 100%;\\n}\\n\\nnav[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:nth-child(1)::before {\\n  content: '\\u2665';\\n  position: absolute;\\n  left: -15px;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  color: var(--primary);\\n  opacity: 0;\\n  transition: var(--transition);\\n}\\n\\nnav[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:nth-child(1):hover::before {\\n  opacity: 1;\\n  left: -20px;\\n}\\n\\nnav[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:nth-child(2)::before {\\n  content: '\\u2665';\\n  position: absolute;\\n  left: -15px;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  color: var(--secondary);\\n  opacity: 0;\\n  transition: var(--transition);\\n}\\n\\nnav[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:nth-child(2):hover::before {\\n  opacity: 1;\\n  left: -20px;\\n}\\n\\nnav[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]:last-child   li[_ngcontent-%COMP%] {\\n  background-color: var(--primary);\\n  padding: 8px 20px;\\n  border-radius: 30px;\\n  color: var(--white);\\n  transition: all 0.3s ease;\\n}\\n\\nnav[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]:last-child   li[_ngcontent-%COMP%]:hover {\\n  background-color: var(--accent);\\n  transform: translateY(-3px);\\n  box-shadow: 0 5px 15px rgba(240, 98, 146, 0.3);\\n}\\n\\nnav[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]:last-child   li[_ngcontent-%COMP%]::after {\\n  display: none;\\n}\\n\\n.end-text[_ngcontent-%COMP%] {\\n  background-color: var(--white);\\n  text-align: center;\\n  padding: 20px;\\n  font-size: 14px;\\n  color: var(--dark-pink);\\n  border-top: 2px dashed var(--primary);\\n}\\n\\n@media(max-width: 768px) {\\n  nav[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    padding: 15px 0;\\n  }\\n  \\n  nav[_ngcontent-%COMP%]   .navmenu[_ngcontent-%COMP%] {\\n    margin-top: 15px;\\n  }\\n  \\n  nav[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n    margin: 0 10px;\\n  }\\n  \\n  nav[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]:last-child {\\n    margin-top: 15px;\\n  }\\n}\\n\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["AppComponent", "constructor", "title", "selectors", "decls", "vars", "consts", "template", "AppComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd"], "sources": ["C:\\Users\\<USER>\\Desktop\\projet angular\\Projet\\src\\app\\app.component.ts", "C:\\Users\\<USER>\\Desktop\\projet angular\\Projet\\src\\app\\app.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-root',\n  templateUrl: './app.component.html',\n  styleUrls: ['./app.component.css']\n})\nexport class AppComponent {\n  title = 'Projet';\n}\n", "<nav>\n    <div class=\"container\">\n        <div class=\"content\">\n            <img class=\"logo\" src=\"assets/women's wear.png\" alt=\"\" routerLink=\"/home\">\n            <ul class=\"navmenu\">\n                <li routerLink=\"/home\">Home</li>\n                <li routerLink=\"/shop\">Shop</li>\n            </ul>\n            <ul> \n                <li routerLink=\"/login\">Log in</li>\n            </ul>\n        </div>\n    </div>\n</nav>\n<div class=\"container\"><router-outlet></router-outlet>\n</div>\n<br>\n<div class=\"end-text\">\n    <p>Copyright © 2025 .All Rights Reserved.Designed By ... </p>\n</div>\n"], "mappings": ";;AAOA,OAAM,MAAOA,YAAY;EALzBC,YAAA;IAME,KAAAC,KAAK,GAAG,QAAQ;;;;uBADLF,YAAY;IAAA;EAAA;;;YAAZA,YAAY;MAAAG,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCPzBE,EAAA,CAAAC,cAAA,UAAK;UAGOD,EAAA,CAAAE,SAAA,aAA0E;UAC1EF,EAAA,CAAAC,cAAA,YAAoB;UACOD,EAAA,CAAAG,MAAA,WAAI;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAChCJ,EAAA,CAAAC,cAAA,YAAuB;UAAAD,EAAA,CAAAG,MAAA,WAAI;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAEpCJ,EAAA,CAAAC,cAAA,SAAI;UACwBD,EAAA,CAAAG,MAAA,cAAM;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAKnDJ,EAAA,CAAAC,cAAA,cAAuB;UAAAD,EAAA,CAAAE,SAAA,qBAA+B;UACtDF,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAE,SAAA,UAAI;UACJF,EAAA,CAAAC,cAAA,cAAsB;UACfD,EAAA,CAAAG,MAAA,mEAAsD;UAAAH,EAAA,CAAAI,YAAA,EAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}