{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nexport let HomeComponent = class HomeComponent {\n  constructor(router) {\n    this.router = router;\n    this.selectedCategory = ''; // Stores the selected category\n    this.items = [{\n      id: 1,\n      title: 'Item 1',\n      description: 'Description 1',\n      price: 50,\n      category: '1',\n      imgSrc: 'assets/4.jpg'\n    }, {\n      id: 2,\n      title: 'Item 2',\n      description: 'Description 2',\n      price: 60,\n      category: '2',\n      imgSrc: 'assets/2.jpg'\n    }, {\n      id: 3,\n      title: 'Item 3',\n      description: 'Description 3',\n      price: 70,\n      category: '1',\n      imgSrc: 'assets/7.jpg'\n    }, {\n      id: 4,\n      title: 'Item 4',\n      description: 'Description 4',\n      price: 80,\n      category: '3',\n      imgSrc: 'assets/8.jpg'\n    }];\n  }\n  navigateToShop() {\n    this.router.navigate(['/shop']);\n  }\n};\nHomeComponent = __decorate([Component({\n  selector: 'app-home',\n  templateUrl: './home.component.html',\n  styleUrls: ['./home.component.css']\n})], HomeComponent);", "map": {"version": 3, "names": ["Component", "HomeComponent", "constructor", "router", "selectedCate<PERSON><PERSON>", "items", "id", "title", "description", "price", "category", "imgSrc", "navigateToShop", "navigate", "__decorate", "selector", "templateUrl", "styleUrls"], "sources": ["C:\\Users\\<USER>\\Desktop\\projet angular\\Projet\\src\\home\\home.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { Router } from '@angular/router';\n\n@Component({\n  selector: 'app-home',\n  templateUrl: './home.component.html',\n  styleUrls: ['./home.component.css']\n})\nexport class HomeComponent {\n  constructor(private router:Router){}\n  selectedCategory: string = ''; // Stores the selected category\n  items = [\n    { id: 1, title: 'Item 1', description: 'Description 1', price: 50, category: '1', imgSrc:'assets/4.jpg'},\n    { id: 2, title: 'Item 2', description: 'Description 2', price: 60, category: '2', imgSrc:'assets/2.jpg'  },\n    { id: 3, title: 'Item 3', description: 'Description 3', price: 70, category: '1', imgSrc:'assets/7.jpg'  },\n    { id: 4, title: 'Item 4', description: 'Description 4', price: 80, category: '3', imgSrc:'assets/8.jpg'  }\n  ];\n  navigateToShop() {\n    this.router.navigate(['/shop']);\n}\n}\n"], "mappings": ";AAAA,SAASA,SAAS,QAAQ,eAAe;AAQlC,WAAMC,aAAa,GAAnB,MAAMA,aAAa;EACxBC,YAAoBC,MAAa;IAAb,KAAAA,MAAM,GAANA,MAAM;IAC1B,KAAAC,gBAAgB,GAAW,EAAE,CAAC,CAAC;IAC/B,KAAAC,KAAK,GAAG,CACN;MAAEC,EAAE,EAAE,CAAC;MAAEC,KAAK,EAAE,QAAQ;MAAEC,WAAW,EAAE,eAAe;MAAEC,KAAK,EAAE,EAAE;MAAEC,QAAQ,EAAE,GAAG;MAAEC,MAAM,EAAC;IAAc,CAAC,EACxG;MAAEL,EAAE,EAAE,CAAC;MAAEC,KAAK,EAAE,QAAQ;MAAEC,WAAW,EAAE,eAAe;MAAEC,KAAK,EAAE,EAAE;MAAEC,QAAQ,EAAE,GAAG;MAAEC,MAAM,EAAC;IAAc,CAAG,EAC1G;MAAEL,EAAE,EAAE,CAAC;MAAEC,KAAK,EAAE,QAAQ;MAAEC,WAAW,EAAE,eAAe;MAAEC,KAAK,EAAE,EAAE;MAAEC,QAAQ,EAAE,GAAG;MAAEC,MAAM,EAAC;IAAc,CAAG,EAC1G;MAAEL,EAAE,EAAE,CAAC;MAAEC,KAAK,EAAE,QAAQ;MAAEC,WAAW,EAAE,eAAe;MAAEC,KAAK,EAAE,EAAE;MAAEC,QAAQ,EAAE,GAAG;MAAEC,MAAM,EAAC;IAAc,CAAG,CAC3G;EAPkC;EAQnCC,cAAcA,CAAA;IACZ,IAAI,CAACT,MAAM,CAACU,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;EACnC;CACC;AAZYZ,aAAa,GAAAa,UAAA,EALzBd,SAAS,CAAC;EACTe,QAAQ,EAAE,UAAU;EACpBC,WAAW,EAAE,uBAAuB;EACpCC,SAAS,EAAE,CAAC,sBAAsB;CACnC,CAAC,C,EACWhB,aAAa,CAYzB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}