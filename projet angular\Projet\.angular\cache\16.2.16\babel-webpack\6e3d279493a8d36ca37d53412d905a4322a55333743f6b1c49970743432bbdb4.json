{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class HomeComponent {\n  constructor(router) {\n    this.router = router;\n  }\n  navigateToShop() {\n    this.router.navigate(['/shop']);\n  }\n  static {\n    this.ɵfac = function HomeComponent_Factory(t) {\n      return new (t || HomeComponent)(i0.ɵɵdirectiveInject(i1.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HomeComponent,\n      selectors: [[\"app-home\"]],\n      decls: 151,\n      vars: 0,\n      consts: [[1, \"section\"], [3, \"click\"], [1, \"trending-product\"], [1, \"center-text\"], [1, \"Products\"], [1, \"row\"], [\"src\", \"assets/photo1.jpg\"], [1, \"product-text\"], [1, \"heart-icon\"], [1, \"bx\", \"bx-heart\"], [1, \"ratting\"], [1, \"bx\", \"bxs-star\"], [1, \"bx\", \"bxs-star-half\"], [1, \"price\"], [\"src\", \"assets/2.jpg\"], [\"src\", \"assets/3.jpg\"], [\"src\", \"assets/4.jpg\"], [\"src\", \"assets/5.jpg\"], [\"src\", \"assets/6.jpg\"], [\"src\", \"assets/7.jpg\"], [\"src\", \"assets/8.jpg\"]],\n      template: function HomeComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"h5\");\n          i0.ɵɵtext(2, \"winter collection\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"h1\");\n          i0.ɵɵtext(4, \"New Winter\");\n          i0.ɵɵelement(5, \"br\");\n          i0.ɵɵtext(6, \"collection 2024\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"h6\");\n          i0.ɵɵtext(8, \"There's nothing like trend\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"p\", 1);\n          i0.ɵɵlistener(\"click\", function HomeComponent_Template_p_click_9_listener() {\n            return ctx.navigateToShop();\n          });\n          i0.ɵɵtext(10, \"Shop now\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(11, \"div\", 2)(12, \"div\", 3)(13, \"h2\");\n          i0.ɵɵtext(14, \"Our tranding \");\n          i0.ɵɵelementStart(15, \"span\");\n          i0.ɵɵtext(16, \"Products\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(17, \"div\", 4)(18, \"div\", 5);\n          i0.ɵɵelement(19, \"img\", 6);\n          i0.ɵɵelementStart(20, \"div\", 7)(21, \"h5\");\n          i0.ɵɵtext(22, \"Sale\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(23, \"div\", 8);\n          i0.ɵɵelement(24, \"i\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"div\", 10);\n          i0.ɵɵelement(26, \"i\", 11)(27, \"i\", 11)(28, \"i\", 11)(29, \"i\", 11)(30, \"i\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"div\", 13)(32, \"h4\");\n          i0.ɵɵtext(33, \"Half Running Set\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"p\");\n          i0.ɵɵtext(35, \"50dt-80dt\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(36, \"div\", 4)(37, \"div\", 5);\n          i0.ɵɵelement(38, \"img\", 14);\n          i0.ɵɵelementStart(39, \"div\", 7)(40, \"h5\");\n          i0.ɵɵtext(41, \"New\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(42, \"div\", 8);\n          i0.ɵɵelement(43, \"i\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"div\", 10);\n          i0.ɵɵelement(45, \"i\", 11)(46, \"i\", 11)(47, \"i\", 11)(48, \"i\", 11)(49, \"i\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(50, \"div\", 13)(51, \"h4\");\n          i0.ɵɵtext(52, \"Half Running Suit\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(53, \"p\");\n          i0.ɵɵtext(54, \"50dt-80dt\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(55, \"div\", 4)(56, \"div\", 5);\n          i0.ɵɵelement(57, \"img\", 15);\n          i0.ɵɵelementStart(58, \"div\", 8);\n          i0.ɵɵelement(59, \"i\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(60, \"div\", 10);\n          i0.ɵɵelement(61, \"i\", 11)(62, \"i\", 11)(63, \"i\", 11)(64, \"i\", 11)(65, \"i\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(66, \"div\", 13)(67, \"h4\");\n          i0.ɵɵtext(68, \"description\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(69, \"p\");\n          i0.ɵɵtext(70, \"50dt-80dt\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(71, \"div\", 4)(72, \"div\", 5);\n          i0.ɵɵelement(73, \"img\", 16);\n          i0.ɵɵelementStart(74, \"div\", 8);\n          i0.ɵɵelement(75, \"i\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(76, \"div\", 10);\n          i0.ɵɵelement(77, \"i\", 11)(78, \"i\", 11)(79, \"i\", 11)(80, \"i\", 11)(81, \"i\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(82, \"div\", 13)(83, \"h4\");\n          i0.ɵɵtext(84, \"description\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(85, \"p\");\n          i0.ɵɵtext(86, \"$99 - $129\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(87, \"div\", 4)(88, \"div\", 5);\n          i0.ɵɵelement(89, \"img\", 17);\n          i0.ɵɵelementStart(90, \"div\", 8);\n          i0.ɵɵelement(91, \"i\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(92, \"div\", 10);\n          i0.ɵɵelement(93, \"i\", 11)(94, \"i\", 11)(95, \"i\", 11)(96, \"i\", 11)(97, \"i\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(98, \"div\", 13)(99, \"h4\");\n          i0.ɵɵtext(100, \"description\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(101, \"p\");\n          i0.ɵɵtext(102, \"50dt-80dt\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(103, \"div\", 4)(104, \"div\", 5);\n          i0.ɵɵelement(105, \"img\", 18);\n          i0.ɵɵelementStart(106, \"div\", 8);\n          i0.ɵɵelement(107, \"i\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(108, \"div\", 10);\n          i0.ɵɵelement(109, \"i\", 11)(110, \"i\", 11)(111, \"i\", 11)(112, \"i\", 11)(113, \"i\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(114, \"div\", 13)(115, \"h4\");\n          i0.ɵɵtext(116, \"description\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(117, \"p\");\n          i0.ɵɵtext(118, \"50dt-80dt\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(119, \"div\", 4)(120, \"div\", 5);\n          i0.ɵɵelement(121, \"img\", 19);\n          i0.ɵɵelementStart(122, \"div\", 8);\n          i0.ɵɵelement(123, \"i\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(124, \"div\", 10);\n          i0.ɵɵelement(125, \"i\", 11)(126, \"i\", 11)(127, \"i\", 11)(128, \"i\", 11)(129, \"i\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(130, \"div\", 13)(131, \"h4\");\n          i0.ɵɵtext(132, \"description\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(133, \"p\");\n          i0.ɵɵtext(134, \"50dt-80dt\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(135, \"div\", 4)(136, \"div\", 5);\n          i0.ɵɵelement(137, \"img\", 20);\n          i0.ɵɵelementStart(138, \"div\", 8);\n          i0.ɵɵelement(139, \"i\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(140, \"div\", 10);\n          i0.ɵɵelement(141, \"i\", 11)(142, \"i\", 11)(143, \"i\", 11)(144, \"i\", 11)(145, \"i\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(146, \"div\", 13)(147, \"h4\");\n          i0.ɵɵtext(148, \"description\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(149, \"p\");\n          i0.ɵɵtext(150, \"50dt-80dt\");\n          i0.ɵɵelementEnd()()()()();\n        }\n      },\n      styles: [\"*[_ngcontent-%COMP%]{\\n    margin: 0;\\n    padding: 0;\\n    box-sizing: border-box;\\n    scroll-behavior: smooth;\\n    font-family: 'sans-serif';\\n    list-style: none;\\n    text-decoration: none;\\n}\\n.section[_ngcontent-%COMP%]{\\n    padding: 5% 10%;\\n    width: 100%;\\n    height: 100vh;\\n    background-image:url('banner-3.png');\\n    background-position: center;\\n    background-size:cover ;\\n    display: grid;\\n    grid-template-columns: repeat(1,1fr);\\n    align-items: center;\\n    h5{\\n        color: #a4936d;\\n        font-size: 16px;\\n        text-transform: capitalize;\\n        font-weight: 500;\\n    }\\n    h1{\\n        color: #000;\\n        font-size: 65px;\\n        text-transform: capitalize;\\n        line-height: 1.1;\\n        font-weight: 600;\\n        margin: 6px 0 10px;\\n    }\\n    h6{\\n        color: #333c65;\\n        font-size: 20px;\\n        font-style: italic;\\n        margin-bottom: 20px;\\n    }\\n    p{\\n        display: inline-block;\\n        color: #111;\\n        font-size: 16px;\\n        font-weight: 500;\\n        text-transform: capitalize;\\n        border: 2px solid #111;\\n        width: 130px;\\n        height: 50px;\\n        padding: 12px 25px;\\n        transition: all .42s ease;\\n        cursor: pointer;\\n    }\\n    p:hover{\\n        background-color: #000;\\n        color: white;\\n    }\\n}\\n.center-text[_ngcontent-%COMP%]{\\n    h2{\\n        color:#111;\\n        font-size: 28px;\\n        text-transform: capitalize;\\n        text-align: center;\\n        margin-bottom: 30px;\\n    }\\n    span{\\n        color: #a4936d;\\n\\n    }\\n}\\n.grid-Products[_ngcontent-%COMP%] {\\n    display: grid;\\n    grid-template-columns: repeat(4, 1fr);\\n    gap: 20px; \\n\\n    justify-content: space-between; \\n\\n}\\n\\n.grid-row[_ngcontent-%COMP%] {\\n  text-align: center; \\n\\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1); \\n\\n  padding: 10px; \\n\\n  border: 1px solid #ccc; \\n\\n}\\n\\n.grid-row[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    width: 100%;\\n    height: auto;\\n    border-radius: 5px; \\n\\n}\\n\\n.row[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]:hover {\\n    transform: scale(0.9); \\n\\n}\\n.product-text[_ngcontent-%COMP%]{\\n    h5{\\n        position: absolute;\\n        top: 13px;\\n        left: 13px;\\n        color: #fff;\\n        font-size: 12px;\\n        font-weight: 500;\\n    }\\n}\\n\\n        \\n\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["HomeComponent", "constructor", "router", "navigateToShop", "navigate", "i0", "ɵɵdirectiveInject", "i1", "Router", "selectors", "decls", "vars", "consts", "template", "HomeComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵlistener", "HomeComponent_Template_p_click_9_listener"], "sources": ["C:\\Users\\<USER>\\Desktop\\projet angular\\Projet\\src\\home\\home.component.ts", "C:\\Users\\<USER>\\Desktop\\projet angular\\Projet\\src\\home\\home.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { Router } from '@angular/router';\n\n@Component({\n  selector: 'app-home',\n  templateUrl: './home.component.html',\n  styleUrls: ['./home.component.css']\n})\nexport class HomeComponent {\n  constructor(private router:Router){}\n  navigateToShop() {\n    this.router.navigate(['/shop']);\n}\n}\n", "<div class=\"section\"><h5>winter collection</h5>\n    <h1>New Winter<br>collection 2024</h1>\n    <h6 >There's nothing like trend</h6>\n    <p (click)=\"navigateToShop()\">Shop now</p></div>\n    <div class=\"trending-product\">\n        <div class=\"center-text\">\n            <h2>Our tranding <span>Products</span></h2>\n        </div>\n        <div class=\"Products\">\n            <div class=\"row\">\n                <img src=\"assets/photo1.jpg\" >\n                <div class=\"product-text\">\n                    <h5>Sale</h5>\n                </div>\n                <div class=\"heart-icon\">\n                    <i class='bx bx-heart'></i>\n                </div>\n                <div class=\"ratting\">\n                    <i class='bx bxs-star'></i>\n                    <i class='bx bxs-star'></i>\n                    <i class='bx bxs-star'></i>\n                    <i class='bx bxs-star'></i>\n                    <i class='bx bxs-star-half'></i>\n                </div>\n                <div class=\"price\">\n                    <h4>Half Running Set</h4>\n                    <p>50dt-80dt</p>\n                </div>\n            </div>\n        </div>\n        <div class=\"Products\">\n            <div class=\"row\">\n                <img src=\"assets/2.jpg\">\n                <div class=\"product-text\">\n                    <h5>New</h5>\n                </div>\n                <div class=\"heart-icon\">\n                    <i class='bx bx-heart'></i>\n                </div>\n                <div class=\"ratting\">\n                    <i class='bx bxs-star'></i>\n                    <i class='bx bxs-star'></i>\n                    <i class='bx bxs-star'></i>\n                    <i class='bx bxs-star'></i>\n                    <i class='bx bxs-star-half'></i>\n                </div>\n                <div class=\"price\">\n                    <h4>Half Running Suit</h4>\n                    <p>50dt-80dt</p>\n                </div>\n            </div>\n        </div>\n        <div class=\"Products\">\n            <div class=\"row\">\n                <img src=\"assets/3.jpg\">\n                <div class=\"heart-icon\">\n                    <i class='bx bx-heart'></i>\n                </div>\n                <div class=\"ratting\">\n                    <i class='bx bxs-star'></i>\n                    <i class='bx bxs-star'></i>\n                    <i class='bx bxs-star'></i>\n                    <i class='bx bxs-star'></i>\n                    <i class='bx bxs-star-half'></i>\n                </div>\n                <div class=\"price\">\n                    <h4>description</h4>\n                    <p>50dt-80dt</p>\n                </div>\n            </div>\n        </div>\n        <div class=\"Products\">\n            <div class=\"row\">\n                <img src=\"assets/4.jpg\">\n                <div class=\"heart-icon\">\n                    <i class='bx bx-heart'></i>\n                </div>\n                <div class=\"ratting\">\n                    <i class='bx bxs-star'></i>\n                    <i class='bx bxs-star'></i>\n                    <i class='bx bxs-star'></i>\n                    <i class='bx bxs-star'></i>\n                    <i class='bx bxs-star-half'></i>\n                </div>\n                <div class=\"price\">\n                    <h4>description</h4>\n                    <p>$99 - $129</p>\n                </div>\n            </div>\n        </div>\n        <div class=\"Products\">\n            <div class=\"row\">\n                <img src=\"assets/5.jpg\">\n                <div class=\"heart-icon\">\n                    <i class='bx bx-heart'></i>\n                </div>\n                <div class=\"ratting\">\n                    <i class='bx bxs-star'></i>\n                    <i class='bx bxs-star'></i>\n                    <i class='bx bxs-star'></i>\n                    <i class='bx bxs-star'></i>\n                    <i class='bx bxs-star-half'></i>\n                </div>\n                <div class=\"price\">\n                    <h4>description</h4>\n                    <p>50dt-80dt</p>\n                </div>\n            </div>\n        </div>\n        <div class=\"Products\">\n            <div class=\"row\">\n                <img src=\"assets/6.jpg\">\n                <div class=\"heart-icon\">\n                    <i class='bx bx-heart'></i>\n                </div>\n                <div class=\"ratting\">\n                    <i class='bx bxs-star'></i>\n                    <i class='bx bxs-star'></i>\n                    <i class='bx bxs-star'></i>\n                    <i class='bx bxs-star'></i>\n                    <i class='bx bxs-star-half'></i>\n                </div>\n                <div class=\"price\">\n                    <h4>description</h4>\n                    <p>50dt-80dt</p>\n                </div>\n            </div>\n        </div>\n        <div class=\"Products\">\n            <div class=\"row\">\n                <img src=\"assets/7.jpg\">\n                <div class=\"heart-icon\">\n                    <i class='bx bx-heart'></i>\n                </div>\n                <div class=\"ratting\">\n                    <i class='bx bxs-star'></i>\n                    <i class='bx bxs-star'></i>\n                    <i class='bx bxs-star'></i>\n                    <i class='bx bxs-star'></i>\n                    <i class='bx bxs-star-half'></i>\n                </div>\n                <div class=\"price\">\n                    <h4>description</h4>\n                    <p>50dt-80dt</p>\n                </div>\n            </div>\n        </div>\n        <div class=\"Products\">\n            <div class=\"row\">\n                <img src=\"assets/8.jpg\">\n                <div class=\"heart-icon\">\n                    <i class='bx bx-heart'></i>\n                </div>\n                <div class=\"ratting\">\n                    <i class='bx bxs-star'></i>\n                    <i class='bx bxs-star'></i>\n                    <i class='bx bxs-star'></i>\n                    <i class='bx bxs-star'></i>\n                    <i class='bx bxs-star-half'></i>\n                </div>\n                <div class=\"price\">\n                    <h4>description</h4>\n                    <p>50dt-80dt</p>\n                </div>\n            </div>\n        </div>\n    </div>\n    \n"], "mappings": ";;AAQA,OAAM,MAAOA,aAAa;EACxBC,YAAoBC,MAAa;IAAb,KAAAA,MAAM,GAANA,MAAM;EAAS;EACnCC,cAAcA,CAAA;IACZ,IAAI,CAACD,MAAM,CAACE,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;EACnC;;;uBAJaJ,aAAa,EAAAK,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAbR,aAAa;MAAAS,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCR1BV,EAAA,CAAAY,cAAA,aAAqB;UAAIZ,EAAA,CAAAa,MAAA,wBAAiB;UAAAb,EAAA,CAAAc,YAAA,EAAK;UAC3Cd,EAAA,CAAAY,cAAA,SAAI;UAAAZ,EAAA,CAAAa,MAAA,iBAAU;UAAAb,EAAA,CAAAe,SAAA,SAAI;UAAAf,EAAA,CAAAa,MAAA,sBAAe;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACtCd,EAAA,CAAAY,cAAA,SAAK;UAAAZ,EAAA,CAAAa,MAAA,iCAA0B;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACpCd,EAAA,CAAAY,cAAA,WAA8B;UAA3BZ,EAAA,CAAAgB,UAAA,mBAAAC,0CAAA;YAAA,OAASN,GAAA,CAAAb,cAAA,EAAgB;UAAA,EAAC;UAACE,EAAA,CAAAa,MAAA,gBAAQ;UAAAb,EAAA,CAAAc,YAAA,EAAI;UAC1Cd,EAAA,CAAAY,cAAA,cAA8B;UAElBZ,EAAA,CAAAa,MAAA,qBAAa;UAAAb,EAAA,CAAAY,cAAA,YAAM;UAAAZ,EAAA,CAAAa,MAAA,gBAAQ;UAAAb,EAAA,CAAAc,YAAA,EAAO;UAE1Cd,EAAA,CAAAY,cAAA,cAAsB;UAEdZ,EAAA,CAAAe,SAAA,cAA8B;UAC9Bf,EAAA,CAAAY,cAAA,cAA0B;UAClBZ,EAAA,CAAAa,MAAA,YAAI;UAAAb,EAAA,CAAAc,YAAA,EAAK;UAEjBd,EAAA,CAAAY,cAAA,cAAwB;UACpBZ,EAAA,CAAAe,SAAA,YAA2B;UAC/Bf,EAAA,CAAAc,YAAA,EAAM;UACNd,EAAA,CAAAY,cAAA,eAAqB;UACjBZ,EAAA,CAAAe,SAAA,aAA2B;UAK/Bf,EAAA,CAAAc,YAAA,EAAM;UACNd,EAAA,CAAAY,cAAA,eAAmB;UACXZ,EAAA,CAAAa,MAAA,wBAAgB;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACzBd,EAAA,CAAAY,cAAA,SAAG;UAAAZ,EAAA,CAAAa,MAAA,iBAAS;UAAAb,EAAA,CAAAc,YAAA,EAAI;UAI5Bd,EAAA,CAAAY,cAAA,cAAsB;UAEdZ,EAAA,CAAAe,SAAA,eAAwB;UACxBf,EAAA,CAAAY,cAAA,cAA0B;UAClBZ,EAAA,CAAAa,MAAA,WAAG;UAAAb,EAAA,CAAAc,YAAA,EAAK;UAEhBd,EAAA,CAAAY,cAAA,cAAwB;UACpBZ,EAAA,CAAAe,SAAA,YAA2B;UAC/Bf,EAAA,CAAAc,YAAA,EAAM;UACNd,EAAA,CAAAY,cAAA,eAAqB;UACjBZ,EAAA,CAAAe,SAAA,aAA2B;UAK/Bf,EAAA,CAAAc,YAAA,EAAM;UACNd,EAAA,CAAAY,cAAA,eAAmB;UACXZ,EAAA,CAAAa,MAAA,yBAAiB;UAAAb,EAAA,CAAAc,YAAA,EAAK;UAC1Bd,EAAA,CAAAY,cAAA,SAAG;UAAAZ,EAAA,CAAAa,MAAA,iBAAS;UAAAb,EAAA,CAAAc,YAAA,EAAI;UAI5Bd,EAAA,CAAAY,cAAA,cAAsB;UAEdZ,EAAA,CAAAe,SAAA,eAAwB;UACxBf,EAAA,CAAAY,cAAA,cAAwB;UACpBZ,EAAA,CAAAe,SAAA,YAA2B;UAC/Bf,EAAA,CAAAc,YAAA,EAAM;UACNd,EAAA,CAAAY,cAAA,eAAqB;UACjBZ,EAAA,CAAAe,SAAA,aAA2B;UAK/Bf,EAAA,CAAAc,YAAA,EAAM;UACNd,EAAA,CAAAY,cAAA,eAAmB;UACXZ,EAAA,CAAAa,MAAA,mBAAW;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACpBd,EAAA,CAAAY,cAAA,SAAG;UAAAZ,EAAA,CAAAa,MAAA,iBAAS;UAAAb,EAAA,CAAAc,YAAA,EAAI;UAI5Bd,EAAA,CAAAY,cAAA,cAAsB;UAEdZ,EAAA,CAAAe,SAAA,eAAwB;UACxBf,EAAA,CAAAY,cAAA,cAAwB;UACpBZ,EAAA,CAAAe,SAAA,YAA2B;UAC/Bf,EAAA,CAAAc,YAAA,EAAM;UACNd,EAAA,CAAAY,cAAA,eAAqB;UACjBZ,EAAA,CAAAe,SAAA,aAA2B;UAK/Bf,EAAA,CAAAc,YAAA,EAAM;UACNd,EAAA,CAAAY,cAAA,eAAmB;UACXZ,EAAA,CAAAa,MAAA,mBAAW;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACpBd,EAAA,CAAAY,cAAA,SAAG;UAAAZ,EAAA,CAAAa,MAAA,kBAAU;UAAAb,EAAA,CAAAc,YAAA,EAAI;UAI7Bd,EAAA,CAAAY,cAAA,cAAsB;UAEdZ,EAAA,CAAAe,SAAA,eAAwB;UACxBf,EAAA,CAAAY,cAAA,cAAwB;UACpBZ,EAAA,CAAAe,SAAA,YAA2B;UAC/Bf,EAAA,CAAAc,YAAA,EAAM;UACNd,EAAA,CAAAY,cAAA,eAAqB;UACjBZ,EAAA,CAAAe,SAAA,aAA2B;UAK/Bf,EAAA,CAAAc,YAAA,EAAM;UACNd,EAAA,CAAAY,cAAA,eAAmB;UACXZ,EAAA,CAAAa,MAAA,oBAAW;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACpBd,EAAA,CAAAY,cAAA,UAAG;UAAAZ,EAAA,CAAAa,MAAA,kBAAS;UAAAb,EAAA,CAAAc,YAAA,EAAI;UAI5Bd,EAAA,CAAAY,cAAA,eAAsB;UAEdZ,EAAA,CAAAe,SAAA,gBAAwB;UACxBf,EAAA,CAAAY,cAAA,eAAwB;UACpBZ,EAAA,CAAAe,SAAA,aAA2B;UAC/Bf,EAAA,CAAAc,YAAA,EAAM;UACNd,EAAA,CAAAY,cAAA,gBAAqB;UACjBZ,EAAA,CAAAe,SAAA,cAA2B;UAK/Bf,EAAA,CAAAc,YAAA,EAAM;UACNd,EAAA,CAAAY,cAAA,gBAAmB;UACXZ,EAAA,CAAAa,MAAA,oBAAW;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACpBd,EAAA,CAAAY,cAAA,UAAG;UAAAZ,EAAA,CAAAa,MAAA,kBAAS;UAAAb,EAAA,CAAAc,YAAA,EAAI;UAI5Bd,EAAA,CAAAY,cAAA,eAAsB;UAEdZ,EAAA,CAAAe,SAAA,gBAAwB;UACxBf,EAAA,CAAAY,cAAA,eAAwB;UACpBZ,EAAA,CAAAe,SAAA,aAA2B;UAC/Bf,EAAA,CAAAc,YAAA,EAAM;UACNd,EAAA,CAAAY,cAAA,gBAAqB;UACjBZ,EAAA,CAAAe,SAAA,cAA2B;UAK/Bf,EAAA,CAAAc,YAAA,EAAM;UACNd,EAAA,CAAAY,cAAA,gBAAmB;UACXZ,EAAA,CAAAa,MAAA,oBAAW;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACpBd,EAAA,CAAAY,cAAA,UAAG;UAAAZ,EAAA,CAAAa,MAAA,kBAAS;UAAAb,EAAA,CAAAc,YAAA,EAAI;UAI5Bd,EAAA,CAAAY,cAAA,eAAsB;UAEdZ,EAAA,CAAAe,SAAA,gBAAwB;UACxBf,EAAA,CAAAY,cAAA,eAAwB;UACpBZ,EAAA,CAAAe,SAAA,aAA2B;UAC/Bf,EAAA,CAAAc,YAAA,EAAM;UACNd,EAAA,CAAAY,cAAA,gBAAqB;UACjBZ,EAAA,CAAAe,SAAA,cAA2B;UAK/Bf,EAAA,CAAAc,YAAA,EAAM;UACNd,EAAA,CAAAY,cAAA,gBAAmB;UACXZ,EAAA,CAAAa,MAAA,oBAAW;UAAAb,EAAA,CAAAc,YAAA,EAAK;UACpBd,EAAA,CAAAY,cAAA,UAAG;UAAAZ,EAAA,CAAAa,MAAA,kBAAS;UAAAb,EAAA,CAAAc,YAAA,EAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}