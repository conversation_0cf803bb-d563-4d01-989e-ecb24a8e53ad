{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.fromCodePoint = String.fromCodePoint || function (astralCodePoint) {\n  return String.fromCharCode(Math.floor((astralCodePoint - 65536) / 1024) + 55296, (astralCodePoint - 65536) % 1024 + 56320);\n};\nexports.getCodePoint = String.prototype.codePointAt ? function (input, position) {\n  return input.codePointAt(position);\n} : function (input, position) {\n  return (input.charCodeAt(position) - 55296) * 1024 + input.charCodeAt(position + 1) - 56320 + 65536;\n};\nexports.highSurrogateFrom = 55296;\nexports.highSurrogateTo = 56319;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "fromCodePoint", "String", "astralCodePoint", "fromCharCode", "Math", "floor", "getCodePoint", "prototype", "codePointAt", "input", "position", "charCodeAt", "highSurrogateFrom", "highSurrogateTo"], "sources": ["C:/Users/<USER>/Desktop/projet angular/Projet/node_modules/html-entities/lib/surrogate-pairs.js"], "sourcesContent": ["\"use strict\";Object.defineProperty(exports,\"__esModule\",{value:true});exports.fromCodePoint=String.fromCodePoint||function(astralCodePoint){return String.fromCharCode(Math.floor((astralCodePoint-65536)/1024)+55296,(astralCodePoint-65536)%1024+56320)};exports.getCodePoint=String.prototype.codePointAt?function(input,position){return input.codePointAt(position)}:function(input,position){return(input.charCodeAt(position)-55296)*1024+input.charCodeAt(position+1)-56320+65536};exports.highSurrogateFrom=55296;exports.highSurrogateTo=56319;\n"], "mappings": "AAAA,YAAY;;AAACA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAC,YAAY,EAAC;EAACC,KAAK,EAAC;AAAI,CAAC,CAAC;AAACD,OAAO,CAACE,aAAa,GAACC,MAAM,CAACD,aAAa,IAAE,UAASE,eAAe,EAAC;EAAC,OAAOD,MAAM,CAACE,YAAY,CAACC,IAAI,CAACC,KAAK,CAAC,CAACH,eAAe,GAAC,KAAK,IAAE,IAAI,CAAC,GAAC,KAAK,EAAC,CAACA,eAAe,GAAC,KAAK,IAAE,IAAI,GAAC,KAAK,CAAC;AAAA,CAAC;AAACJ,OAAO,CAACQ,YAAY,GAACL,MAAM,CAACM,SAAS,CAACC,WAAW,GAAC,UAASC,KAAK,EAACC,QAAQ,EAAC;EAAC,OAAOD,KAAK,CAACD,WAAW,CAACE,QAAQ,CAAC;AAAA,CAAC,GAAC,UAASD,KAAK,EAACC,QAAQ,EAAC;EAAC,OAAM,CAACD,KAAK,CAACE,UAAU,CAACD,QAAQ,CAAC,GAAC,KAAK,IAAE,IAAI,GAACD,KAAK,CAACE,UAAU,CAACD,QAAQ,GAAC,CAAC,CAAC,GAAC,KAAK,GAAC,KAAK;AAAA,CAAC;AAACZ,OAAO,CAACc,iBAAiB,GAAC,KAAK;AAACd,OAAO,CAACe,eAAe,GAAC,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}