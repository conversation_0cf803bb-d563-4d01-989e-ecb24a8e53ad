{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/forms\";\nfunction LognComponent_option_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const gov_r1 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(gov_r1);\n  }\n}\nexport class LognComponent {\n  static {\n    this.ɵfac = function LognComponent_Factory(t) {\n      return new (t || LognComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LognComponent,\n      selectors: [[\"app-logn\"]],\n      decls: 19,\n      vars: 6,\n      consts: [[\"align\", \"center\", 1, \"register\"], [\"src\", \"assets/women's wear.png\", \"alt\", \"Logo\"], [\"align\", \"center\", 1, \"container\"], [\"type\", \"text\", \"name\", \"username\", 3, \"ngModel\", \"ngModelChange\", \"input\"], [\"colspan\", \"2\", \"align\", \"center\"], [3, \"hidden\"], [\"name\", \"governorate\", 3, \"ngModel\", \"hidden\", \"disabled\", \"ngModelChange\", \"change\"], [4, \"ngFor\", \"ngForOf\"]],\n      template: function LognComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵelement(1, \"img\", 1);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(2, \"div\", 2)(3, \"fieldset\")(4, \"legend\");\n          i0.ɵɵtext(5, \"Log in\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"form\")(7, \"table\")(8, \"tr\")(9, \"td\")(10, \"p\");\n          i0.ɵɵtext(11, \"Username\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"input\", 3);\n          i0.ɵɵlistener(\"ngModelChange\", function LognComponent_Template_input_ngModelChange_12_listener($event) {\n            return ctx.username = $event;\n          })(\"input\", function LognComponent_Template_input_input_12_listener() {\n            return ctx.checkStep(1);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(13, \"tr\")(14, \"td\", 4)(15, \"p\", 5);\n          i0.ɵɵtext(16, \"Governorate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"select\", 6);\n          i0.ɵɵlistener(\"ngModelChange\", function LognComponent_Template_select_ngModelChange_17_listener($event) {\n            return ctx.governorate = $event;\n          })(\"change\", function LognComponent_Template_select_change_17_listener() {\n            return ctx.checkStep(2);\n          });\n          i0.ɵɵtemplate(18, LognComponent_option_18_Template, 2, 1, \"option\", 7);\n          i0.ɵɵelementEnd()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"ngModel\", ctx.username);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"hidden\", !ctx.username);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngModel\", ctx.governorate)(\"hidden\", !ctx.username)(\"disabled\", !ctx.username);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.governorates);\n        }\n      },\n      dependencies: [i1.NgForOf, i2.ɵNgNoValidate, i2.NgSelectOption, i2.ɵNgSelectMultipleOption, i2.DefaultValueAccessor, i2.SelectControlValueAccessor, i2.NgControlStatus, i2.NgControlStatusGroup, i2.NgModel, i2.NgForm],\n      styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "gov_r1", "LognComponent", "selectors", "decls", "vars", "consts", "template", "LognComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵlistener", "LognComponent_Template_input_ngModelChange_12_listener", "$event", "username", "LognComponent_Template_input_input_12_listener", "checkStep", "LognComponent_Template_select_ngModelChange_17_listener", "governorate", "LognComponent_Template_select_change_17_listener", "ɵɵtemplate", "LognComponent_option_18_Template", "ɵɵproperty", "governorates"], "sources": ["C:\\Users\\<USER>\\Desktop\\projet angular\\Projet\\src\\logn\\logn.component.ts", "C:\\Users\\<USER>\\Desktop\\projet angular\\Projet\\src\\logn\\logn.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-logn',\n  templateUrl: './logn.component.html',\n  styleUrls: ['./logn.component.css']\n})\nexport class LognComponent {\n\n}\n", "<div class=\"register\" align=\"center\">\n    <img src=\"assets/women's wear.png\" alt=\"Logo\" />\n</div>\n<div class=\"container\" align=\"center\">\n    <fieldset>\n        <legend>Log in</legend>\n        <form>\n            <table>\n                <tr>\n                    <td>\n                        <p>Username</p>\n                        <input type=\"text\" name=\"username\" [(ngModel)]=\"username\" (input)=\"checkStep(1)\" />\n                    </td>\n                </tr>\n            \n                <!-- Governorate Field -->\n                <tr>\n                    <td colspan=\"2\" align=\"center\">\n                        <p [hidden]=\"!username\">Governorate</p>\n                        <select name=\"governorate\" [(ngModel)]=\"governorate\" [hidden]=\"!username\" [disabled]=\"!username\" (change)=\"checkStep(2)\">\n                            <option *ngFor=\"let gov of governorates\">{{ gov }}</option>\n                        </select>\n                    </td>\n                </tr>\n\n"], "mappings": ";;;;;ICoB4BA,EAAA,CAAAC,cAAA,aAAyC;IAAAD,EAAA,CAAAE,MAAA,GAAS;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAAlBH,EAAA,CAAAI,SAAA,GAAS;IAATJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAS;;;ADb9E,OAAM,MAAOC,aAAa;;;uBAAbA,aAAa;IAAA;EAAA;;;YAAbA,aAAa;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCP1Bd,EAAA,CAAAC,cAAA,aAAqC;UACjCD,EAAA,CAAAgB,SAAA,aAAgD;UACpDhB,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,aAAsC;UAEtBD,EAAA,CAAAE,MAAA,aAAM;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACvBH,EAAA,CAAAC,cAAA,WAAM;UAIaD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACfH,EAAA,CAAAC,cAAA,gBAAmF;UAAhDD,EAAA,CAAAiB,UAAA,2BAAAC,uDAAAC,MAAA;YAAA,OAAAJ,GAAA,CAAAK,QAAA,GAAAD,MAAA;UAAA,EAAsB,mBAAAE,+CAAA;YAAA,OAAUN,GAAA,CAAAO,SAAA,CAAU,CAAC,CAAC;UAAA,EAAtB;UAAzDtB,EAAA,CAAAG,YAAA,EAAmF;UAK3FH,EAAA,CAAAC,cAAA,UAAI;UAE4BD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACvCH,EAAA,CAAAC,cAAA,iBAAyH;UAA9FD,EAAA,CAAAiB,UAAA,2BAAAM,wDAAAJ,MAAA;YAAA,OAAAJ,GAAA,CAAAS,WAAA,GAAAL,MAAA;UAAA,EAAyB,oBAAAM,iDAAA;YAAA,OAAuDV,GAAA,CAAAO,SAAA,CAAU,CAAC,CAAC;UAAA,EAAnE;UAChDtB,EAAA,CAAA0B,UAAA,KAAAC,gCAAA,oBAA2D;UAC/D3B,EAAA,CAAAG,YAAA,EAAS;;;UAV0BH,EAAA,CAAAI,SAAA,IAAsB;UAAtBJ,EAAA,CAAA4B,UAAA,YAAAb,GAAA,CAAAK,QAAA,CAAsB;UAOtDpB,EAAA,CAAAI,SAAA,GAAoB;UAApBJ,EAAA,CAAA4B,UAAA,YAAAb,GAAA,CAAAK,QAAA,CAAoB;UACIpB,EAAA,CAAAI,SAAA,GAAyB;UAAzBJ,EAAA,CAAA4B,UAAA,YAAAb,GAAA,CAAAS,WAAA,CAAyB,YAAAT,GAAA,CAAAK,QAAA,eAAAL,GAAA,CAAAK,QAAA;UACxBpB,EAAA,CAAAI,SAAA,GAAe;UAAfJ,EAAA,CAAA4B,UAAA,YAAAb,GAAA,CAAAc,YAAA,CAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}