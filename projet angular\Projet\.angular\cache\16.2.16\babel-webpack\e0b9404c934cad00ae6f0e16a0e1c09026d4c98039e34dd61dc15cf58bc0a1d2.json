{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class AppComponent {\n  constructor() {\n    this.title = 'Projet';\n  }\n  static {\n    this.ɵfac = function AppComponent_Factory(t) {\n      return new (t || AppComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppComponent,\n      selectors: [[\"app-root\"]],\n      decls: 18,\n      vars: 0,\n      consts: [[1, \"container\"], [1, \"content\"], [\"src\", \"assets/women's wear.png\", \"alt\", \"\", \"routerLink\", \"/home\", 1, \"logo\"], [1, \"navmenu\"], [\"routerLink\", \"/home\"], [\"routerLink\", \"/shop\"], [\"routerLink\", \"/login\"], [1, \"end-text\"]],\n      template: function AppComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"nav\")(1, \"div\", 0)(2, \"div\", 1);\n          i0.ɵɵelement(3, \"img\", 2);\n          i0.ɵɵelementStart(4, \"ul\", 3)(5, \"li\", 4);\n          i0.ɵɵtext(6, \"Home\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"li\", 5);\n          i0.ɵɵtext(8, \"Shop\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"ul\")(10, \"li\", 6);\n          i0.ɵɵtext(11, \"Log in\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(12, \"div\", 0);\n          i0.ɵɵelement(13, \"router-outlet\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(14, \"br\");\n          i0.ɵɵelementStart(15, \"div\", 7)(16, \"p\");\n          i0.ɵɵtext(17, \"Copyright \\u00A9 2025 .All Rights Reserved.Designed By ... \");\n          i0.ɵɵelementEnd()();\n        }\n      },\n      dependencies: [i1.RouterOutlet, i1.RouterLink],\n      styles: [\"nav[_ngcontent-%COMP%] {\\n  background-color: var(--plum);\\n  box-shadow: var(--shadow);\\n  position: sticky;\\n  top: 0;\\n  z-index: 1000;\\n  width: 100%;\\n  border-bottom: 3px solid var(--primary);\\n}\\n\\nnav[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 15px 0;\\n}\\n\\nnav[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 130px;\\n  transition: var(--transition);\\n  filter: drop-shadow(0 2px 5px rgba(255, 107, 156, 0.5));\\n}\\n\\nnav[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n}\\n\\nnav[_ngcontent-%COMP%]   .navmenu[_ngcontent-%COMP%] {\\n  display: flex;\\n  list-style: none;\\n  margin: 0;\\n  padding: 0;\\n}\\n\\nnav[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  margin: 0 20px;\\n  font-weight: 500;\\n  position: relative;\\n  cursor: pointer;\\n  transition: var(--transition);\\n  font-size: 16px;\\n  letter-spacing: 0.5px;\\n  color: var(--light-pink);\\n}\\n\\nnav[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]::after {\\n  content: '';\\n  position: absolute;\\n  bottom: -5px;\\n  left: 0;\\n  width: 0;\\n  height: 2px;\\n  background: var(--primary);\\n  transition: var(--transition);\\n}\\n\\nnav[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:hover {\\n  color: var(--primary);\\n}\\n\\nnav[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:hover::after {\\n  width: 100%;\\n}\\n\\n\\n\\nnav[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:nth-child(1)::before {\\n  content: '\\u2665';\\n  position: absolute;\\n  left: -15px;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  color: var(--primary);\\n  opacity: 0;\\n  transition: var(--transition);\\n}\\n\\nnav[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:nth-child(1):hover::before {\\n  opacity: 1;\\n  left: -20px;\\n}\\n\\nnav[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:nth-child(2)::before {\\n  content: '\\u2665';\\n  position: absolute;\\n  left: -15px;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  color: var(--secondary);\\n  opacity: 0;\\n  transition: var(--transition);\\n}\\n\\nnav[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:nth-child(2):hover::before {\\n  opacity: 1;\\n  left: -20px;\\n}\\n\\n\\n\\nnav[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]:last-child   li[_ngcontent-%COMP%] {\\n  background-color: var(--primary);\\n  padding: 8px 20px;\\n  border-radius: 30px;\\n  color: var(--plum);\\n  transition: all 0.3s ease;\\n}\\n\\nnav[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]:last-child   li[_ngcontent-%COMP%]:hover {\\n  background-color: var(--secondary);\\n  transform: translateY(-3px);\\n  box-shadow: 0 5px 15px rgba(255, 107, 156, 0.3);\\n}\\n\\nnav[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]:last-child   li[_ngcontent-%COMP%]::after {\\n  display: none;\\n}\\n\\n.end-text[_ngcontent-%COMP%] {\\n  background-color: var(--plum);\\n  text-align: center;\\n  padding: 20px;\\n  font-size: 14px;\\n  color: var(--light-pink);\\n  border-top: 2px dashed var(--primary);\\n}\\n\\n@media(max-width: 768px) {\\n  nav[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    padding: 15px 0;\\n  }\\n  \\n  nav[_ngcontent-%COMP%]   .navmenu[_ngcontent-%COMP%] {\\n    margin-top: 15px;\\n  }\\n  \\n  nav[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n    margin: 0 10px;\\n  }\\n  \\n  nav[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]:last-child {\\n    margin-top: 15px;\\n  }\\n}\\n\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvYXBwLmNvbXBvbmVudC5jc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7RUFDRSw2QkFBNkI7RUFDN0IseUJBQXlCO0VBQ3pCLGdCQUFnQjtFQUNoQixNQUFNO0VBQ04sYUFBYTtFQUNiLFdBQVc7RUFDWCx1Q0FBdUM7QUFDekM7O0FBRUE7RUFDRSxhQUFhO0VBQ2IsOEJBQThCO0VBQzlCLG1CQUFtQjtFQUNuQixlQUFlO0FBQ2pCOztBQUVBO0VBQ0UsWUFBWTtFQUNaLDZCQUE2QjtFQUM3Qix1REFBdUQ7QUFDekQ7O0FBRUE7RUFDRSxzQkFBc0I7QUFDeEI7O0FBRUE7RUFDRSxhQUFhO0VBQ2IsZ0JBQWdCO0VBQ2hCLFNBQVM7RUFDVCxVQUFVO0FBQ1o7O0FBRUE7RUFDRSxjQUFjO0VBQ2QsZ0JBQWdCO0VBQ2hCLGtCQUFrQjtFQUNsQixlQUFlO0VBQ2YsNkJBQTZCO0VBQzdCLGVBQWU7RUFDZixxQkFBcUI7RUFDckIsd0JBQXdCO0FBQzFCOztBQUVBO0VBQ0UsV0FBVztFQUNYLGtCQUFrQjtFQUNsQixZQUFZO0VBQ1osT0FBTztFQUNQLFFBQVE7RUFDUixXQUFXO0VBQ1gsMEJBQTBCO0VBQzFCLDZCQUE2QjtBQUMvQjs7QUFFQTtFQUNFLHFCQUFxQjtBQUN2Qjs7QUFFQTtFQUNFLFdBQVc7QUFDYjs7QUFFQSw2QkFBNkI7QUFDN0I7RUFDRSxZQUFZO0VBQ1osa0JBQWtCO0VBQ2xCLFdBQVc7RUFDWCxRQUFRO0VBQ1IsMkJBQTJCO0VBQzNCLHFCQUFxQjtFQUNyQixVQUFVO0VBQ1YsNkJBQTZCO0FBQy9COztBQUVBO0VBQ0UsVUFBVTtFQUNWLFdBQVc7QUFDYjs7QUFFQTtFQUNFLFlBQVk7RUFDWixrQkFBa0I7RUFDbEIsV0FBVztFQUNYLFFBQVE7RUFDUiwyQkFBMkI7RUFDM0IsdUJBQXVCO0VBQ3ZCLFVBQVU7RUFDViw2QkFBNkI7QUFDL0I7O0FBRUE7RUFDRSxVQUFVO0VBQ1YsV0FBVztBQUNiOztBQUVBLGlDQUFpQztBQUNqQztFQUNFLGdDQUFnQztFQUNoQyxpQkFBaUI7RUFDakIsbUJBQW1CO0VBQ25CLGtCQUFrQjtFQUNsQix5QkFBeUI7QUFDM0I7O0FBRUE7RUFDRSxrQ0FBa0M7RUFDbEMsMkJBQTJCO0VBQzNCLCtDQUErQztBQUNqRDs7QUFFQTtFQUNFLGFBQWE7QUFDZjs7QUFFQTtFQUNFLDZCQUE2QjtFQUM3QixrQkFBa0I7RUFDbEIsYUFBYTtFQUNiLGVBQWU7RUFDZix3QkFBd0I7RUFDeEIscUNBQXFDO0FBQ3ZDOztBQUVBO0VBQ0U7SUFDRSxzQkFBc0I7SUFDdEIsZUFBZTtFQUNqQjs7RUFFQTtJQUNFLGdCQUFnQjtFQUNsQjs7RUFFQTtJQUNFLGNBQWM7RUFDaEI7O0VBRUE7SUFDRSxnQkFBZ0I7RUFDbEI7QUFDRiIsInNvdXJjZXNDb250ZW50IjpbIm5hdiB7XHJcbiAgYmFja2dyb3VuZC1jb2xvcjogdmFyKC0tcGx1bSk7XHJcbiAgYm94LXNoYWRvdzogdmFyKC0tc2hhZG93KTtcclxuICBwb3NpdGlvbjogc3RpY2t5O1xyXG4gIHRvcDogMDtcclxuICB6LWluZGV4OiAxMDAwO1xyXG4gIHdpZHRoOiAxMDAlO1xyXG4gIGJvcmRlci1ib3R0b206IDNweCBzb2xpZCB2YXIoLS1wcmltYXJ5KTtcclxufVxyXG5cclxubmF2IC5jb250ZW50IHtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcclxuICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gIHBhZGRpbmc6IDE1cHggMDtcclxufVxyXG5cclxubmF2IGltZyB7XHJcbiAgd2lkdGg6IDEzMHB4O1xyXG4gIHRyYW5zaXRpb246IHZhcigtLXRyYW5zaXRpb24pO1xyXG4gIGZpbHRlcjogZHJvcC1zaGFkb3coMCAycHggNXB4IHJnYmEoMjU1LCAxMDcsIDE1NiwgMC41KSk7XHJcbn1cclxuXHJcbm5hdiBpbWc6aG92ZXIge1xyXG4gIHRyYW5zZm9ybTogc2NhbGUoMS4wNSk7XHJcbn1cclxuXHJcbm5hdiAubmF2bWVudSB7XHJcbiAgZGlzcGxheTogZmxleDtcclxuICBsaXN0LXN0eWxlOiBub25lO1xyXG4gIG1hcmdpbjogMDtcclxuICBwYWRkaW5nOiAwO1xyXG59XHJcblxyXG5uYXYgbGkge1xyXG4gIG1hcmdpbjogMCAyMHB4O1xyXG4gIGZvbnQtd2VpZ2h0OiA1MDA7XHJcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xyXG4gIGN1cnNvcjogcG9pbnRlcjtcclxuICB0cmFuc2l0aW9uOiB2YXIoLS10cmFuc2l0aW9uKTtcclxuICBmb250LXNpemU6IDE2cHg7XHJcbiAgbGV0dGVyLXNwYWNpbmc6IDAuNXB4O1xyXG4gIGNvbG9yOiB2YXIoLS1saWdodC1waW5rKTtcclxufVxyXG5cclxubmF2IGxpOjphZnRlciB7XHJcbiAgY29udGVudDogJyc7XHJcbiAgcG9zaXRpb246IGFic29sdXRlO1xyXG4gIGJvdHRvbTogLTVweDtcclxuICBsZWZ0OiAwO1xyXG4gIHdpZHRoOiAwO1xyXG4gIGhlaWdodDogMnB4O1xyXG4gIGJhY2tncm91bmQ6IHZhcigtLXByaW1hcnkpO1xyXG4gIHRyYW5zaXRpb246IHZhcigtLXRyYW5zaXRpb24pO1xyXG59XHJcblxyXG5uYXYgbGk6aG92ZXIge1xyXG4gIGNvbG9yOiB2YXIoLS1wcmltYXJ5KTtcclxufVxyXG5cclxubmF2IGxpOmhvdmVyOjphZnRlciB7XHJcbiAgd2lkdGg6IDEwMCU7XHJcbn1cclxuXHJcbi8qIEZhbmN5IGFuaW1hdGVkIG1lbnUgaXRlbSAqL1xyXG5uYXYgbGk6bnRoLWNoaWxkKDEpOjpiZWZvcmUge1xyXG4gIGNvbnRlbnQ6ICfDosKZwqUnO1xyXG4gIHBvc2l0aW9uOiBhYnNvbHV0ZTtcclxuICBsZWZ0OiAtMTVweDtcclxuICB0b3A6IDUwJTtcclxuICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTUwJSk7XHJcbiAgY29sb3I6IHZhcigtLXByaW1hcnkpO1xyXG4gIG9wYWNpdHk6IDA7XHJcbiAgdHJhbnNpdGlvbjogdmFyKC0tdHJhbnNpdGlvbik7XHJcbn1cclxuXHJcbm5hdiBsaTpudGgtY2hpbGQoMSk6aG92ZXI6OmJlZm9yZSB7XHJcbiAgb3BhY2l0eTogMTtcclxuICBsZWZ0OiAtMjBweDtcclxufVxyXG5cclxubmF2IGxpOm50aC1jaGlsZCgyKTo6YmVmb3JlIHtcclxuICBjb250ZW50OiAnw6LCmcKlJztcclxuICBwb3NpdGlvbjogYWJzb2x1dGU7XHJcbiAgbGVmdDogLTE1cHg7XHJcbiAgdG9wOiA1MCU7XHJcbiAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC01MCUpO1xyXG4gIGNvbG9yOiB2YXIoLS1zZWNvbmRhcnkpO1xyXG4gIG9wYWNpdHk6IDA7XHJcbiAgdHJhbnNpdGlvbjogdmFyKC0tdHJhbnNpdGlvbik7XHJcbn1cclxuXHJcbm5hdiBsaTpudGgtY2hpbGQoMik6aG92ZXI6OmJlZm9yZSB7XHJcbiAgb3BhY2l0eTogMTtcclxuICBsZWZ0OiAtMjBweDtcclxufVxyXG5cclxuLyogTG9naW4gYnV0dG9uIHNwZWNpYWwgc3R5bGluZyAqL1xyXG5uYXYgdWw6bGFzdC1jaGlsZCBsaSB7XHJcbiAgYmFja2dyb3VuZC1jb2xvcjogdmFyKC0tcHJpbWFyeSk7XHJcbiAgcGFkZGluZzogOHB4IDIwcHg7XHJcbiAgYm9yZGVyLXJhZGl1czogMzBweDtcclxuICBjb2xvcjogdmFyKC0tcGx1bSk7XHJcbiAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcclxufVxyXG5cclxubmF2IHVsOmxhc3QtY2hpbGQgbGk6aG92ZXIge1xyXG4gIGJhY2tncm91bmQtY29sb3I6IHZhcigtLXNlY29uZGFyeSk7XHJcbiAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0zcHgpO1xyXG4gIGJveC1zaGFkb3c6IDAgNXB4IDE1cHggcmdiYSgyNTUsIDEwNywgMTU2LCAwLjMpO1xyXG59XHJcblxyXG5uYXYgdWw6bGFzdC1jaGlsZCBsaTo6YWZ0ZXIge1xyXG4gIGRpc3BsYXk6IG5vbmU7XHJcbn1cclxuXHJcbi5lbmQtdGV4dCB7XHJcbiAgYmFja2dyb3VuZC1jb2xvcjogdmFyKC0tcGx1bSk7XHJcbiAgdGV4dC1hbGlnbjogY2VudGVyO1xyXG4gIHBhZGRpbmc6IDIwcHg7XHJcbiAgZm9udC1zaXplOiAxNHB4O1xyXG4gIGNvbG9yOiB2YXIoLS1saWdodC1waW5rKTtcclxuICBib3JkZXItdG9wOiAycHggZGFzaGVkIHZhcigtLXByaW1hcnkpO1xyXG59XHJcblxyXG5AbWVkaWEobWF4LXdpZHRoOiA3NjhweCkge1xyXG4gIG5hdiAuY29udGVudCB7XHJcbiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xyXG4gICAgcGFkZGluZzogMTVweCAwO1xyXG4gIH1cclxuICBcclxuICBuYXYgLm5hdm1lbnUge1xyXG4gICAgbWFyZ2luLXRvcDogMTVweDtcclxuICB9XHJcbiAgXHJcbiAgbmF2IGxpIHtcclxuICAgIG1hcmdpbjogMCAxMHB4O1xyXG4gIH1cclxuICBcclxuICBuYXYgdWw6bGFzdC1jaGlsZCB7XHJcbiAgICBtYXJnaW4tdG9wOiAxNXB4O1xyXG4gIH1cclxufVxyXG5cclxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["AppComponent", "constructor", "title", "selectors", "decls", "vars", "consts", "template", "AppComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd"], "sources": ["C:\\Users\\<USER>\\Desktop\\projet angular\\Projet\\src\\app\\app.component.ts", "C:\\Users\\<USER>\\Desktop\\projet angular\\Projet\\src\\app\\app.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-root',\n  templateUrl: './app.component.html',\n  styleUrls: ['./app.component.css']\n})\nexport class AppComponent {\n  title = 'Projet';\n}\n", "<nav>\n    <div class=\"container\">\n        <div class=\"content\">\n            <img class=\"logo\" src=\"assets/women's wear.png\" alt=\"\" routerLink=\"/home\">\n            <ul class=\"navmenu\">\n                <li routerLink=\"/home\">Home</li>\n                <li routerLink=\"/shop\">Shop</li>\n            </ul>\n            <ul> \n                <li routerLink=\"/login\">Log in</li>\n            </ul>\n        </div>\n    </div>\n</nav>\n<div class=\"container\"><router-outlet></router-outlet>\n</div>\n<br>\n<div class=\"end-text\">\n    <p>Copyright © 2025 .All Rights Reserved.Designed By ... </p>\n</div>\n"], "mappings": ";;AAOA,OAAM,MAAOA,YAAY;EALzBC,YAAA;IAME,KAAAC,KAAK,GAAG,QAAQ;;;;uBADLF,YAAY;IAAA;EAAA;;;YAAZA,YAAY;MAAAG,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCPzBE,EAAA,CAAAC,cAAA,UAAK;UAGOD,EAAA,CAAAE,SAAA,aAA0E;UAC1EF,EAAA,CAAAC,cAAA,YAAoB;UACOD,EAAA,CAAAG,MAAA,WAAI;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAChCJ,EAAA,CAAAC,cAAA,YAAuB;UAAAD,EAAA,CAAAG,MAAA,WAAI;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAEpCJ,EAAA,CAAAC,cAAA,SAAI;UACwBD,EAAA,CAAAG,MAAA,cAAM;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAKnDJ,EAAA,CAAAC,cAAA,cAAuB;UAAAD,EAAA,CAAAE,SAAA,qBAA+B;UACtDF,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAE,SAAA,UAAI;UACJF,EAAA,CAAAC,cAAA,cAAsB;UACfD,EAAA,CAAAG,MAAA,mEAAsD;UAAAH,EAAA,CAAAI,YAAA,EAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}