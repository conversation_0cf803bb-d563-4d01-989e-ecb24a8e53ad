{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/forms\";\nexport class LognComponent {\n  constructor(router) {\n    this.router = router;\n  }\n  onSubmit() {\n    this.router.navigate(['/home']);\n  }\n  static {\n    this.ɵfac = function LognComponent_Factory(t) {\n      return new (t || LognComponent)(i0.ɵɵdirectiveInject(i1.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LognComponent,\n      selectors: [[\"app-logn\"]],\n      decls: 25,\n      vars: 0,\n      consts: [[\"align\", \"center\", 1, \"register\"], [\"src\", \"assets/women's wear.png\", \"alt\", \"Logo\"], [\"align\", \"center\", 1, \"container\"], [\"type\", \"text\", \"name\", \"username\"], [\"type\", \"password\", \"name\", \"password\"], [\"routerLink\", \"/signup\"], [\"type\", \"button\", 2, \"padding\", \"5px 45px\", 3, \"click\"], [\"type\", \"button\", 2, \"padding\", \"5px 45px\"]],\n      template: function LognComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵelement(1, \"img\", 1);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(2, \"div\", 2)(3, \"fieldset\")(4, \"legend\");\n          i0.ɵɵtext(5, \"Log in\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"form\")(7, \"table\")(8, \"tr\")(9, \"td\")(10, \"p\");\n          i0.ɵɵtext(11, \"Username\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(12, \"input\", 3);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(13, \"tr\")(14, \"td\")(15, \"p\");\n          i0.ɵɵtext(16, \"password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(17, \"input\", 4);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(18, \"a\", 5);\n          i0.ɵɵtext(19, \"You don't have an account?click here to sign up\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"div\")(21, \"button\", 6);\n          i0.ɵɵlistener(\"click\", function LognComponent_Template_button_click_21_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵtext(22, \"Submit\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"button\", 7);\n          i0.ɵɵtext(24, \"Reset\");\n          i0.ɵɵelementEnd()()()()();\n        }\n      },\n      dependencies: [i1.RouterLink, i2.ɵNgNoValidate, i2.NgControlStatusGroup, i2.NgForm],\n      styles: [\".register[_ngcontent-%COMP%]{\\n    padding-top: 80px;\\n}\\nimg[_ngcontent-%COMP%]{\\nwidth: 200px;\\nheight: 200px;\\n} \\nlegend[_ngcontent-%COMP%]{\\nfont-size:50px;\\nfont-family: 'Trebuchet MS'; \\nfont-style: italic;\\nfont-weight:bold; \\ncolor: rgb(22, 21, 21);\\n}\\np[_ngcontent-%COMP%]{\\nfont-size:15px;\\nfont-family: 'Lucida Sans Unicode';\\nfont-style: italic;  \\ncolor: rgb(22, 21, 21);\\n\\n}\\nselect[_ngcontent-%COMP%]{\\npadding:1px 45px;\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9sb2duL2xvZ24uY29tcG9uZW50LmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtJQUNJLGlCQUFpQjtBQUNyQjtBQUNBO0FBQ0EsWUFBWTtBQUNaLGFBQWE7QUFDYjtBQUNBO0FBQ0EsY0FBYztBQUNkLDJCQUEyQjtBQUMzQixrQkFBa0I7QUFDbEIsZ0JBQWdCO0FBQ2hCLHNCQUFzQjtBQUN0QjtBQUNBO0FBQ0EsY0FBYztBQUNkLGtDQUFrQztBQUNsQyxrQkFBa0I7QUFDbEIsc0JBQXNCOztBQUV0QjtBQUNBO0FBQ0EsZ0JBQWdCO0FBQ2hCIiwic291cmNlc0NvbnRlbnQiOlsiLnJlZ2lzdGVye1xyXG4gICAgcGFkZGluZy10b3A6IDgwcHg7XHJcbn1cclxuaW1ne1xyXG53aWR0aDogMjAwcHg7XHJcbmhlaWdodDogMjAwcHg7XHJcbn0gXHJcbmxlZ2VuZHtcclxuZm9udC1zaXplOjUwcHg7XHJcbmZvbnQtZmFtaWx5OiAnVHJlYnVjaGV0IE1TJzsgXHJcbmZvbnQtc3R5bGU6IGl0YWxpYztcclxuZm9udC13ZWlnaHQ6Ym9sZDsgXHJcbmNvbG9yOiByZ2IoMjIsIDIxLCAyMSk7XHJcbn1cclxucHtcclxuZm9udC1zaXplOjE1cHg7XHJcbmZvbnQtZmFtaWx5OiAnTHVjaWRhIFNhbnMgVW5pY29kZSc7XHJcbmZvbnQtc3R5bGU6IGl0YWxpYzsgIFxyXG5jb2xvcjogcmdiKDIyLCAyMSwgMjEpO1xyXG5cclxufVxyXG5zZWxlY3R7XHJcbnBhZGRpbmc6MXB4IDQ1cHg7XHJcbn1cclxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["LognComponent", "constructor", "router", "onSubmit", "navigate", "i0", "ɵɵdirectiveInject", "i1", "Router", "selectors", "decls", "vars", "consts", "template", "LognComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵlistener", "LognComponent_Template_button_click_21_listener"], "sources": ["C:\\Users\\<USER>\\Desktop\\projet angular\\Projet\\src\\logn\\logn.component.ts", "C:\\Users\\<USER>\\Desktop\\projet angular\\Projet\\src\\logn\\logn.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { Router } from '@angular/router';\n\n@Component({\n  selector: 'app-logn',\n  templateUrl: './logn.component.html',\n  styleUrls: ['./logn.component.css']\n})\nexport class LognComponent {\n  constructor(private router: Router) {}\n  onSubmit() {this.router.navigate(['/home']);}\n}\n", "<div class=\"register\" align=\"center\">\n    <img src=\"assets/women's wear.png\" alt=\"Logo\" />\n</div>\n<div class=\"container\" align=\"center\">\n    <fieldset>\n        <legend>Log in</legend>\n        <form>\n            <table>\n                <tr>\n                    <td>\n                        <p>Username</p>\n                        <input type=\"text\" name=\"username\"/>\n                    </td>\n                </tr>\n            \n                <!-- Governorate Field -->\n                <tr>\n                    <td>\n                        <p>password</p>\n                        <input type=\"password\" name=\"password\"/>\n                    </td>\n                </tr>\n            </table>\n            <a routerLink=\"/signup\">You don't have an account?click here to sign up</a>\n            <div>\n                <button type=\"button\" style=\"padding: 5px 45px;\" (click)=\"onSubmit()\">Submit</button>\n                <button type=\"button\" style=\"padding: 5px 45px;\">Reset</button>\n            </div>\n        </form>\n    </fieldset>\n</div>\n\n\n"], "mappings": ";;;AAQA,OAAM,MAAOA,aAAa;EACxBC,YAAoBC,MAAc;IAAd,KAAAA,MAAM,GAANA,MAAM;EAAW;EACrCC,QAAQA,CAAA;IAAI,IAAI,CAACD,MAAM,CAACE,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;EAAC;;;uBAFjCJ,aAAa,EAAAK,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAbR,aAAa;MAAAS,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCR1BV,EAAA,CAAAY,cAAA,aAAqC;UACjCZ,EAAA,CAAAa,SAAA,aAAgD;UACpDb,EAAA,CAAAc,YAAA,EAAM;UACNd,EAAA,CAAAY,cAAA,aAAsC;UAEtBZ,EAAA,CAAAe,MAAA,aAAM;UAAAf,EAAA,CAAAc,YAAA,EAAS;UACvBd,EAAA,CAAAY,cAAA,WAAM;UAIaZ,EAAA,CAAAe,MAAA,gBAAQ;UAAAf,EAAA,CAAAc,YAAA,EAAI;UACfd,EAAA,CAAAa,SAAA,gBAAoC;UACxCb,EAAA,CAAAc,YAAA,EAAK;UAITd,EAAA,CAAAY,cAAA,UAAI;UAEOZ,EAAA,CAAAe,MAAA,gBAAQ;UAAAf,EAAA,CAAAc,YAAA,EAAI;UACfd,EAAA,CAAAa,SAAA,gBAAwC;UAC5Cb,EAAA,CAAAc,YAAA,EAAK;UAGbd,EAAA,CAAAY,cAAA,YAAwB;UAAAZ,EAAA,CAAAe,MAAA,uDAA+C;UAAAf,EAAA,CAAAc,YAAA,EAAI;UAC3Ed,EAAA,CAAAY,cAAA,WAAK;UACgDZ,EAAA,CAAAgB,UAAA,mBAAAC,gDAAA;YAAA,OAASN,GAAA,CAAAb,QAAA,EAAU;UAAA,EAAC;UAACE,EAAA,CAAAe,MAAA,cAAM;UAAAf,EAAA,CAAAc,YAAA,EAAS;UACrFd,EAAA,CAAAY,cAAA,iBAAiD;UAAAZ,EAAA,CAAAe,MAAA,aAAK;UAAAf,EAAA,CAAAc,YAAA,EAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}