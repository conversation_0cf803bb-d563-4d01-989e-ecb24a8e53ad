import { Component } from '@angular/core';

@Component({
  selector: 'app-shop',
  templateUrl: './shop.component.html',
  styleUrls: ['./shop.component.css']
})
export class ShopComponent {
  selectedCategory: string = '';
  searchQuery: string = '';
  items = [
    { id: 1, title: 'dress', description: 'Description 1', price: 50, category: '1', imgSrc:'assets/4.jpg'},
    { id: 2, title: 'shorts', description: 'Description 2', price: 60, category: '2', imgSrc:'assets/2.jpg'  },
    { id: 3, title: 'shirt', description: 'Description 3', price: 70, category: '1', imgSrc:'assets/7.jpg'  },
    { id: 4, title: 'jacket', description: 'Description 4', price: 80, category: '3', imgSrc:'assets/8.jpg'  },
    { id: 5, title: 'pants', description: 'Description 5', price: 50, category: '1', imgSrc:'assets/5.jpg'  },
    { id: 6, title: 'jacket', description: 'Description 6', price: 70, category: '1', imgSrc:'assets/1.jpg'  },
    { id: 7, title: 'pink shorts ', description: 'Description 7', price: 40, category: '2', imgSrc:'assets/3.jpg'  }

  ];

  getFilteredItems() {
    const query = this.searchQuery.toLowerCase();
    return this.items.filter(item => {
      const matchesCategory = !this.selectedCategory || item.category === this.selectedCategory;
      const matchesSearch = item.title.toLowerCase().includes(query) || item.description.toLowerCase().includes(query);
      return matchesCategory && matchesSearch;
    });
  }

}
