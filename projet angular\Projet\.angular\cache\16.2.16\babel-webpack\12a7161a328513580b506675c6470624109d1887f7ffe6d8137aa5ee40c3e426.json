{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nexport class ShopComponent {\n  static {\n    this.ɵfac = function ShopComponent_Factory(t) {\n      return new (t || ShopComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ShopComponent,\n      selectors: [[\"app-shop\"]],\n      decls: 27,\n      vars: 0,\n      consts: [[1, \"box\", \"mt-5\"], [1, \"my-2\", \"w-25\"], [\"for\", \"\", 1, \"my-1\"], [1, \"form-control\"], [\"value\", \"1\"], [\"value\", \"2\"], [\"value\", \"3\"], [\"value\", \"4\"], [1, \"row\"], [1, \"col-md-3\", \"col-sm-12\"], [1, \"item\"], [\"src\", \"test\", \"alt\", \"\"], [1, \"body\"], [1, \"px-3\", \"mb-2\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"btn\", \"btn-success\"]],\n      template: function ShopComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"label\", 2);\n          i0.ɵɵtext(3, \"Categories\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"select\", 3)(5, \"option\", 4);\n          i0.ɵɵtext(6, \"option1\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"option\", 5);\n          i0.ɵɵtext(8, \"option2\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"option\", 6);\n          i0.ɵɵtext(10, \"option3\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"option\", 7);\n          i0.ɵɵtext(12, \"option4\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(13, \"div\", 8)(14, \"div\", 9)(15, \"div\", 10);\n          i0.ɵɵelement(16, \"img\", 11);\n          i0.ɵɵelementStart(17, \"div\", 12)(18, \"h4\");\n          i0.ɵɵtext(19, \"Some title\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"p\");\n          i0.ɵɵtext(21, \"Test add Description\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(22, \"div\", 13)(23, \"button\", 14);\n          i0.ɵɵtext(24, \"Add to Cart\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"span\");\n          i0.ɵɵtext(26, \"50 DT\");\n          i0.ɵɵelementEnd()()()()()();\n        }\n      },\n      dependencies: [i1.NgSelectOption, i1.ɵNgSelectMultipleOption],\n      styles: [\"*[_ngcontent-%COMP%]{\\n    margin-top: 100px;}\\n.item[_ngcontent-%COMP%]{\\n    width: 50%;\\n    text-decoration: none;\\n    color: #333;\\n    margin: 15px;\\n    display: inline-block;\\n    border-radius: 10px;\\n    box-shadow: 0px 2px 5px rgba(202,37,16,0.3);\\n    transition: all 0.3s ease-in-out;\\n    img{\\n        width: 100%;\\n        height: 200px;\\n        border-radius: 10px;\\n    }\\n    .body{padding-top: 0;\\n    }\\n    p{\\n        margin: 75px;\\n        height: 100px;\\n        overflow: hidden;\\n        font-size: 16px;\\n        padding: 5px 15px;\\n    }\\n    h4{\\n        height: 75px;\\n        overflow: hidden;\\n        padding: 5px 15px;\\n        font-size: 18px;\\n        background-color: #eee;\\n        font-weight: 600;\\n        margin-top: 10px;\\n    }\\n    &:hover{\\n        transform: scale(1.1);\\n    }\\n    span{\\n        background-color: #f1b4b4;\\n        padding: 3px 7px;\\n        border-radius: 30px;\\n    }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["ShopComponent", "selectors", "decls", "vars", "consts", "template", "ShopComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement"], "sources": ["C:\\Users\\<USER>\\Desktop\\projet angular\\Projet\\src\\shop\\shop.component.ts", "C:\\Users\\<USER>\\Desktop\\projet angular\\Projet\\src\\shop\\shop.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-shop',\n  templateUrl: './shop.component.html',\n  styleUrls: ['./shop.component.css']\n})\nexport class ShopComponent {\n\n}\n", "<div class=\" box mt-5\">\n    <div class=\"my-2 w-25\" >\n        <label for=\"\" class=\"my-1\">Categories</label>\n        <select class=\"form-control\">\n            <option value=\"1\">option1</option>\n            <option value=\"2\">option2</option>\n            <option value=\"3\">option3</option>\n            <option value=\"4\">option4</option>\n        </select>\n    </div>\n    <div class=\"row\">\n        <div class=\"col-md-3 col-sm-12\">\n            <div class=\"item\">\n                <img src=\"test\" alt=\"\">\n                <div class=\"body\">\n                    <h4>Some title</h4>\n                    <p>Test add Description</p>\n                </div>\n                <div class=\"px-3 mb-2 d-flex justify-content-between align-items-center\">\n                    <button class=\"btn btn-success\">Add to Cart</button>\n                    <span>50 DT</span>\n                </div>\n            </div>\n        </div>\n    </div>\n</div>\n\n"], "mappings": ";;AAOA,OAAM,MAAOA,aAAa;;;uBAAbA,aAAa;IAAA;EAAA;;;YAAbA,aAAa;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCP1BE,EAAA,CAAAC,cAAA,aAAuB;UAEYD,EAAA,CAAAE,MAAA,iBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAQ;UAC7CH,EAAA,CAAAC,cAAA,gBAA6B;UACPD,EAAA,CAAAE,MAAA,cAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAClCH,EAAA,CAAAC,cAAA,gBAAkB;UAAAD,EAAA,CAAAE,MAAA,cAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAClCH,EAAA,CAAAC,cAAA,gBAAkB;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAClCH,EAAA,CAAAC,cAAA,iBAAkB;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAG1CH,EAAA,CAAAC,cAAA,cAAiB;UAGLD,EAAA,CAAAI,SAAA,eAAuB;UACvBJ,EAAA,CAAAC,cAAA,eAAkB;UACVD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACnBH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,4BAAoB;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAE/BH,EAAA,CAAAC,cAAA,eAAyE;UACrCD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACpDH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}