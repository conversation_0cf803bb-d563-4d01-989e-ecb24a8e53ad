/* Pink-Themed CSS with Feminine Colors */
:root {
  --primary: #ff80ab;
  --secondary: #f06292;
  --accent: #ff4081;
  --light-pink: #fce4ec;
  --medium-pink: #f8bbd0;
  --dark-pink: #ad1457;
  --white: #ffffff;
  --off-white: #fafafa;
  --shadow: 0 5px 20px rgba(240, 98, 146, 0.2);
  --transition: all 0.3s ease;
  --border-radius: 15px;
  --font-cursive: 'Pacifico', cursive;
  --font-main: 'Montserrat', sans-serif;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--font-main);
  line-height: 1.6;
  color: var(--dark-pink);
  background-color: var(--light-pink);
  background-image: linear-gradient(135deg, var(--light-pink), var(--medium-pink));
  margin: 0;
}

a {
  text-decoration: none;
  color: inherit;
}

img {
  max-width: 100%;
  height: auto;
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

.btn {
  display: inline-block;
  padding: 12px 28px;
  background: var(--primary);
  color: var(--white);
  border: none;
  border-radius: 50px;
  cursor: pointer;
  transition: var(--transition);
  font-weight: 500;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 10px rgba(240, 98, 146, 0.3);
}

.btn:hover {
  background: var(--accent);
  transform: translateY(-3px);
  box-shadow: 0 7px 15px rgba(255, 64, 129, 0.4);
}






