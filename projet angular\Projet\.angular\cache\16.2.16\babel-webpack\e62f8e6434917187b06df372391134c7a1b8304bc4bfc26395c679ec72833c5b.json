{"ast": null, "code": "export function getSymbolIterator() {\n  if (typeof Symbol !== 'function' || !Symbol.iterator) {\n    return '@@iterator';\n  }\n  return Symbol.iterator;\n}\nexport const iterator = getSymbolIterator();", "map": {"version": 3, "names": ["getSymbolIterator", "Symbol", "iterator"], "sources": ["C:/Users/<USER>/Desktop/projet angular/Projet/node_modules/rxjs/dist/esm/internal/symbol/iterator.js"], "sourcesContent": ["export function getSymbolIterator() {\n    if (typeof Symbol !== 'function' || !Symbol.iterator) {\n        return '@@iterator';\n    }\n    return Symbol.iterator;\n}\nexport const iterator = getSymbolIterator();\n"], "mappings": "AAAA,OAAO,SAASA,iBAAiBA,CAAA,EAAG;EAChC,IAAI,OAAOC,MAAM,KAAK,UAAU,IAAI,CAACA,MAAM,CAACC,QAAQ,EAAE;IAClD,OAAO,YAAY;EACvB;EACA,OAAOD,MAAM,CAACC,QAAQ;AAC1B;AACA,OAAO,MAAMA,QAAQ,GAAGF,iBAAiB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}