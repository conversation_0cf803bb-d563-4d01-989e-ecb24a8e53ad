import { Component } from '@angular/core';
import { Router } from '@angular/router';

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.css']
})
export class LoginComponent {
  step: number = 1; // Tracks the current visible field
  username: string = '';
  governorate: string = '';
  gender: string = '';
  email: string = '';
  phone: string = '';
  password: string = '';
  confirmPassword: string = '';
  errorMessage: string = "";

  governorates: string[] = [
    '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON> Arous', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Sfax', '<PERSON><PERSON> Bouzid', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>',
  ];
  // Advances to the next step when user interacts with an input
  checkStep(currentStep: number): void {
    // Ensure step is advanced only when the input is filled
    if (currentStep === 1 && this.username) {
      this.step = 2;
    } else if (currentStep === 2 && this.governorate) {
      this.step = 3;}
      else if (currentStep === 3 && this.gender) {
      this.step = 4;
    } else if (currentStep === 4 && this.email && this.phone) {
      this.step = 5;
    } else if (currentStep === 5) {
      if (this.password === this.confirmPassword) {
        this.step = 6;
      }
    }
  }    

  constructor(private router: Router) {}

  onSubmit() {
    if (this.password !== this.confirmPassword) {
      this.errorMessage = "Passwords do not match. Please try again.";
      return; // Stop further execution
    }
    this.router.navigate(['/home']);
}
}
