{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nfunction LoginComponent_option_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const gov_r1 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(gov_r1);\n  }\n}\nexport class LoginComponent {\n  // Advances to the next step when user interacts with an input\n  checkStep(currentStep) {\n    // Ensure step is advanced only when the input is filled\n    if (currentStep === 1 && this.username) {\n      this.step = 2;\n    } else if (currentStep === 2 && this.governorate) {\n      this.step = 3;\n    } else if (currentStep === 3 && this.age && this.gender) {\n      this.step = 4;\n    } else if (currentStep === 4 && this.email && this.phone) {\n      this.step = 5;\n    } else if (currentStep === 5 && this.password && this.confirmPassword) {\n      this.step = 6;\n    }\n  }\n  constructor(router) {\n    this.router = router;\n    this.step = 1; // Tracks the current visible field\n    this.username = '';\n    this.governorate = '';\n    this.age = 0; // Default age is 0 until the user provides input\n    this.gender = '';\n    this.email = '';\n    this.phone = '';\n    this.password = '';\n    this.confirmPassword = '';\n    this.isOver18 = false;\n    this.governorates = ['Ariana', 'Béja', 'Ben Arous', 'Bizerte', 'Gabès', 'Gafsa', 'Jendouba', 'Kairouan', 'Kasserine', 'Kebili', 'Kef', 'Mahdia', 'Manouba', 'Medenine', 'Monastir', 'Nabeul', 'Sfax', 'Sidi Bouzid', 'Siliana', 'Sousse', 'Tataouine', 'Tozeur', 'Tunis', 'Zaghouan'];\n  }\n  canSubmit() {\n    // Check if all conditions are met\n    return this.step >= 6 && this.password === this.confirmPassword && this.isOver18;\n  }\n  onSubmit() {\n    // If conditions are met, navigate to the home page\n    if (this.canSubmit()) {\n      this.router.navigate(['/home']);\n    } else {\n      alert('Please ensure all fields are filled out correctly!');\n    }\n  }\n  static {\n    this.ɵfac = function LoginComponent_Factory(t) {\n      return new (t || LoginComponent)(i0.ɵɵdirectiveInject(i1.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LoginComponent,\n      selectors: [[\"app-login\"]],\n      decls: 61,\n      vars: 39,\n      consts: [[\"align\", \"center\", 1, \"register\"], [\"src\", \"assets/logo.jpg\", \"alt\", \"Logo\"], [\"align\", \"center\", 1, \"container\"], [\"type\", \"text\", \"name\", \"username\", 3, \"ngModel\", \"ngModelChange\", \"input\"], [\"colspan\", \"2\", \"align\", \"center\"], [3, \"hidden\", \"change\"], [\"name\", \"governorate\", 3, \"ngModel\", \"hidden\", \"disabled\", \"ngModelChange\", \"change\"], [4, \"ngFor\", \"ngForOf\"], [3, \"hidden\", \"input\"], [\"type\", \"date\", 3, \"ngModel\", \"hidden\", \"disabled\", \"ngModelChange\", \"input\"], [3, \"hidden\"], [\"type\", \"radio\", \"name\", \"gender\", \"value\", \"Male\", 3, \"ngModel\", \"hidden\", \"disabled\", \"ngModelChange\"], [\"type\", \"radio\", \"name\", \"gender\", \"value\", \"Female\", 3, \"ngModel\", \"hidden\", \"disabled\", \"ngModelChange\"], [\"type\", \"email\", 3, \"ngModel\", \"hidden\", \"disabled\", \"ngModelChange\", \"input\"], [\"type\", \"tel\", 3, \"ngModel\", \"hidden\", \"disabled\", \"ngModelChange\", \"input\"], [\"type\", \"password\", \"name\", \"password\", 3, \"ngModel\", \"hidden\", \"disabled\", \"ngModelChange\", \"input\"], [\"type\", \"password\", \"name\", \"confirmPassword\", 3, \"ngModel\", \"hidden\", \"disabled\", \"ngModelChange\", \"input\"], [\"type\", \"checkbox\", 3, \"ngModel\", \"hidden\", \"disabled\", \"ngModelChange\"], [\"type\", \"button\", 2, \"padding\", \"5px 45px\", 3, \"click\"], [\"type\", \"button\", 2, \"padding\", \"5px 45px\"]],\n      template: function LoginComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵelement(1, \"img\", 1);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(2, \"div\", 2)(3, \"fieldset\")(4, \"legend\");\n          i0.ɵɵtext(5, \"Register\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"form\")(7, \"table\")(8, \"tr\")(9, \"td\")(10, \"p\");\n          i0.ɵɵtext(11, \"Username\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"input\", 3);\n          i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_12_listener($event) {\n            return ctx.username = $event;\n          })(\"input\", function LoginComponent_Template_input_input_12_listener() {\n            return ctx.checkStep(1);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(13, \"tr\")(14, \"td\", 4)(15, \"p\", 5);\n          i0.ɵɵlistener(\"change\", function LoginComponent_Template_p_change_15_listener() {\n            return ctx.checkStep(2);\n          });\n          i0.ɵɵtext(16, \"Governorate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"select\", 6);\n          i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_select_ngModelChange_17_listener($event) {\n            return ctx.governorate = $event;\n          })(\"change\", function LoginComponent_Template_select_change_17_listener() {\n            return ctx.checkStep(2);\n          });\n          i0.ɵɵtemplate(18, LoginComponent_option_18_Template, 2, 1, \"option\", 7);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(19, \"tr\")(20, \"td\")(21, \"p\", 8);\n          i0.ɵɵlistener(\"input\", function LoginComponent_Template_p_input_21_listener() {\n            return ctx.checkStep(3);\n          });\n          i0.ɵɵtext(22, \"age\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"input\", 9);\n          i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_23_listener($event) {\n            return ctx.birthday = $event;\n          })(\"input\", function LoginComponent_Template_input_input_23_listener() {\n            return ctx.checkStep(3);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(24, \"td\")(25, \"p\", 10);\n          i0.ɵɵtext(26, \"Gender\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"input\", 11);\n          i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_27_listener($event) {\n            return ctx.gender = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"span\", 10);\n          i0.ɵɵtext(29, \"Male\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"input\", 12);\n          i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_30_listener($event) {\n            return ctx.gender = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"span\", 10);\n          i0.ɵɵtext(32, \"Female\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(33, \"tr\")(34, \"td\")(35, \"p\", 8);\n          i0.ɵɵlistener(\"input\", function LoginComponent_Template_p_input_35_listener() {\n            return ctx.checkStep(4);\n          });\n          i0.ɵɵtext(36, \"Mail account\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"input\", 13);\n          i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_37_listener($event) {\n            return ctx.email = $event;\n          })(\"input\", function LoginComponent_Template_input_input_37_listener() {\n            return ctx.checkStep(4);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(38, \"td\")(39, \"p\", 8);\n          i0.ɵɵlistener(\"input\", function LoginComponent_Template_p_input_39_listener() {\n            return ctx.checkStep(4);\n          });\n          i0.ɵɵtext(40, \"Phone number\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"input\", 14);\n          i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_41_listener($event) {\n            return ctx.phone = $event;\n          })(\"input\", function LoginComponent_Template_input_input_41_listener() {\n            return ctx.checkStep(4);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(42, \"tr\")(43, \"td\")(44, \"p\", 8);\n          i0.ɵɵlistener(\"input\", function LoginComponent_Template_p_input_44_listener() {\n            return ctx.checkStep(5);\n          });\n          i0.ɵɵtext(45, \"Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"input\", 15);\n          i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_46_listener($event) {\n            return ctx.password = $event;\n          })(\"input\", function LoginComponent_Template_input_input_46_listener() {\n            return ctx.checkStep(5);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(47, \"td\")(48, \"p\", 8);\n          i0.ɵɵlistener(\"input\", function LoginComponent_Template_p_input_48_listener() {\n            return ctx.checkStep(5);\n          });\n          i0.ɵɵtext(49, \"Confirm Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(50, \"input\", 16);\n          i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_50_listener($event) {\n            return ctx.confirmPassword = $event;\n          })(\"input\", function LoginComponent_Template_input_input_50_listener() {\n            return ctx.checkStep(5);\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(51, \"div\")(52, \"input\", 17);\n          i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_52_listener($event) {\n            return ctx.isOver18 = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(53, \"span\", 10);\n          i0.ɵɵtext(54, \" By creating an account, you confirm that you are 18 years or older.\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(55, \"br\")(56, \"br\");\n          i0.ɵɵelementStart(57, \"button\", 18);\n          i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_57_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵtext(58, \"Submit\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(59, \"button\", 19);\n          i0.ɵɵtext(60, \"Reset\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"ngModel\", ctx.username);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"hidden\", !ctx.username);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngModel\", ctx.governorate)(\"hidden\", !ctx.username)(\"disabled\", !ctx.username);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.governorates);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"hidden\", !ctx.governorate);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngModel\", ctx.birthday)(\"hidden\", !ctx.governorate)(\"disabled\", !ctx.governorate);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"hidden\", !ctx.birthday);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngModel\", ctx.gender)(\"hidden\", !ctx.birthday)(\"disabled\", !ctx.birthday);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"hidden\", !ctx.birthday);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngModel\", ctx.gender)(\"hidden\", !ctx.birthday)(\"disabled\", !ctx.birthday);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"hidden\", !ctx.birthday);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"hidden\", !ctx.gender);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngModel\", ctx.email)(\"hidden\", !ctx.gender)(\"disabled\", !ctx.gender);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"hidden\", !ctx.email);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngModel\", ctx.phone)(\"hidden\", !ctx.email)(\"disabled\", !ctx.email);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"hidden\", !ctx.phone);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngModel\", ctx.password)(\"hidden\", !ctx.phone)(\"disabled\", !ctx.phone);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"hidden\", !ctx.password);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngModel\", ctx.confirmPassword)(\"hidden\", !ctx.password)(\"disabled\", !ctx.password);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngModel\", ctx.isOver18)(\"hidden\", !ctx.confirmPassword)(\"disabled\", !ctx.confirmPassword);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"hidden\", !ctx.confirmPassword);\n        }\n      },\n      dependencies: [i2.NgForOf, i3.ɵNgNoValidate, i3.NgSelectOption, i3.ɵNgSelectMultipleOption, i3.DefaultValueAccessor, i3.CheckboxControlValueAccessor, i3.SelectControlValueAccessor, i3.RadioControlValueAccessor, i3.NgControlStatus, i3.NgControlStatusGroup, i3.NgModel, i3.NgForm],\n      styles: [\".register[_ngcontent-%COMP%]{\\n        padding-top: 80px;\\n}\\nimg[_ngcontent-%COMP%]{\\n    width: 150px;\\n    height: 150px;\\n} \\nlegend[_ngcontent-%COMP%]{\\n    font-size:50px;\\n    font-family: 'Trebuchet MS'; \\n    font-style: italic;\\n    font-weight:bold; \\n    color: rgb(22, 21, 21);\\n}\\np[_ngcontent-%COMP%]{\\n    font-size:15px;\\n    font-family: 'Lucida Sans Unicode';\\n    font-style: italic;  \\n    color: rgb(22, 21, 21);\\n    \\n}\\nselect[_ngcontent-%COMP%]{\\n    padding:1px 45px;\\n}\\n    \\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9sb2dpbi9sb2dpbi5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO1FBQ1EsaUJBQWlCO0FBQ3pCO0FBQ0E7SUFDSSxZQUFZO0lBQ1osYUFBYTtBQUNqQjtBQUNBO0lBQ0ksY0FBYztJQUNkLDJCQUEyQjtJQUMzQixrQkFBa0I7SUFDbEIsZ0JBQWdCO0lBQ2hCLHNCQUFzQjtBQUMxQjtBQUNBO0lBQ0ksY0FBYztJQUNkLGtDQUFrQztJQUNsQyxrQkFBa0I7SUFDbEIsc0JBQXNCOztBQUUxQjtBQUNBO0lBQ0ksZ0JBQWdCO0FBQ3BCIiwic291cmNlc0NvbnRlbnQiOlsiLnJlZ2lzdGVye1xyXG4gICAgICAgIHBhZGRpbmctdG9wOiA4MHB4O1xyXG59XHJcbmltZ3tcclxuICAgIHdpZHRoOiAxNTBweDtcclxuICAgIGhlaWdodDogMTUwcHg7XHJcbn0gXHJcbmxlZ2VuZHtcclxuICAgIGZvbnQtc2l6ZTo1MHB4O1xyXG4gICAgZm9udC1mYW1pbHk6ICdUcmVidWNoZXQgTVMnOyBcclxuICAgIGZvbnQtc3R5bGU6IGl0YWxpYztcclxuICAgIGZvbnQtd2VpZ2h0OmJvbGQ7IFxyXG4gICAgY29sb3I6IHJnYigyMiwgMjEsIDIxKTtcclxufVxyXG5we1xyXG4gICAgZm9udC1zaXplOjE1cHg7XHJcbiAgICBmb250LWZhbWlseTogJ0x1Y2lkYSBTYW5zIFVuaWNvZGUnO1xyXG4gICAgZm9udC1zdHlsZTogaXRhbGljOyAgXHJcbiAgICBjb2xvcjogcmdiKDIyLCAyMSwgMjEpO1xyXG4gICAgXHJcbn1cclxuc2VsZWN0e1xyXG4gICAgcGFkZGluZzoxcHggNDVweDtcclxufVxyXG4gICAgIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "gov_r1", "LoginComponent", "checkStep", "currentStep", "username", "step", "governorate", "age", "gender", "email", "phone", "password", "confirmPassword", "constructor", "router", "isOver18", "governorates", "canSubmit", "onSubmit", "navigate", "alert", "ɵɵdirectiveInject", "i1", "Router", "selectors", "decls", "vars", "consts", "template", "LoginComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵlistener", "LoginComponent_Template_input_ngModelChange_12_listener", "$event", "LoginComponent_Template_input_input_12_listener", "LoginComponent_Template_p_change_15_listener", "LoginComponent_Template_select_ngModelChange_17_listener", "LoginComponent_Template_select_change_17_listener", "ɵɵtemplate", "LoginComponent_option_18_Template", "LoginComponent_Template_p_input_21_listener", "LoginComponent_Template_input_ngModelChange_23_listener", "birthday", "LoginComponent_Template_input_input_23_listener", "LoginComponent_Template_input_ngModelChange_27_listener", "LoginComponent_Template_input_ngModelChange_30_listener", "LoginComponent_Template_p_input_35_listener", "LoginComponent_Template_input_ngModelChange_37_listener", "LoginComponent_Template_input_input_37_listener", "LoginComponent_Template_p_input_39_listener", "LoginComponent_Template_input_ngModelChange_41_listener", "LoginComponent_Template_input_input_41_listener", "LoginComponent_Template_p_input_44_listener", "LoginComponent_Template_input_ngModelChange_46_listener", "LoginComponent_Template_input_input_46_listener", "LoginComponent_Template_p_input_48_listener", "LoginComponent_Template_input_ngModelChange_50_listener", "LoginComponent_Template_input_input_50_listener", "LoginComponent_Template_input_ngModelChange_52_listener", "LoginComponent_Template_button_click_57_listener", "ɵɵproperty"], "sources": ["C:\\Users\\<USER>\\Desktop\\projet angular\\Projet\\src\\login\\login.component.ts", "C:\\Users\\<USER>\\Desktop\\projet angular\\Projet\\src\\login\\login.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { Router } from '@angular/router';\n\n@Component({\n  selector: 'app-login',\n  templateUrl: './login.component.html',\n  styleUrls: ['./login.component.css']\n})\nexport class LoginComponent {\n  step: number = 1; // Tracks the current visible field\n  username: string = '';\n  governorate: string = '';\n  age: number = 0; // Default age is 0 until the user provides input\n  gender: string = '';\n  email: string = '';\n  phone: string = '';\n  password: string = '';\n  confirmPassword: string = '';\n  isOver18: boolean = false;\n\n  governorates: string[] = [\n    '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>',\n    '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>',\n    '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>',\n    '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>',\n  ];\n  // Advances to the next step when user interacts with an input\n  checkStep(currentStep: number): void {\n    // Ensure step is advanced only when the input is filled\n    if (currentStep === 1 && this.username) {\n      this.step = 2;\n    } else if (currentStep === 2 && this.governorate) {\n      this.step = 3;\n    } else if (currentStep === 3 && this.age && this.gender) {\n      this.step = 4;\n    } else if (currentStep === 4 && this.email && this.phone) {\n      this.step = 5;\n    } else if (currentStep === 5 && this.password && this.confirmPassword) {\n      this.step = 6;\n    }\n  }\n\n  constructor(private router: Router) {}\n\n  canSubmit(): boolean {\n    // Check if all conditions are met\n    return (\n      this.step >= 6 &&\n      this.password === this.confirmPassword &&\n      this.isOver18\n    );\n  }\n\n  onSubmit() {\n    // If conditions are met, navigate to the home page\n    if (this.canSubmit()) {\n      this.router.navigate(['/home']);\n  } else {\n    alert('Please ensure all fields are filled out correctly!');\n  }\n}\n}\n", "<div class=\"register\" align=\"center\">\n    <img src=\"assets/logo.jpg\" alt=\"Logo\" />\n</div>\n<div class=\"container\" align=\"center\">\n    <fieldset>\n        <legend>Register</legend>\n        <form>\n            <table>\n                <!-- Username Field -->\n                <tr>\n                    <td>\n                        <p>Username</p>\n                        <input type=\"text\" name=\"username\" [(ngModel)]=\"username\" (input)=\"checkStep(1)\" />\n                    </td>\n                </tr>\n\n                <!-- Governorate Field -->\n                <tr>\n                    <td colspan=\"2\" align=\"center\">\n                        <p [hidden]=\"!username\"  (change)=\"checkStep(2)\">Governorate</p>\n                        <select name=\"governorate\" [(ngModel)]=\"governorate\" [hidden]=\"!username\" [disabled]=\"!username\" (change)=\"checkStep(2)\">\n                            <option *ngFor=\"let gov of governorates\">{{ gov }}</option>\n                        </select>\n                    </td>\n                </tr>\n\n                <!-- Birthday and Gender Fields -->\n                <tr>\n                    <td>\n                        <p [hidden]=\"!governorate\"  (input)=\"checkStep(3)\">age</p>\n                        <input type=\"date\" [(ngModel)]=\"birthday\" [hidden]=\"!governorate\" [disabled]=\"!governorate\" (input)=\"checkStep(3)\" />\n                    </td>\n                    <td>\n                        <p [hidden]=\"!birthday\">Gender</p>\n                        <input type=\"radio\" name=\"gender\" [(ngModel)]=\"gender\" value=\"Male\" [hidden]=\"!birthday\" [disabled]=\"!birthday\" /> <span [hidden]=\"!birthday\">Male</span>\n                        <input type=\"radio\" name=\"gender\" [(ngModel)]=\"gender\" value=\"Female\" [hidden]=\"!birthday\" [disabled]=\"!birthday\" /> <span [hidden]=\"!birthday\">Female</span>\n                    </td>\n                </tr>\n\n                <!-- Email and Phone Fields -->\n                <tr>\n                    <td>\n                        <p [hidden]=\"!gender\" (input)=\"checkStep(4)\">Mail account</p>\n                        <input type=\"email\" [(ngModel)]=\"email\" [hidden]=\"!gender\" [disabled]=\"!gender\" (input)=\"checkStep(4)\" />\n                    </td>\n                    <td>\n                        <p [hidden]=\"!email\" (input)=\"checkStep(4)\">Phone number</p>\n                        <input type=\"tel\" [(ngModel)]=\"phone\" [hidden]=\"!email\" [disabled]=\"!email\" (input)=\"checkStep(4)\" />\n                    </td>\n                </tr>\n\n                <!-- Password Fields -->\n                <tr>\n                    <td>\n                        <p [hidden]=\"!phone\" (input)=\"checkStep(5)\">Password</p>\n                        <input type=\"password\" name=\"password\" [(ngModel)]=\"password\" [hidden]=\"!phone\" [disabled]=\"!phone\" (input)=\"checkStep(5)\" />\n                    </td>\n                    <td>\n                        <p [hidden]=\"!password\" (input)=\"checkStep(5)\">Confirm Password</p>\n                        <input type=\"password\" name=\"confirmPassword\" [(ngModel)]=\"confirmPassword\" [hidden]=\"!password\" [disabled]=\"!password\" (input)=\"checkStep(5)\" />\n                    </td>\n                </tr>\n            </table>\n\n            <!-- Terms and Submit -->\n            <div>\n                <input type=\"checkbox\" [(ngModel)]=\"isOver18\" [hidden]=\"!confirmPassword\" [disabled]=\"!confirmPassword\" /><span [hidden]=\"!confirmPassword\"> By creating an account, you confirm that you are 18 years or older.</span><br>\n                <br>\n                <button type=\"button\" style=\"padding: 5px 45px;\" (click)=\"onSubmit()\">Submit</button>\n                <button type=\"button\" style=\"padding: 5px 45px;\">Reset</button>\n            </div>\n\n        </form>\n    </fieldset>\n</div>\n"], "mappings": ";;;;;;ICqB4BA,EAAA,CAAAC,cAAA,aAAyC;IAAAD,EAAA,CAAAE,MAAA,GAAS;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAAlBH,EAAA,CAAAI,SAAA,GAAS;IAATJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAS;;;ADb9E,OAAM,MAAOC,cAAc;EAkBzB;EACAC,SAASA,CAACC,WAAmB;IAC3B;IACA,IAAIA,WAAW,KAAK,CAAC,IAAI,IAAI,CAACC,QAAQ,EAAE;MACtC,IAAI,CAACC,IAAI,GAAG,CAAC;KACd,MAAM,IAAIF,WAAW,KAAK,CAAC,IAAI,IAAI,CAACG,WAAW,EAAE;MAChD,IAAI,CAACD,IAAI,GAAG,CAAC;KACd,MAAM,IAAIF,WAAW,KAAK,CAAC,IAAI,IAAI,CAACI,GAAG,IAAI,IAAI,CAACC,MAAM,EAAE;MACvD,IAAI,CAACH,IAAI,GAAG,CAAC;KACd,MAAM,IAAIF,WAAW,KAAK,CAAC,IAAI,IAAI,CAACM,KAAK,IAAI,IAAI,CAACC,KAAK,EAAE;MACxD,IAAI,CAACL,IAAI,GAAG,CAAC;KACd,MAAM,IAAIF,WAAW,KAAK,CAAC,IAAI,IAAI,CAACQ,QAAQ,IAAI,IAAI,CAACC,eAAe,EAAE;MACrE,IAAI,CAACP,IAAI,GAAG,CAAC;;EAEjB;EAEAQ,YAAoBC,MAAc;IAAd,KAAAA,MAAM,GAANA,MAAM;IAjC1B,KAAAT,IAAI,GAAW,CAAC,CAAC,CAAC;IAClB,KAAAD,QAAQ,GAAW,EAAE;IACrB,KAAAE,WAAW,GAAW,EAAE;IACxB,KAAAC,GAAG,GAAW,CAAC,CAAC,CAAC;IACjB,KAAAC,MAAM,GAAW,EAAE;IACnB,KAAAC,KAAK,GAAW,EAAE;IAClB,KAAAC,KAAK,GAAW,EAAE;IAClB,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAC,eAAe,GAAW,EAAE;IAC5B,KAAAG,QAAQ,GAAY,KAAK;IAEzB,KAAAC,YAAY,GAAa,CACvB,QAAQ,EAAE,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,UAAU,EACtE,UAAU,EAAE,WAAW,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,EACzE,UAAU,EAAE,QAAQ,EAAE,MAAM,EAAE,aAAa,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,EAC7E,QAAQ,EAAE,OAAO,EAAE,UAAU,CAC9B;EAiBoC;EAErCC,SAASA,CAAA;IACP;IACA,OACE,IAAI,CAACZ,IAAI,IAAI,CAAC,IACd,IAAI,CAACM,QAAQ,KAAK,IAAI,CAACC,eAAe,IACtC,IAAI,CAACG,QAAQ;EAEjB;EAEAG,QAAQA,CAAA;IACN;IACA,IAAI,IAAI,CAACD,SAAS,EAAE,EAAE;MACpB,IAAI,CAACH,MAAM,CAACK,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;KAClC,MAAM;MACLC,KAAK,CAAC,oDAAoD,CAAC;;EAE/D;;;uBApDanB,cAAc,EAAAP,EAAA,CAAA2B,iBAAA,CAAAC,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAdtB,cAAc;MAAAuB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCR3BpC,EAAA,CAAAC,cAAA,aAAqC;UACjCD,EAAA,CAAAsC,SAAA,aAAwC;UAC5CtC,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,aAAsC;UAEtBD,EAAA,CAAAE,MAAA,eAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACzBH,EAAA,CAAAC,cAAA,WAAM;UAKaD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACfH,EAAA,CAAAC,cAAA,gBAAmF;UAAhDD,EAAA,CAAAuC,UAAA,2BAAAC,wDAAAC,MAAA;YAAA,OAAAJ,GAAA,CAAA3B,QAAA,GAAA+B,MAAA;UAAA,EAAsB,mBAAAC,gDAAA;YAAA,OAAUL,GAAA,CAAA7B,SAAA,CAAU,CAAC,CAAC;UAAA,EAAtB;UAAzDR,EAAA,CAAAG,YAAA,EAAmF;UAK3FH,EAAA,CAAAC,cAAA,UAAI;UAE6BD,EAAA,CAAAuC,UAAA,oBAAAI,6CAAA;YAAA,OAAUN,GAAA,CAAA7B,SAAA,CAAU,CAAC,CAAC;UAAA,EAAC;UAACR,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAChEH,EAAA,CAAAC,cAAA,iBAAyH;UAA9FD,EAAA,CAAAuC,UAAA,2BAAAK,yDAAAH,MAAA;YAAA,OAAAJ,GAAA,CAAAzB,WAAA,GAAA6B,MAAA;UAAA,EAAyB,oBAAAI,kDAAA;YAAA,OAAuDR,GAAA,CAAA7B,SAAA,CAAU,CAAC,CAAC;UAAA,EAAnE;UAChDR,EAAA,CAAA8C,UAAA,KAAAC,iCAAA,oBAA2D;UAC/D/C,EAAA,CAAAG,YAAA,EAAS;UAKjBH,EAAA,CAAAC,cAAA,UAAI;UAEgCD,EAAA,CAAAuC,UAAA,mBAAAS,4CAAA;YAAA,OAASX,GAAA,CAAA7B,SAAA,CAAU,CAAC,CAAC;UAAA,EAAC;UAACR,EAAA,CAAAE,MAAA,WAAG;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC1DH,EAAA,CAAAC,cAAA,gBAAqH;UAAlGD,EAAA,CAAAuC,UAAA,2BAAAU,wDAAAR,MAAA;YAAA,OAAAJ,GAAA,CAAAa,QAAA,GAAAT,MAAA;UAAA,EAAsB,mBAAAU,gDAAA;YAAA,OAA4Dd,GAAA,CAAA7B,SAAA,CAAU,CAAC,CAAC;UAAA,EAAxE;UAAzCR,EAAA,CAAAG,YAAA,EAAqH;UAEzHH,EAAA,CAAAC,cAAA,UAAI;UACwBD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAClCH,EAAA,CAAAC,cAAA,iBAAkH;UAAhFD,EAAA,CAAAuC,UAAA,2BAAAa,wDAAAX,MAAA;YAAA,OAAAJ,GAAA,CAAAvB,MAAA,GAAA2B,MAAA;UAAA,EAAoB;UAAtDzC,EAAA,CAAAG,YAAA,EAAkH;UAACH,EAAA,CAAAC,cAAA,gBAA2B;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACzJH,EAAA,CAAAC,cAAA,iBAAoH;UAAlFD,EAAA,CAAAuC,UAAA,2BAAAc,wDAAAZ,MAAA;YAAA,OAAAJ,GAAA,CAAAvB,MAAA,GAAA2B,MAAA;UAAA,EAAoB;UAAtDzC,EAAA,CAAAG,YAAA,EAAoH;UAACH,EAAA,CAAAC,cAAA,gBAA2B;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAKrKH,EAAA,CAAAC,cAAA,UAAI;UAE0BD,EAAA,CAAAuC,UAAA,mBAAAe,4CAAA;YAAA,OAASjB,GAAA,CAAA7B,SAAA,CAAU,CAAC,CAAC;UAAA,EAAC;UAACR,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC7DH,EAAA,CAAAC,cAAA,iBAAyG;UAArFD,EAAA,CAAAuC,UAAA,2BAAAgB,wDAAAd,MAAA;YAAA,OAAAJ,GAAA,CAAAtB,KAAA,GAAA0B,MAAA;UAAA,EAAmB,mBAAAe,gDAAA;YAAA,OAAkDnB,GAAA,CAAA7B,SAAA,CAAU,CAAC,CAAC;UAAA,EAA9D;UAAvCR,EAAA,CAAAG,YAAA,EAAyG;UAE7GH,EAAA,CAAAC,cAAA,UAAI;UACqBD,EAAA,CAAAuC,UAAA,mBAAAkB,4CAAA;YAAA,OAASpB,GAAA,CAAA7B,SAAA,CAAU,CAAC,CAAC;UAAA,EAAC;UAACR,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC5DH,EAAA,CAAAC,cAAA,iBAAqG;UAAnFD,EAAA,CAAAuC,UAAA,2BAAAmB,wDAAAjB,MAAA;YAAA,OAAAJ,GAAA,CAAArB,KAAA,GAAAyB,MAAA;UAAA,EAAmB,mBAAAkB,gDAAA;YAAA,OAAgDtB,GAAA,CAAA7B,SAAA,CAAU,CAAC,CAAC;UAAA,EAA5D;UAArCR,EAAA,CAAAG,YAAA,EAAqG;UAK7GH,EAAA,CAAAC,cAAA,UAAI;UAEyBD,EAAA,CAAAuC,UAAA,mBAAAqB,4CAAA;YAAA,OAASvB,GAAA,CAAA7B,SAAA,CAAU,CAAC,CAAC;UAAA,EAAC;UAACR,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACxDH,EAAA,CAAAC,cAAA,iBAA6H;UAAtFD,EAAA,CAAAuC,UAAA,2BAAAsB,wDAAApB,MAAA;YAAA,OAAAJ,GAAA,CAAApB,QAAA,GAAAwB,MAAA;UAAA,EAAsB,mBAAAqB,gDAAA;YAAA,OAAgDzB,GAAA,CAAA7B,SAAA,CAAU,CAAC,CAAC;UAAA,EAA5D;UAA7DR,EAAA,CAAAG,YAAA,EAA6H;UAEjIH,EAAA,CAAAC,cAAA,UAAI;UACwBD,EAAA,CAAAuC,UAAA,mBAAAwB,4CAAA;YAAA,OAAS1B,GAAA,CAAA7B,SAAA,CAAU,CAAC,CAAC;UAAA,EAAC;UAACR,EAAA,CAAAE,MAAA,wBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACnEH,EAAA,CAAAC,cAAA,iBAAiJ;UAAnGD,EAAA,CAAAuC,UAAA,2BAAAyB,wDAAAvB,MAAA;YAAA,OAAAJ,GAAA,CAAAnB,eAAA,GAAAuB,MAAA;UAAA,EAA6B,mBAAAwB,gDAAA;YAAA,OAAsD5B,GAAA,CAAA7B,SAAA,CAAU,CAAC,CAAC;UAAA,EAAlE;UAA3ER,EAAA,CAAAG,YAAA,EAAiJ;UAM7JH,EAAA,CAAAC,cAAA,WAAK;UACsBD,EAAA,CAAAuC,UAAA,2BAAA2B,wDAAAzB,MAAA;YAAA,OAAAJ,GAAA,CAAAhB,QAAA,GAAAoB,MAAA;UAAA,EAAsB;UAA7CzC,EAAA,CAAAG,YAAA,EAA0G;UAAAH,EAAA,CAAAC,cAAA,gBAAkC;UAACD,EAAA,CAAAE,MAAA,4EAAmE;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAAAH,EAAA,CAAAsC,SAAA,UAAI;UAE3NtC,EAAA,CAAAC,cAAA,kBAAsE;UAArBD,EAAA,CAAAuC,UAAA,mBAAA4B,iDAAA;YAAA,OAAS9B,GAAA,CAAAb,QAAA,EAAU;UAAA,EAAC;UAACxB,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACrFH,EAAA,CAAAC,cAAA,kBAAiD;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAS;;;UAzDpBH,EAAA,CAAAI,SAAA,IAAsB;UAAtBJ,EAAA,CAAAoE,UAAA,YAAA/B,GAAA,CAAA3B,QAAA,CAAsB;UAOtDV,EAAA,CAAAI,SAAA,GAAoB;UAApBJ,EAAA,CAAAoE,UAAA,YAAA/B,GAAA,CAAA3B,QAAA,CAAoB;UACIV,EAAA,CAAAI,SAAA,GAAyB;UAAzBJ,EAAA,CAAAoE,UAAA,YAAA/B,GAAA,CAAAzB,WAAA,CAAyB,YAAAyB,GAAA,CAAA3B,QAAA,eAAA2B,GAAA,CAAA3B,QAAA;UACxBV,EAAA,CAAAI,SAAA,GAAe;UAAfJ,EAAA,CAAAoE,UAAA,YAAA/B,GAAA,CAAAf,YAAA,CAAe;UAQxCtB,EAAA,CAAAI,SAAA,GAAuB;UAAvBJ,EAAA,CAAAoE,UAAA,YAAA/B,GAAA,CAAAzB,WAAA,CAAuB;UACPZ,EAAA,CAAAI,SAAA,GAAsB;UAAtBJ,EAAA,CAAAoE,UAAA,YAAA/B,GAAA,CAAAa,QAAA,CAAsB,YAAAb,GAAA,CAAAzB,WAAA,eAAAyB,GAAA,CAAAzB,WAAA;UAGtCZ,EAAA,CAAAI,SAAA,GAAoB;UAApBJ,EAAA,CAAAoE,UAAA,YAAA/B,GAAA,CAAAa,QAAA,CAAoB;UACWlD,EAAA,CAAAI,SAAA,GAAoB;UAApBJ,EAAA,CAAAoE,UAAA,YAAA/B,GAAA,CAAAvB,MAAA,CAAoB,YAAAuB,GAAA,CAAAa,QAAA,eAAAb,GAAA,CAAAa,QAAA;UAAmElD,EAAA,CAAAI,SAAA,GAAoB;UAApBJ,EAAA,CAAAoE,UAAA,YAAA/B,GAAA,CAAAa,QAAA,CAAoB;UAC3GlD,EAAA,CAAAI,SAAA,GAAoB;UAApBJ,EAAA,CAAAoE,UAAA,YAAA/B,GAAA,CAAAvB,MAAA,CAAoB,YAAAuB,GAAA,CAAAa,QAAA,eAAAb,GAAA,CAAAa,QAAA;UAAqElD,EAAA,CAAAI,SAAA,GAAoB;UAApBJ,EAAA,CAAAoE,UAAA,YAAA/B,GAAA,CAAAa,QAAA,CAAoB;UAO5IlD,EAAA,CAAAI,SAAA,GAAkB;UAAlBJ,EAAA,CAAAoE,UAAA,YAAA/B,GAAA,CAAAvB,MAAA,CAAkB;UACDd,EAAA,CAAAI,SAAA,GAAmB;UAAnBJ,EAAA,CAAAoE,UAAA,YAAA/B,GAAA,CAAAtB,KAAA,CAAmB,YAAAsB,GAAA,CAAAvB,MAAA,eAAAuB,GAAA,CAAAvB,MAAA;UAGpCd,EAAA,CAAAI,SAAA,GAAiB;UAAjBJ,EAAA,CAAAoE,UAAA,YAAA/B,GAAA,CAAAtB,KAAA,CAAiB;UACFf,EAAA,CAAAI,SAAA,GAAmB;UAAnBJ,EAAA,CAAAoE,UAAA,YAAA/B,GAAA,CAAArB,KAAA,CAAmB,YAAAqB,GAAA,CAAAtB,KAAA,eAAAsB,GAAA,CAAAtB,KAAA;UAOlCf,EAAA,CAAAI,SAAA,GAAiB;UAAjBJ,EAAA,CAAAoE,UAAA,YAAA/B,GAAA,CAAArB,KAAA,CAAiB;UACmBhB,EAAA,CAAAI,SAAA,GAAsB;UAAtBJ,EAAA,CAAAoE,UAAA,YAAA/B,GAAA,CAAApB,QAAA,CAAsB,YAAAoB,GAAA,CAAArB,KAAA,eAAAqB,GAAA,CAAArB,KAAA;UAG1DhB,EAAA,CAAAI,SAAA,GAAoB;UAApBJ,EAAA,CAAAoE,UAAA,YAAA/B,GAAA,CAAApB,QAAA,CAAoB;UACuBjB,EAAA,CAAAI,SAAA,GAA6B;UAA7BJ,EAAA,CAAAoE,UAAA,YAAA/B,GAAA,CAAAnB,eAAA,CAA6B,YAAAmB,GAAA,CAAApB,QAAA,eAAAoB,GAAA,CAAApB,QAAA;UAO5DjB,EAAA,CAAAI,SAAA,GAAsB;UAAtBJ,EAAA,CAAAoE,UAAA,YAAA/B,GAAA,CAAAhB,QAAA,CAAsB,YAAAgB,GAAA,CAAAnB,eAAA,eAAAmB,GAAA,CAAAnB,eAAA;UAAmElB,EAAA,CAAAI,SAAA,GAA2B;UAA3BJ,EAAA,CAAAoE,UAAA,YAAA/B,GAAA,CAAAnB,eAAA,CAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}