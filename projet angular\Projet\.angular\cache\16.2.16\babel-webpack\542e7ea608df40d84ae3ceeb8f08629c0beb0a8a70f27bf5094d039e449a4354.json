{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class AppComponent {\n  constructor() {\n    this.title = 'Projet';\n  }\n  static {\n    this.ɵfac = function AppComponent_Factory(t) {\n      return new (t || AppComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppComponent,\n      selectors: [[\"app-root\"]],\n      decls: 61,\n      vars: 0,\n      consts: [[1, \"container\"], [1, \"content\"], [\"src\", \"assets/women's wear.png\", \"alt\", \"\", \"routerLink\", \"/home\", 1, \"logo\"], [\"routerLink\", \"/home\"], [\"routerLink\", \"/shop\"], [\"routerLink\", \"/sign\"], [1, \"contact\"], [1, \"contact-info\"], [1, \"first-info\"], [\"src\", \"assets/women's wear.png\", \"alt\", \"\"], [1, \"social-icon\"], [\"href\", \"https://www.facebook.com/facebook/\"], [1, \"bx\", \"bxl-facebook-circle\"], [\"href\", \"https://x.com/\"], [1, \"bx\", \"bxl-twitter\"], [\"href\", \"https://www.instagram.com/instagram/\"], [1, \"bx\", \"bxl-instagram\"], [\"href\", \"https://www.youtube.com/@YouTube\"], [1, \"bx\", \"bxl-youtube\"], [\"href\", \"https://www.linkedin.com/feed/\"], [1, \"bx\", \"bxl-linkedin\"], [1, \"second-info\"], [1, \"third-info\"], [1, \"end-text\"]],\n      template: function AppComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"nav\")(1, \"div\", 0)(2, \"div\", 1);\n          i0.ɵɵelement(3, \"img\", 2);\n          i0.ɵɵelementStart(4, \"ul\")(5, \"li\", 3);\n          i0.ɵɵtext(6, \"Home\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"li\", 4);\n          i0.ɵɵtext(8, \"Shop\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"ul\")(10, \"li\", 5);\n          i0.ɵɵtext(11, \"Sign Up\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(12, \"div\", 0);\n          i0.ɵɵelement(13, \"router-outlet\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"section\", 6)(15, \"div\", 7)(16, \"div\", 8);\n          i0.ɵɵelement(17, \"img\", 9);\n          i0.ɵɵelementStart(18, \"p\");\n          i0.ɵɵtext(19, \"123 Olive Street, Sidi Bouzid, 9100\");\n          i0.ɵɵelement(20, \"br\");\n          i0.ɵɵtext(21, \" Tunisia\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"p\");\n          i0.ɵɵtext(23, \"<EMAIL>\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"div\", 10)(25, \"a\", 11);\n          i0.ɵɵelement(26, \"i\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"a\", 13);\n          i0.ɵɵelement(28, \"i\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"a\", 15);\n          i0.ɵɵelement(30, \"i\", 16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"a\", 17);\n          i0.ɵɵelement(32, \"i\", 18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"a\", 19);\n          i0.ɵɵelement(34, \"i\", 20);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(35, \"div\", 21)(36, \"h4\");\n          i0.ɵɵtext(37, \"Support\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"p\");\n          i0.ɵɵtext(39, \"About us\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"p\");\n          i0.ɵɵtext(41, \"Contact us\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"p\");\n          i0.ɵɵtext(43, \"size guide\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"p\");\n          i0.ɵɵtext(45, \"Privacy\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(46, \"p\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"div\", 22)(48, \"h4\");\n          i0.ɵɵtext(49, \"Company\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(50, \"p\");\n          i0.ɵɵtext(51, \"About\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(52, \"p\");\n          i0.ɵɵtext(53, \"Blog\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(54, \"p\");\n          i0.ɵɵtext(55, \"Affiliate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(56, \"p\");\n          i0.ɵɵtext(57, \"Login\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(58, \"div\", 23)(59, \"p\");\n          i0.ɵɵtext(60, \"Copyright \\u00A9 2024 .All Rights Reserved.Designed By ... \");\n          i0.ɵɵelementEnd()();\n        }\n      },\n      dependencies: [i1.RouterOutlet, i1.RouterLink],\n      styles: [\"nav[_ngcontent-%COMP%]{\\n    color:#fff;\\n    top: 0;\\n    z-index: 1000;\\n    width: 100%;\\n    img{\\n        width: 100px;\\n    }\\n    .content{\\n        display: flex;\\n        justify-content: space-between;\\n        align-items: center;\\n        padding:10px 0;\\n        ul{\\n            list-style: none;\\n            display: flex;\\n            justify-content: space-between;\\n            width: auto;\\n            margin: 0;\\n            padding: 0;\\n            li{\\n                cursor: pointer;\\n                color:#000;\\n                text-decoration: none;\\n                margin: 0 10px;\\n                \\n            }\\n            li:hover{\\n                color: #a4936d;\\n                transform: scale(1.1);\\n            }\\n        }\\n    }\\n}\\n.contact[_ngcontent-%COMP%]{\\n    background-color: #fff;\\n    border: #F3F4F6,double;\\n    font-family:'Courier New', Courier, monospace;\\n}\\n.contact-info[_ngcontent-%COMP%]{\\n    display: grid;\\n    grid-template-columns: repeat(auto-fit,minmax(160px,auto));\\n    gap:3rem\\n}\\n.contact-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{\\n    color:#212529;\\n    font-size: 14px;\\n    text-transform: uppercase;\\n    margin-bottom: 10px;\\n}\\n.first-info[_ngcontent-%COMP%]{\\n    img{\\n        width: 140px;\\n        height: auto;\\n\\n    }\\n}\\n.contact-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{\\n    color:#565656;\\n    font-size: 14px;\\n    font-weight: 400;\\n    text-transform: capitalize;\\n    line-height: 1.5;\\n    margin-bottom: 10px;\\n    cursor: pointer;\\n    transition: all .42s;\\n}\\n.contact-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:hover{\\n    color: #a4936d;\\n}\\n.social-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{\\n    color: #565656;\\n    margin-right: 10px;\\n    font-size: 20px;\\n    transition: all .42s;\\n\\n}\\n.social-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]:hover{\\n    color: rgb(23, 23, 127);\\n    transform: scale(1.3);\\n}\\n.end-text[_ngcontent-%COMP%]{\\n    background-color: #edfff1;\\n    text-align: center;\\n}\\n        \\n\\n\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["AppComponent", "constructor", "title", "selectors", "decls", "vars", "consts", "template", "AppComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd"], "sources": ["C:\\Users\\<USER>\\Desktop\\projet angular\\Projet\\src\\app\\app.component.ts", "C:\\Users\\<USER>\\Desktop\\projet angular\\Projet\\src\\app\\app.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-root',\n  templateUrl: './app.component.html',\n  styleUrls: ['./app.component.css']\n})\nexport class AppComponent {\n  title = 'Projet';\n}\n", "<nav>\n    <div class=\"container\">\n        <div class=\"content\">\n            <img class=\"logo\" src=\"assets/women's wear.png\" alt=\"\"routerLink=\"/home\">\n            <ul>\n                <li routerLink=\"/home\">Home</li>\n                <li routerLink=\"/shop\" >Shop</li>\n            </ul>\n            <ul> \n                <li routerLink=\"/sign\">Sign Up</li>\n            </ul>\n        </div>\n    </div>\n</nav>\n<div class=\"container\"><router-outlet></router-outlet>\n</div>\n<section class=\"contact\">\n    <div class=\"contact-info\">\n        <div class=\"first-info\">\n            <img src=\"assets/women's wear.png\" alt=\"\">\n            <p>123 Olive Street, Sidi Bouzid, 9100<br> Tunisia</p>\n            <p><EMAIL></p>\n            <div class=\"social-icon\">\n                <a href=\"https://www.facebook.com/facebook/\"><i class='bx bxl-facebook-circle'></i></a>\n                <a href=\"https://x.com/\"><i class='bx bxl-twitter'></i></a>\n                <a href=\"https://www.instagram.com/instagram/\"><i class='bx bxl-instagram'></i></a>\n                <a href=\"https://www.youtube.com/@YouTube\"><i class='bx bxl-youtube'></i></a>\n                <a href=\"https://www.linkedin.com/feed/\"><i class='bx bxl-linkedin'></i></a>\n            </div>\n        </div>\n        <div class=\"second-info\">\n            <h4>Support</h4>\n            <p>About us</p>\n            <p>Contact us</p>\n            <p>size guide</p>\n            <p>Privacy</p>\n            <p></p>\n        </div>\n        <div class=\"third-info\">\n            <h4>Company</h4>\n            <p>About</p>\n            <p>Blog</p>\n            <p>Affiliate</p>\n            <p>Login</p>\n        </div>\n    </div>\n</section>\n<div class=\"end-text\">\n    <p>Copyright © 2024 .All Rights Reserved.Designed By ... </p>\n</div>"], "mappings": ";;AAOA,OAAM,MAAOA,YAAY;EALzBC,YAAA;IAME,KAAAC,KAAK,GAAG,QAAQ;;;;uBADLF,YAAY;IAAA;EAAA;;;YAAZA,YAAY;MAAAG,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCPzBE,EAAA,CAAAC,cAAA,UAAK;UAGOD,EAAA,CAAAE,SAAA,aAAyE;UACzEF,EAAA,CAAAC,cAAA,SAAI;UACuBD,EAAA,CAAAG,MAAA,WAAI;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAChCJ,EAAA,CAAAC,cAAA,YAAwB;UAAAD,EAAA,CAAAG,MAAA,WAAI;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAErCJ,EAAA,CAAAC,cAAA,SAAI;UACuBD,EAAA,CAAAG,MAAA,eAAO;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAKnDJ,EAAA,CAAAC,cAAA,cAAuB;UAAAD,EAAA,CAAAE,SAAA,qBAA+B;UACtDF,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAC,cAAA,kBAAyB;UAGbD,EAAA,CAAAE,SAAA,cAA0C;UAC1CF,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,2CAAmC;UAAAH,EAAA,CAAAE,SAAA,UAAI;UAACF,EAAA,CAAAG,MAAA,gBAAO;UAAAH,EAAA,CAAAI,YAAA,EAAI;UACtDJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,qCAA6B;UAAAH,EAAA,CAAAI,YAAA,EAAI;UACpCJ,EAAA,CAAAC,cAAA,eAAyB;UACwBD,EAAA,CAAAE,SAAA,aAAsC;UAAAF,EAAA,CAAAI,YAAA,EAAI;UACvFJ,EAAA,CAAAC,cAAA,aAAyB;UAAAD,EAAA,CAAAE,SAAA,aAA8B;UAAAF,EAAA,CAAAI,YAAA,EAAI;UAC3DJ,EAAA,CAAAC,cAAA,aAA+C;UAAAD,EAAA,CAAAE,SAAA,aAAgC;UAAAF,EAAA,CAAAI,YAAA,EAAI;UACnFJ,EAAA,CAAAC,cAAA,aAA2C;UAAAD,EAAA,CAAAE,SAAA,aAA8B;UAAAF,EAAA,CAAAI,YAAA,EAAI;UAC7EJ,EAAA,CAAAC,cAAA,aAAyC;UAAAD,EAAA,CAAAE,SAAA,aAA+B;UAAAF,EAAA,CAAAI,YAAA,EAAI;UAGpFJ,EAAA,CAAAC,cAAA,eAAyB;UACjBD,EAAA,CAAAG,MAAA,eAAO;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAChBJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,gBAAQ;UAAAH,EAAA,CAAAI,YAAA,EAAI;UACfJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,kBAAU;UAAAH,EAAA,CAAAI,YAAA,EAAI;UACjBJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,kBAAU;UAAAH,EAAA,CAAAI,YAAA,EAAI;UACjBJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,eAAO;UAAAH,EAAA,CAAAI,YAAA,EAAI;UACdJ,EAAA,CAAAE,SAAA,SAAO;UACXF,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAC,cAAA,eAAwB;UAChBD,EAAA,CAAAG,MAAA,eAAO;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAChBJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,aAAK;UAAAH,EAAA,CAAAI,YAAA,EAAI;UACZJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,YAAI;UAAAH,EAAA,CAAAI,YAAA,EAAI;UACXJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,iBAAS;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAChBJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,aAAK;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAIxBJ,EAAA,CAAAC,cAAA,eAAsB;UACfD,EAAA,CAAAG,MAAA,mEAAsD;UAAAH,EAAA,CAAAI,YAAA,EAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}