<div class="register" align="center">
    <img src="assets/women's wear.png" alt="Logo" />
</div>
<div class="container" align="center">
    <fieldset>
        <legend>Register</legend>
        <form>
            <table>
                <!-- Username Field -->
                <tr>
                    <td>
                        <p>Username</p>
                        <input type="text" name="username" [(ngModel)]="username" (input)="checkStep(1)" />
                    </td>
                </tr>
            
                <!-- Governorate Field -->
                <tr>
                    <td colspan="2" align="center">
                        <p [hidden]="!username">Governorate</p>
                        <select name="governorate" [(ngModel)]="governorate" [hidden]="!username" [disabled]="!username" (change)="checkStep(2)">
                            <option *ngFor="let gov of governorates">{{ gov }}</option>
                        </select>
                    </td>
                </tr>
            
                <!-- Gender Fields -->
                <tr>
                    <td colspan="2">
                        <p [hidden]="!governorate" >Gender</p>
                        <input type="radio" name="gender" [(ngModel)]="gender" value="Male" [hidden]="!governorate"  [disabled]="!governorate" (change)="checkStep(3)" /> <span  [hidden]="!governorate"  (change)="checkStep(3)" >Male</span>
                        <input type="radio" name="gender" [(ngModel)]="gender" value="Female" [hidden]="!governorate"  [disabled]="!governorate" (change)="checkStep(3)" /> <span  [hidden]="!governorate"  (change)="checkStep(3)" >Female</span>
                    </td>
                </tr>
            
                <!-- Email and Phone Fields -->
                <tr>
                    <td>
                        <p [hidden]="!gender">Mail account</p>
                        <input type="text" [(ngModel)]="email" [hidden]="!gender"[disabled]="!gender"  (input)="checkStep(4)" />
                    </td>
                    <td>
                        <p [hidden]="!gender">Phone number</p>
                        <input type="text" [(ngModel)]="phone" [hidden]="!gender" [disabled]="!gender" (input)="checkStep(4)" />
                    </td>
                </tr>
            
                <!-- Password Fields -->
                <tr>
                    <td>
                        <p [hidden]="!gender">Password</p>
                        <input type="password" name="password" [(ngModel)]="password" [hidden]="!gender" [disabled]="!gender" (input)="checkStep(6)" />
                    </td>
                    <td>
                        <p [hidden]="!password">Confirm Password</p>
                        <input type="password" name="confirmPassword" [(ngModel)]="confirmPassword" [hidden]="!password" [disabled]="!password" (input)="checkStep(6)" />
                    </td>
                </tr>
            </table>
            

            <!-- Terms and Submit -->
            <div>
                <button type="button" style="padding: 5px 45px;" [disabled]="!confirmPassword"(click)="onSubmit()">Submit</button>
                <button type="button" style="padding: 5px 45px;">Reset</button>
            </div>
            <div *ngIf="errorMessage" style="color: red;">
                {{ errorMessage }}
              </div>
        </form>
    </fieldset>
</div>
