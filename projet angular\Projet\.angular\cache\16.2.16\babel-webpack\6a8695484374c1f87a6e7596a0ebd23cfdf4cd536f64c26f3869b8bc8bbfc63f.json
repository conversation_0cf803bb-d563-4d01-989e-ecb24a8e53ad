{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common/http\";\nimport * as i3 from \"@angular/common\";\nfunction HomeComponent_div_17_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵelement(1, \"img\", 28);\n    i0.ɵɵelementStart(2, \"div\", 29)(3, \"span\", 30);\n    i0.ɵɵtext(4, \"Sale\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 31)(6, \"div\", 32);\n    i0.ɵɵelement(7, \"i\", 33)(8, \"i\", 33)(9, \"i\", 33)(10, \"i\", 33)(11, \"i\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 35)(13, \"h4\");\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"titlecase\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"h4\");\n    i0.ɵɵtext(17);\n    i0.ɵɵpipe(18, \"slice\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"p\");\n    i0.ɵɵtext(20);\n    i0.ɵɵpipe(21, \"currency\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const item_r2 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate(\"alt\", item_r2.title);\n    i0.ɵɵproperty(\"src\", item_r2.imgSrc, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(13);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(15, 6, item_r2.title));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", i0.ɵɵpipeBind3(18, 8, item_r2.description, 0, 50), \"\", item_r2.description.length > 50 ? \"...\" : \"\", \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind3(21, 12, item_r2.price, \"TND\", \"symbol\"));\n  }\n}\nfunction HomeComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵtemplate(1, HomeComponent_div_17_div_1_Template, 22, 16, \"div\", 26);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.items);\n  }\n}\nexport class HomeComponent {\n  constructor(router, http) {\n    this.router = router;\n    this.http = http;\n    this.items = [];\n  }\n  ngOnInit() {\n    this.http.get('assets/product.json').subscribe({\n      next: response => {\n        this.items = response.products;\n        console.log('Loaded items:', this.items);\n      },\n      error: err => {\n        console.error('Error loading JSON:', err);\n        // Add proper error handling\n        this.items = []; // Provide fallback empty array\n      }\n    });\n  }\n\n  navigateToShop() {\n    this.router.navigate(['/shop']);\n  }\n  static {\n    this.ɵfac = function HomeComponent_Factory(t) {\n      return new (t || HomeComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.HttpClient));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HomeComponent,\n      selectors: [[\"app-home\"]],\n      decls: 77,\n      vars: 1,\n      consts: [[1, \"section\"], [1, \"btn-shop\", 3, \"click\"], [1, \"trending-product\"], [1, \"center-text\"], [\"class\", \"grid-Products\", 4, \"ngIf\"], [1, \"client-reviews\"], [1, \"reviews\"], [\"src\", \"assets/woman.jpg\", \"alt\", \"\"], [1, \"contact\"], [1, \"contact-info\"], [1, \"first-info\"], [\"src\", \"assets/women's wear.png\", \"alt\", \"\"], [1, \"social-icon\"], [\"href\", \"https://www.facebook.com/facebook/\"], [1, \"bx\", \"bxl-facebook-circle\"], [\"href\", \"https://x.com/\"], [1, \"bx\", \"bxl-twitter\"], [\"href\", \"https://www.instagram.com/instagram/\"], [1, \"bx\", \"bxl-instagram\"], [\"href\", \"https://www.youtube.com/@YouTube\"], [1, \"bx\", \"bxl-youtube\"], [\"href\", \"https://www.linkedin.com/feed/\"], [1, \"bx\", \"bxl-linkedin\"], [1, \"second-info\"], [1, \"third-info\"], [1, \"grid-Products\"], [\"class\", \"grid-row\", 4, \"ngFor\", \"ngForOf\"], [1, \"grid-row\"], [3, \"src\", \"alt\"], [1, \"product-text\"], [1, \"badge\"], [1, \"product-info\"], [1, \"ratting\"], [1, \"bx\", \"bxs-star\"], [1, \"bx\", \"bxs-star-half\"], [1, \"price\"]],\n      template: function HomeComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"h5\");\n          i0.ɵɵtext(2, \"2025 collection\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"h1\");\n          i0.ɵɵtext(4, \"New\");\n          i0.ɵɵelement(5, \"br\");\n          i0.ɵɵtext(6, \"collection 2025\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"h6\");\n          i0.ɵɵtext(8, \"There's nothing like trend\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"p\", 1);\n          i0.ɵɵlistener(\"click\", function HomeComponent_Template_p_click_9_listener() {\n            return ctx.navigateToShop();\n          });\n          i0.ɵɵtext(10, \"Shop now\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(11, \"div\", 2)(12, \"div\", 3)(13, \"h2\");\n          i0.ɵɵtext(14, \"Our tranding \");\n          i0.ɵɵelementStart(15, \"span\");\n          i0.ɵɵtext(16, \"Products\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(17, HomeComponent_div_17_Template, 2, 1, \"div\", 4);\n          i0.ɵɵelement(18, \"br\")(19, \"br\");\n          i0.ɵɵelementStart(20, \"section\", 5)(21, \"div\", 6)(22, \"h3\");\n          i0.ɵɵtext(23, \"Client Reviews\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(24, \"img\", 7);\n          i0.ɵɵelementStart(25, \"p\");\n          i0.ɵɵtext(26, \"Hello, my name is Sarah, and I\\u2019ve been shopping for stylish and comfortable women\\u2019s clothing online for years. I\\u2019m always looking for outfits that reflect my personality\\u2014elegant yet practical for my busy lifestyle. I appreciate a website that's easy to navigate with clear size guides, diverse collections, and quality fabrics. Fast delivery and excellent customer service are also a must for me. A good online shopping experience keeps me coming back for more!\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"h2\");\n          i0.ɵɵtext(28, \"Sarah\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"p\");\n          i0.ɵɵtext(30, \"CEO of Addle\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelement(31, \"br\")(32, \"br\");\n          i0.ɵɵelementStart(33, \"section\", 8)(34, \"div\", 9)(35, \"div\", 10);\n          i0.ɵɵelement(36, \"img\", 11);\n          i0.ɵɵelementStart(37, \"p\");\n          i0.ɵɵtext(38, \"123 Olive Street, Sidi Bouzid, 9100\");\n          i0.ɵɵelement(39, \"br\");\n          i0.ɵɵtext(40, \" Tunisia\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"p\");\n          i0.ɵɵtext(42, \"<EMAIL>\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"div\", 12)(44, \"a\", 13);\n          i0.ɵɵelement(45, \"i\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"a\", 15);\n          i0.ɵɵelement(47, \"i\", 16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(48, \"a\", 17);\n          i0.ɵɵelement(49, \"i\", 18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(50, \"a\", 19);\n          i0.ɵɵelement(51, \"i\", 20);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(52, \"a\", 21);\n          i0.ɵɵelement(53, \"i\", 22);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(54, \"div\", 23)(55, \"h4\");\n          i0.ɵɵtext(56, \"Support\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(57, \"p\");\n          i0.ɵɵtext(58, \"About us\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(59, \"p\");\n          i0.ɵɵtext(60, \"Contact us\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(61, \"p\");\n          i0.ɵɵtext(62, \"size guide\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(63, \"p\");\n          i0.ɵɵtext(64, \"Privacy\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(65, \"p\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(66, \"div\", 24)(67, \"h4\");\n          i0.ɵɵtext(68, \"Company\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(69, \"p\");\n          i0.ɵɵtext(70, \"About\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(71, \"p\");\n          i0.ɵɵtext(72, \"Blog\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(73, \"p\");\n          i0.ɵɵtext(74, \"Affiliate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(75, \"p\");\n          i0.ɵɵtext(76, \"Login\");\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(17);\n          i0.ɵɵproperty(\"ngIf\", ctx.items.length > 0);\n        }\n      },\n      dependencies: [i3.NgForOf, i3.NgIf, i3.SlicePipe, i3.TitleCasePipe, i3.CurrencyPipe],\n      styles: [\"*[_ngcontent-%COMP%]{\\n    margin: 0;\\n    padding: 0;\\n    box-sizing: border-box;\\n    scroll-behavior: smooth;\\n    font-family: 'sans-serif';\\n    list-style: none;\\n    text-decoration: none;\\n}\\n\\n\\n.section[_ngcontent-%COMP%] {\\n  padding: 5% 10%;\\n  width: 100%;\\n  min-height: 80vh;\\n  background-image: linear-gradient(rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.8)), url('banner-3.png');\\n  background-position: center;\\n  background-size: cover;\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: center;\\n}\\n\\n.section[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  color: var(--accent);\\n  font-size: 18px;\\n  text-transform: uppercase;\\n  letter-spacing: 2px;\\n  font-weight: 500;\\n}\\n\\n.section[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  color: var(--primary);\\n  font-size: 3.5rem;\\n  line-height: 1.1;\\n  font-weight: 700;\\n  margin: 15px 0;\\n}\\n\\n.section[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  color: var(--secondary);\\n  font-size: 20px;\\n  font-style: italic;\\n  margin-bottom: 30px;\\n}\\n\\n.section[_ngcontent-%COMP%]   .btn-shop[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  padding: 12px 30px;\\n  background-color: transparent;\\n  border: 2px solid var(--primary);\\n  color: var(--primary);\\n  font-weight: 600;\\n  text-transform: uppercase;\\n  letter-spacing: 1px;\\n  cursor: pointer;\\n  transition: var(--transition);\\n}\\n\\n.section[_ngcontent-%COMP%]   .btn-shop[_ngcontent-%COMP%]:hover {\\n  background-color: var(--primary);\\n  color: white;\\n}\\n\\n\\n.center-text[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin: 60px 0 40px;\\n}\\n\\n.center-text[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 32px;\\n  position: relative;\\n  display: inline-block;\\n}\\n\\n.center-text[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]::after {\\n  content: '';\\n  position: absolute;\\n  bottom: -10px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  width: 70px;\\n  height: 3px;\\n  background-color: var(--accent);\\n}\\n\\n.center-text[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  color: var(--accent);\\n}\\n\\n.grid-Products[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\\n  gap: 30px;\\n  padding: 0 20px;\\n}\\n\\n.grid-row[_ngcontent-%COMP%] {\\n  background-color: white;\\n  border-radius: var(--border-radius);\\n  overflow: hidden;\\n  box-shadow: var(--shadow);\\n  transition: var(--transition);\\n}\\n\\n.grid-row[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-10px);\\n  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);\\n}\\n\\n.grid-row[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 250px;\\n  object-fit: cover;\\n  transition: var(--transition);\\n}\\n\\n.grid-row[_ngcontent-%COMP%]:hover   img[_ngcontent-%COMP%] {\\n  transform: scale(1.05);\\n}\\n\\n\\n.client-reviews[_ngcontent-%COMP%] {\\n  background-color: var(--light);\\n  padding: 80px 20px;\\n  margin-top: 80px;\\n}\\n\\n.reviews[_ngcontent-%COMP%] {\\n  max-width: 800px;\\n  margin: 0 auto;\\n  text-align: center;\\n}\\n\\n.reviews[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 32px;\\n  margin-bottom: 40px;\\n  position: relative;\\n  display: inline-block;\\n}\\n\\n.reviews[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]::after {\\n  content: '';\\n  position: absolute;\\n  bottom: -10px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  width: 50px;\\n  height: 3px;\\n  background-color: var(--accent);\\n}\\n\\n.reviews[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100px;\\n  height: 100px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n  border: 5px solid white;\\n  box-shadow: var(--shadow);\\n  margin: 20px auto;\\n}\\n\\n.reviews[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 16px;\\n  line-height: 1.8;\\n  margin-bottom: 20px;\\n  font-style: italic;\\n}\\n\\n.reviews[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 22px;\\n  color: var(--primary);\\n  margin-bottom: 5px;\\n}\\n\\n\\n.contact[_ngcontent-%COMP%] {\\n  padding: 80px 20px;\\n  background-color: white;\\n}\\n\\n.contact-info[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n  gap: 40px;\\n}\\n\\n.contact-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: var(--primary);\\n  font-size: 18px;\\n  text-transform: uppercase;\\n  margin-bottom: 20px;\\n  position: relative;\\n}\\n\\n.contact-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]::after {\\n  content: '';\\n  position: absolute;\\n  bottom: -8px;\\n  left: 0;\\n  width: 40px;\\n  height: 2px;\\n  background-color: var(--accent);\\n}\\n\\n.first-info[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 140px;\\n  margin-bottom: 20px;\\n}\\n\\n.contact-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  margin-bottom: 10px;\\n  transition: var(--transition);\\n}\\n\\n.contact-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:hover {\\n  color: var(--accent);\\n  transform: translateX(5px);\\n}\\n\\n.social-icon[_ngcontent-%COMP%] {\\n  display: flex;\\n  margin-top: 20px;\\n}\\n\\n.social-icon[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 40px;\\n  height: 40px;\\n  background-color: var(--light);\\n  border-radius: 50%;\\n  margin-right: 10px;\\n  transition: var(--transition);\\n}\\n\\n.social-icon[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  background-color: var(--accent);\\n  color: white;\\n  transform: translateY(-5px);\\n}\\n\\n.social-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n}\\n\\n@media (max-width: 768px) {\\n  .section[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n    font-size: 2.5rem;\\n  }\\n  \\n  .grid-Products[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\\n  }\\n}\\n.end-text[_ngcontent-%COMP%]{\\n    background-color: #edfff1;\\n    text-align: center;\\n    padding: 20px;\\n}\\n.end-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{\\n    color: #111;\\n    text-transform: capitalize;\\n\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵpropertyInterpolate", "item_r2", "title", "ɵɵproperty", "imgSrc", "ɵɵsanitizeUrl", "ɵɵtextInterpolate", "ɵɵpipeBind1", "ɵɵtextInterpolate2", "ɵɵpipeBind3", "description", "length", "price", "ɵɵtemplate", "HomeComponent_div_17_div_1_Template", "ctx_r0", "items", "HomeComponent", "constructor", "router", "http", "ngOnInit", "get", "subscribe", "next", "response", "products", "console", "log", "error", "err", "navigateToShop", "navigate", "ɵɵdirectiveInject", "i1", "Router", "i2", "HttpClient", "selectors", "decls", "vars", "consts", "template", "HomeComponent_Template", "rf", "ctx", "ɵɵlistener", "HomeComponent_Template_p_click_9_listener", "HomeComponent_div_17_Template"], "sources": ["C:\\Users\\<USER>\\Desktop\\projet angular\\Projet\\src\\home\\home.component.ts", "C:\\Users\\<USER>\\Desktop\\projet angular\\Projet\\src\\home\\home.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { HttpClient } from '@angular/common/http';\n\n// Define the Produit interface\ninterface Produit {\n  id: number;\n  title: string;\n  description: string;\n  imgSrc: string;\n}\n\n@Component({\n  selector: 'app-home',\n  templateUrl: './home.component.html',\n  styleUrls: ['./home.component.css']\n})\nexport class HomeComponent implements OnInit {\n  items: Produit[] = [];\n\n  constructor(private router: Router, private http: HttpClient) {}\n\n  ngOnInit() {\n    this.http.get<{products: Produit[]}>('assets/product.json').subscribe({\n      next: (response: {products: Produit[]}) => {\n        this.items = response.products;\n        console.log('Loaded items:', this.items);\n      },\n      error: (err: any) => {\n        console.error('Error loading JSON:', err);\n        // Add proper error handling\n        this.items = []; // Provide fallback empty array\n      }\n    });\n  }\n\n  navigateToShop() {\n    this.router.navigate(['/shop']);\n  }\n}\n\n\n\n", "<div class=\"section\">\n    <h5>2025 collection</h5>\n    <h1>New<br>collection 2025</h1>\n    <h6>There's nothing like trend</h6>\n    <p class=\"btn-shop\" (click)=\"navigateToShop()\">Shop now</p>\n</div>\n\n<div class=\"trending-product\">\n    <div class=\"center-text\">\n        <h2>Our tranding <span>Products</span></h2>\n    </div>\n    \n    <div class=\"grid-Products\" *ngIf=\"items.length > 0\">\n        <div class=\"grid-row\" *ngFor=\"let item of items\">\n            <img [src]=\"item.imgSrc\" alt=\"{{ item.title }}\">\n\n            <div class=\"product-text\">\n                <span class=\"badge\">Sale</span>\n            </div>\n\n            <div class=\"product-info\">\n                <div class=\"ratting\">\n                    <i class='bx bxs-star'></i>\n                    <i class='bx bxs-star'></i>\n                    <i class='bx bxs-star'></i>\n                    <i class='bx bxs-star'></i>\n                    <i class='bx bxs-star-half'></i>\n                </div>\n\n                <div class=\"price\">\n                    <h4>{{ item.title | titlecase }}</h4>\n                    <h4>{{ item.description | slice:0:50 }}{{ item.description.length > 50 ? '...' : '' }}</h4>\n                    <p>{{ item.price | currency:'TND':'symbol' }}</p>\n\n                </div>\n            </div>\n        </div>\n    </div>\n\n    <br><br>\n    <section class=\"client-reviews\">\n        <div class=\"reviews\">\n            <h3>Client Reviews</h3>\n            <img src=\"assets/woman.jpg\"  alt=\"\">\n            <p>Hello, my name is Sarah, and I’ve been shopping for stylish and comfortable women’s clothing online for years. I’m always looking for outfits that reflect my personality—elegant yet practical for my busy lifestyle. I appreciate a website that's easy to navigate with clear size guides, diverse collections, and quality fabrics. Fast delivery and excellent customer service are also a must for me. A good online shopping experience keeps me coming back for more!</p>\n            <h2>Sarah</h2>\n            <p>CEO of Addle</p>\n        </div>\n    </section>\n</div>\n<br><br>\n<section class=\"contact\">\n    <div class=\"contact-info\">\n        <div class=\"first-info\">\n            <img src=\"assets/women's wear.png\" alt=\"\">\n            <p>123 Olive Street, Sidi Bouzid, 9100<br> Tunisia</p>\n            <p>Herwardrobe&#64;gmail.com</p>\n            <div class=\"social-icon\">\n                <a href=\"https://www.facebook.com/facebook/\"><i class='bx bxl-facebook-circle'></i></a>\n                <a href=\"https://x.com/\"><i class='bx bxl-twitter'></i></a>\n                <a href=\"https://www.instagram.com/instagram/\"><i class='bx bxl-instagram'></i></a>\n                <a href=\"https://www.youtube.com/@YouTube\"><i class='bx bxl-youtube'></i></a>\n                <a href=\"https://www.linkedin.com/feed/\"><i class='bx bxl-linkedin'></i></a>\n            </div>\n        </div>\n        <div class=\"second-info\">\n            <h4>Support</h4>\n            <p>About us</p>\n            <p>Contact us</p>\n            <p>size guide</p>\n            <p>Privacy</p>\n            <p></p>\n        </div>\n        <div class=\"third-info\">\n            <h4>Company</h4>\n            <p>About</p>\n            <p>Blog</p>\n            <p>Affiliate</p>\n            <p>Login</p>\n        </div>\n    </div>\n</section>\n\n\n"], "mappings": ";;;;;;ICaQA,EAAA,CAAAC,cAAA,cAAiD;IAC7CD,EAAA,CAAAE,SAAA,cAAgD;IAEhDF,EAAA,CAAAC,cAAA,cAA0B;IACFD,EAAA,CAAAG,MAAA,WAAI;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAGnCJ,EAAA,CAAAC,cAAA,cAA0B;IAElBD,EAAA,CAAAE,SAAA,YAA2B;IAK/BF,EAAA,CAAAI,YAAA,EAAM;IAENJ,EAAA,CAAAC,cAAA,eAAmB;IACXD,EAAA,CAAAG,MAAA,IAA4B;;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACrCJ,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAG,MAAA,IAAkF;;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC3FJ,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAG,MAAA,IAA0C;;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;IAlBhCJ,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAAM,qBAAA,QAAAC,OAAA,CAAAC,KAAA,CAAsB;IAA1CR,EAAA,CAAAS,UAAA,QAAAF,OAAA,CAAAG,MAAA,EAAAV,EAAA,CAAAW,aAAA,CAAmB;IAgBZX,EAAA,CAAAK,SAAA,IAA4B;IAA5BL,EAAA,CAAAY,iBAAA,CAAAZ,EAAA,CAAAa,WAAA,QAAAN,OAAA,CAAAC,KAAA,EAA4B;IAC5BR,EAAA,CAAAK,SAAA,GAAkF;IAAlFL,EAAA,CAAAc,kBAAA,KAAAd,EAAA,CAAAe,WAAA,QAAAR,OAAA,CAAAS,WAAA,cAAAT,OAAA,CAAAS,WAAA,CAAAC,MAAA,uBAAkF;IACnFjB,EAAA,CAAAK,SAAA,GAA0C;IAA1CL,EAAA,CAAAY,iBAAA,CAAAZ,EAAA,CAAAe,WAAA,SAAAR,OAAA,CAAAW,KAAA,mBAA0C;;;;;IApB7DlB,EAAA,CAAAC,cAAA,cAAoD;IAChDD,EAAA,CAAAmB,UAAA,IAAAC,mCAAA,oBAuBM;IACVpB,EAAA,CAAAI,YAAA,EAAM;;;;IAxBqCJ,EAAA,CAAAK,SAAA,GAAQ;IAARL,EAAA,CAAAS,UAAA,YAAAY,MAAA,CAAAC,KAAA,CAAQ;;;ADIvD,OAAM,MAAOC,aAAa;EAGxBC,YAAoBC,MAAc,EAAUC,IAAgB;IAAxC,KAAAD,MAAM,GAANA,MAAM;IAAkB,KAAAC,IAAI,GAAJA,IAAI;IAFhD,KAAAJ,KAAK,GAAc,EAAE;EAE0C;EAE/DK,QAAQA,CAAA;IACN,IAAI,CAACD,IAAI,CAACE,GAAG,CAAwB,qBAAqB,CAAC,CAACC,SAAS,CAAC;MACpEC,IAAI,EAAGC,QAA+B,IAAI;QACxC,IAAI,CAACT,KAAK,GAAGS,QAAQ,CAACC,QAAQ;QAC9BC,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,IAAI,CAACZ,KAAK,CAAC;MAC1C,CAAC;MACDa,KAAK,EAAGC,GAAQ,IAAI;QAClBH,OAAO,CAACE,KAAK,CAAC,qBAAqB,EAAEC,GAAG,CAAC;QACzC;QACA,IAAI,CAACd,KAAK,GAAG,EAAE,CAAC,CAAC;MACnB;KACD,CAAC;EACJ;;EAEAe,cAAcA,CAAA;IACZ,IAAI,CAACZ,MAAM,CAACa,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;EACjC;;;uBArBWf,aAAa,EAAAvB,EAAA,CAAAuC,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAzC,EAAA,CAAAuC,iBAAA,CAAAG,EAAA,CAAAC,UAAA;IAAA;EAAA;;;YAAbpB,aAAa;MAAAqB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCjB1BlD,EAAA,CAAAC,cAAA,aAAqB;UACbD,EAAA,CAAAG,MAAA,sBAAe;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACxBJ,EAAA,CAAAC,cAAA,SAAI;UAAAD,EAAA,CAAAG,MAAA,UAAG;UAAAH,EAAA,CAAAE,SAAA,SAAI;UAAAF,EAAA,CAAAG,MAAA,sBAAe;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAC/BJ,EAAA,CAAAC,cAAA,SAAI;UAAAD,EAAA,CAAAG,MAAA,iCAA0B;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACnCJ,EAAA,CAAAC,cAAA,WAA+C;UAA3BD,EAAA,CAAAoD,UAAA,mBAAAC,0CAAA;YAAA,OAASF,GAAA,CAAAd,cAAA,EAAgB;UAAA,EAAC;UAACrC,EAAA,CAAAG,MAAA,gBAAQ;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAG/DJ,EAAA,CAAAC,cAAA,cAA8B;UAElBD,EAAA,CAAAG,MAAA,qBAAa;UAAAH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAG,MAAA,gBAAQ;UAAAH,EAAA,CAAAI,YAAA,EAAO;UAG1CJ,EAAA,CAAAmB,UAAA,KAAAmC,6BAAA,iBAyBM;UAENtD,EAAA,CAAAE,SAAA,UAAI;UACJF,EAAA,CAAAC,cAAA,kBAAgC;UAEpBD,EAAA,CAAAG,MAAA,sBAAc;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACvBJ,EAAA,CAAAE,SAAA,cAAoC;UACpCF,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,yeAA6c;UAAAH,EAAA,CAAAI,YAAA,EAAI;UACpdJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,aAAK;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACdJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,oBAAY;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAI/BJ,EAAA,CAAAE,SAAA,UAAI;UACJF,EAAA,CAAAC,cAAA,kBAAyB;UAGbD,EAAA,CAAAE,SAAA,eAA0C;UAC1CF,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,2CAAmC;UAAAH,EAAA,CAAAE,SAAA,UAAI;UAACF,EAAA,CAAAG,MAAA,gBAAO;UAAAH,EAAA,CAAAI,YAAA,EAAI;UACtDJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,6BAAyB;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAChCJ,EAAA,CAAAC,cAAA,eAAyB;UACwBD,EAAA,CAAAE,SAAA,aAAsC;UAAAF,EAAA,CAAAI,YAAA,EAAI;UACvFJ,EAAA,CAAAC,cAAA,aAAyB;UAAAD,EAAA,CAAAE,SAAA,aAA8B;UAAAF,EAAA,CAAAI,YAAA,EAAI;UAC3DJ,EAAA,CAAAC,cAAA,aAA+C;UAAAD,EAAA,CAAAE,SAAA,aAAgC;UAAAF,EAAA,CAAAI,YAAA,EAAI;UACnFJ,EAAA,CAAAC,cAAA,aAA2C;UAAAD,EAAA,CAAAE,SAAA,aAA8B;UAAAF,EAAA,CAAAI,YAAA,EAAI;UAC7EJ,EAAA,CAAAC,cAAA,aAAyC;UAAAD,EAAA,CAAAE,SAAA,aAA+B;UAAAF,EAAA,CAAAI,YAAA,EAAI;UAGpFJ,EAAA,CAAAC,cAAA,eAAyB;UACjBD,EAAA,CAAAG,MAAA,eAAO;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAChBJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,gBAAQ;UAAAH,EAAA,CAAAI,YAAA,EAAI;UACfJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,kBAAU;UAAAH,EAAA,CAAAI,YAAA,EAAI;UACjBJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,kBAAU;UAAAH,EAAA,CAAAI,YAAA,EAAI;UACjBJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,eAAO;UAAAH,EAAA,CAAAI,YAAA,EAAI;UACdJ,EAAA,CAAAE,SAAA,SAAO;UACXF,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAC,cAAA,eAAwB;UAChBD,EAAA,CAAAG,MAAA,eAAO;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAChBJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,aAAK;UAAAH,EAAA,CAAAI,YAAA,EAAI;UACZJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,YAAI;UAAAH,EAAA,CAAAI,YAAA,EAAI;UACXJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,iBAAS;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAChBJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,aAAK;UAAAH,EAAA,CAAAI,YAAA,EAAI;;;UAlEQJ,EAAA,CAAAK,SAAA,IAAsB;UAAtBL,EAAA,CAAAS,UAAA,SAAA0C,GAAA,CAAA7B,KAAA,CAAAL,MAAA,KAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}