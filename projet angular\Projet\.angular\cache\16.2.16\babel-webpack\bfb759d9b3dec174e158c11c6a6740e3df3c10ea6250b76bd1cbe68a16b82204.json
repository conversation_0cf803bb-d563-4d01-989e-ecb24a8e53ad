{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/forms\";\nfunction ShopComponent_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"div\", 15)(2, \"div\", 16);\n    i0.ɵɵelement(3, \"img\", 17);\n    i0.ɵɵelementStart(4, \"div\", 18)(5, \"h4\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 19)(10, \"button\", 20);\n    i0.ɵɵtext(11, \"Add to Cart\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\");\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const item_r2 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate(\"alt\", item_r2.imgSrc);\n    i0.ɵɵproperty(\"src\", item_r2.imgSrc, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r2.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r2.description);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", item_r2.price, \" DT\");\n  }\n}\nfunction ShopComponent_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"p\");\n    i0.ɵɵtext(2, \"No items available for the selected category.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class ShopComponent {\n  constructor() {\n    this.selectedCategory = '';\n    this.searchQuery = '';\n    this.items = [{\n      id: 1,\n      title: 'dress',\n      description: 'Description 1',\n      price: 50,\n      category: '1',\n      imgSrc: 'assets/4.jpg'\n    }, {\n      id: 2,\n      title: 'shorts',\n      description: 'Description 2',\n      price: 60,\n      category: '2',\n      imgSrc: 'assets/2.jpg'\n    }, {\n      id: 3,\n      title: 'shirt',\n      description: 'Description 3',\n      price: 70,\n      category: '1',\n      imgSrc: 'assets/7.jpg'\n    }, {\n      id: 4,\n      title: 'jacket',\n      description: 'Description 4',\n      price: 80,\n      category: '3',\n      imgSrc: 'assets/8.jpg'\n    }, {\n      id: 4,\n      title: 'pants',\n      description: 'Description 5',\n      price: 50,\n      category: '1',\n      imgSrc: 'assets/5.jpg'\n    }, {\n      id: 4,\n      title: 'jacket',\n      description: 'Description 6',\n      price: 70,\n      category: '1',\n      imgSrc: 'assets/1.jpg'\n    }, {\n      id: 4,\n      title: 'pink shorts ',\n      description: 'Description 7',\n      price: 40,\n      category: '2',\n      imgSrc: 'assets/3.jpg'\n    }];\n  }\n  getFilteredItems() {\n    const query = this.searchQuery.toLowerCase();\n    return this.items.filter(item => {\n      const matchesCategory = !this.selectedCategory || item.category === this.selectedCategory;\n      const matchesSearch = item.title.toLowerCase().includes(query) || item.description.toLowerCase().includes(query);\n      return matchesCategory && matchesSearch;\n    });\n  }\n  static {\n    this.ɵfac = function ShopComponent_Factory(t) {\n      return new (t || ShopComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ShopComponent,\n      selectors: [[\"app-shop\"]],\n      decls: 21,\n      vars: 4,\n      consts: [[1, \"box\", \"mt-5\"], [1, \"my-2\", \"w-25\"], [\"for\", \"categories\", 1, \"my-1\"], [\"id\", \"categories\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"value\", \"\"], [\"value\", \"1\"], [\"value\", \"2\"], [\"value\", \"3\"], [\"value\", \"4\"], [1, \"my-2\", \"w-50\"], [\"for\", \"search\", 1, \"my-1\"], [\"id\", \"search\", \"type\", \"text\", \"placeholder\", \"Search by title...\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"class\", \"row\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"text-center mt-4\", 4, \"ngIf\"], [1, \"row\"], [1, \"col-md-3\", \"col-sm-12\"], [1, \"item\", \"d-flex\"], [3, \"src\", \"alt\"], [1, \"body\"], [1, \"px-3\", \"mb-2\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"btn\", \"btn-success\"], [1, \"text-center\", \"mt-4\"]],\n      template: function ShopComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"label\", 2);\n          i0.ɵɵtext(3, \"Categories\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"select\", 3);\n          i0.ɵɵlistener(\"ngModelChange\", function ShopComponent_Template_select_ngModelChange_4_listener($event) {\n            return ctx.selectedCategory = $event;\n          });\n          i0.ɵɵelementStart(5, \"option\", 4);\n          i0.ɵɵtext(6, \"All Categories\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"option\", 5);\n          i0.ɵɵtext(8, \"for summer\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"option\", 6);\n          i0.ɵɵtext(10, \"shorts\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"option\", 7);\n          i0.ɵɵtext(12, \"jackets\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"option\", 8);\n          i0.ɵɵtext(14, \"shoes\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(15, \"div\", 9)(16, \"label\", 10);\n          i0.ɵɵtext(17, \"Search Items\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"input\", 11);\n          i0.ɵɵlistener(\"ngModelChange\", function ShopComponent_Template_input_ngModelChange_18_listener($event) {\n            return ctx.searchQuery = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(19, ShopComponent_div_19_Template, 14, 5, \"div\", 12);\n          i0.ɵɵtemplate(20, ShopComponent_div_20_Template, 3, 0, \"div\", 13);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.selectedCategory);\n          i0.ɵɵadvance(14);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchQuery);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.getFilteredItems());\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.getFilteredItems().length === 0);\n        }\n      },\n      dependencies: [i1.NgForOf, i1.NgIf, i2.NgSelectOption, i2.ɵNgSelectMultipleOption, i2.DefaultValueAccessor, i2.SelectControlValueAccessor, i2.NgControlStatus, i2.NgModel],\n      styles: [\"body[_ngcontent-%COMP%]{margin-top: 100px;}\\n\\n.shop-container[_ngcontent-%COMP%] {\\n  padding: 80px 20px;\\n}\\n\\n.shop-header[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 40px;\\n}\\n\\n.shop-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 36px;\\n  position: relative;\\n  display: inline-block;\\n  color: var(--dark-pink);\\n  font-family: var(--font-cursive);\\n}\\n\\n.shop-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]::before, .shop-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]::after {\\n  content: '\\u2665';\\n  position: absolute;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  color: var(--primary);\\n  font-size: 24px;\\n}\\n\\n.shop-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]::before {\\n  left: -40px;\\n}\\n\\n.shop-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]::after {\\n  right: -40px;\\n}\\n\\n.filters[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  margin-bottom: 30px;\\n  flex-wrap: wrap;\\n}\\n\\n.filter-btn[_ngcontent-%COMP%] {\\n  padding: 8px 20px;\\n  margin: 0 10px 10px;\\n  background-color: var(--white);\\n  border: none;\\n  border-radius: 30px;\\n  cursor: pointer;\\n  transition: var(--transition);\\n  color: var(--dark-pink);\\n  box-shadow: 0 3px 10px rgba(240, 98, 146, 0.1);\\n}\\n\\n.filter-btn[_ngcontent-%COMP%]:hover, .filter-btn.active[_ngcontent-%COMP%] {\\n  background-color: var(--primary);\\n  color: var(--white);\\n  box-shadow: 0 5px 15px rgba(240, 98, 146, 0.2);\\n}\\n\\n.row[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\\n  gap: 30px;\\n  padding: 0 20px;\\n}\\n\\n.item[_ngcontent-%COMP%] {\\n  background-color: var(--white);\\n  border-radius: 15px;\\n  overflow: hidden;\\n  box-shadow: var(--shadow);\\n  transition: var(--transition);\\n  position: relative;\\n  margin-bottom: 30px;\\n}\\n\\n.item[_ngcontent-%COMP%]::before {\\n  content: '';\\n  position: absolute;\\n  top: -5px;\\n  left: -5px;\\n  right: -5px;\\n  bottom: -5px;\\n  background: linear-gradient(45deg, var(--primary), var(--secondary), var(--accent));\\n  z-index: -1;\\n  border-radius: 20px;\\n  opacity: 0;\\n  transition: var(--transition);\\n}\\n\\n.item[_ngcontent-%COMP%]:hover::before {\\n  opacity: 1;\\n}\\n\\n.item[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-10px) scale(1.02);\\n  box-shadow: 0 15px 30px rgba(240, 98, 146, 0.2);\\n}\\n\\n.item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 250px;\\n  object-fit: cover;\\n  transition: var(--transition);\\n}\\n\\n.item[_ngcontent-%COMP%]:hover   img[_ngcontent-%COMP%] {\\n  transform: scale(1.05);\\n}\\n\\n.body[_ngcontent-%COMP%] {\\n  padding: 15px;\\n}\\n\\n.item[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  color: var(--dark-pink);\\n  margin-bottom: 5px;\\n  text-transform: capitalize;\\n}\\n\\n.item[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 14px;\\n  margin-bottom: 15px;\\n  height: 60px;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  display: -webkit-box;\\n  -webkit-line-clamp: 3;\\n  -webkit-box-orient: vertical;\\n}\\n\\n.px-3[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 0 15px 15px;\\n}\\n\\n.btn[_ngcontent-%COMP%] {\\n  padding: 8px 15px;\\n  background-color: var(--primary);\\n  color: var(--white);\\n  border: none;\\n  border-radius: 20px;\\n  cursor: pointer;\\n  transition: var(--transition);\\n  font-size: 14px;\\n  font-weight: 600;\\n}\\n\\n.btn[_ngcontent-%COMP%]:hover {\\n  background-color: var(--accent);\\n  transform: translateY(-3px);\\n  box-shadow: 0 5px 15px rgba(240, 98, 146, 0.3);\\n}\\n\\nspan.price[_ngcontent-%COMP%] {\\n  color: var(--primary);\\n  font-weight: 600;\\n  font-size: 18px;\\n}\\n\\n@media (max-width: 768px) {\\n  .row[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵpropertyInterpolate", "item_r2", "imgSrc", "ɵɵproperty", "ɵɵsanitizeUrl", "ɵɵtextInterpolate", "title", "description", "ɵɵtextInterpolate1", "price", "ShopComponent", "constructor", "selectedCate<PERSON><PERSON>", "searchQuery", "items", "id", "category", "getFilteredItems", "query", "toLowerCase", "filter", "item", "matchesCategory", "matchesSearch", "includes", "selectors", "decls", "vars", "consts", "template", "ShopComponent_Template", "rf", "ctx", "ɵɵlistener", "ShopComponent_Template_select_ngModelChange_4_listener", "$event", "ShopComponent_Template_input_ngModelChange_18_listener", "ɵɵtemplate", "ShopComponent_div_19_Template", "ShopComponent_div_20_Template", "length"], "sources": ["C:\\Users\\<USER>\\Desktop\\projet angular\\Projet\\src\\shop\\shop.component.ts", "C:\\Users\\<USER>\\Desktop\\projet angular\\Projet\\src\\shop\\shop.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-shop',\n  templateUrl: './shop.component.html',\n  styleUrls: ['./shop.component.css']\n})\nexport class ShopComponent {\n  selectedCategory: string = '';\n  searchQuery: string = '';\n  items = [\n    { id: 1, title: 'dress', description: 'Description 1', price: 50, category: '1', imgSrc:'assets/4.jpg'},\n    { id: 2, title: 'shorts', description: 'Description 2', price: 60, category: '2', imgSrc:'assets/2.jpg'  },\n    { id: 3, title: 'shirt', description: 'Description 3', price: 70, category: '1', imgSrc:'assets/7.jpg'  },\n    { id: 4, title: 'jacket', description: 'Description 4', price: 80, category: '3', imgSrc:'assets/8.jpg'  },\n    { id: 4, title: 'pants', description: 'Description 5', price: 50, category: '1', imgSrc:'assets/5.jpg'  },\n    { id: 4, title: 'jacket', description: 'Description 6', price: 70, category: '1', imgSrc:'assets/1.jpg'  },\n    { id: 4, title: 'pink shorts ', description: 'Description 7', price: 40, category: '2', imgSrc:'assets/3.jpg'  }\n\n  ];\n\n  getFilteredItems() {\n    const query = this.searchQuery.toLowerCase();\n    return this.items.filter(item => {\n      const matchesCategory = !this.selectedCategory || item.category === this.selectedCategory;\n      const matchesSearch = item.title.toLowerCase().includes(query) || item.description.toLowerCase().includes(query);\n      return matchesCategory && matchesSearch;\n    });\n  }\n\n}\n", "<div class=\"box mt-5\">\n    <div class=\"my-2 w-25\">\n      <label for=\"categories\" class=\"my-1\">Categories</label>\n      <select\n        id=\"categories\"\n        class=\"form-control\"\n        [(ngModel)]=\"selectedCategory\"\n      >\n        <option value=\"\">All Categories</option>\n        <option value=\"1\">for summer</option>\n        <option value=\"2\">shorts</option>\n        <option value=\"3\">jackets</option>\n        <option value=\"4\">shoes</option>\n      </select>\n    </div>\n    <div class=\"my-2 w-50\">\n      <label for=\"search\" class=\"my-1\">Search Items</label>\n      <input\n        id=\"search\"\n        type=\"text\"\n        class=\"form-control\"\n        placeholder=\"Search by title...\"\n        [(ngModel)]=\"searchQuery\"\n      />\n    </div>\n    <div class=\"row\" *ngFor=\"let item of getFilteredItems()\">\n      <div class=\"col-md-3 col-sm-12\">\n      <div class=\"item d-flex\">\n          <img [src]=\"item.imgSrc\" alt={{item.imgSrc}}>\n          <div class=\"body\">\n            <h4>{{ item.title }}</h4>\n            <p>{{ item.description }}</p>\n          </div>\n          <div class=\"px-3 mb-2 d-flex justify-content-between align-items-center\">\n            <button class=\"btn btn-success\">Add to Cart</button>\n            <span>{{ item.price }} DT</span>\n          </div>\n        </div>\n      </div>\n    </div>\n  \n    <!-- Show a message if no items match the filter -->\n    <div *ngIf=\"getFilteredItems().length === 0\" class=\"text-center mt-4\">\n      <p>No items available for the selected category.</p>\n    </div>\n  </div>\n  \n"], "mappings": ";;;;;ICyBIA,EAAA,CAAAC,cAAA,cAAyD;IAGnDD,EAAA,CAAAE,SAAA,cAA6C;IAC7CF,EAAA,CAAAC,cAAA,cAAkB;IACZD,EAAA,CAAAG,MAAA,GAAgB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACzBJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,GAAsB;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAE/BJ,EAAA,CAAAC,cAAA,cAAyE;IACvCD,EAAA,CAAAG,MAAA,mBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACpDJ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAG,MAAA,IAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IAPTJ,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAM,qBAAA,QAAAC,OAAA,CAAAC,MAAA,CAAmB;IAAvCR,EAAA,CAAAS,UAAA,QAAAF,OAAA,CAAAC,MAAA,EAAAR,EAAA,CAAAU,aAAA,CAAmB;IAElBV,EAAA,CAAAK,SAAA,GAAgB;IAAhBL,EAAA,CAAAW,iBAAA,CAAAJ,OAAA,CAAAK,KAAA,CAAgB;IACjBZ,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAAW,iBAAA,CAAAJ,OAAA,CAAAM,WAAA,CAAsB;IAInBb,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAc,kBAAA,KAAAP,OAAA,CAAAQ,KAAA,QAAmB;;;;;IAOjCf,EAAA,CAAAC,cAAA,cAAsE;IACjED,EAAA,CAAAG,MAAA,oDAA6C;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;ADpC1D,OAAM,MAAOY,aAAa;EAL1BC,YAAA;IAME,KAAAC,gBAAgB,GAAW,EAAE;IAC7B,KAAAC,WAAW,GAAW,EAAE;IACxB,KAAAC,KAAK,GAAG,CACN;MAAEC,EAAE,EAAE,CAAC;MAAET,KAAK,EAAE,OAAO;MAAEC,WAAW,EAAE,eAAe;MAAEE,KAAK,EAAE,EAAE;MAAEO,QAAQ,EAAE,GAAG;MAAEd,MAAM,EAAC;IAAc,CAAC,EACvG;MAAEa,EAAE,EAAE,CAAC;MAAET,KAAK,EAAE,QAAQ;MAAEC,WAAW,EAAE,eAAe;MAAEE,KAAK,EAAE,EAAE;MAAEO,QAAQ,EAAE,GAAG;MAAEd,MAAM,EAAC;IAAc,CAAG,EAC1G;MAAEa,EAAE,EAAE,CAAC;MAAET,KAAK,EAAE,OAAO;MAAEC,WAAW,EAAE,eAAe;MAAEE,KAAK,EAAE,EAAE;MAAEO,QAAQ,EAAE,GAAG;MAAEd,MAAM,EAAC;IAAc,CAAG,EACzG;MAAEa,EAAE,EAAE,CAAC;MAAET,KAAK,EAAE,QAAQ;MAAEC,WAAW,EAAE,eAAe;MAAEE,KAAK,EAAE,EAAE;MAAEO,QAAQ,EAAE,GAAG;MAAEd,MAAM,EAAC;IAAc,CAAG,EAC1G;MAAEa,EAAE,EAAE,CAAC;MAAET,KAAK,EAAE,OAAO;MAAEC,WAAW,EAAE,eAAe;MAAEE,KAAK,EAAE,EAAE;MAAEO,QAAQ,EAAE,GAAG;MAAEd,MAAM,EAAC;IAAc,CAAG,EACzG;MAAEa,EAAE,EAAE,CAAC;MAAET,KAAK,EAAE,QAAQ;MAAEC,WAAW,EAAE,eAAe;MAAEE,KAAK,EAAE,EAAE;MAAEO,QAAQ,EAAE,GAAG;MAAEd,MAAM,EAAC;IAAc,CAAG,EAC1G;MAAEa,EAAE,EAAE,CAAC;MAAET,KAAK,EAAE,cAAc;MAAEC,WAAW,EAAE,eAAe;MAAEE,KAAK,EAAE,EAAE;MAAEO,QAAQ,EAAE,GAAG;MAAEd,MAAM,EAAC;IAAc,CAAG,CAEjH;;EAEDe,gBAAgBA,CAAA;IACd,MAAMC,KAAK,GAAG,IAAI,CAACL,WAAW,CAACM,WAAW,EAAE;IAC5C,OAAO,IAAI,CAACL,KAAK,CAACM,MAAM,CAACC,IAAI,IAAG;MAC9B,MAAMC,eAAe,GAAG,CAAC,IAAI,CAACV,gBAAgB,IAAIS,IAAI,CAACL,QAAQ,KAAK,IAAI,CAACJ,gBAAgB;MACzF,MAAMW,aAAa,GAAGF,IAAI,CAACf,KAAK,CAACa,WAAW,EAAE,CAACK,QAAQ,CAACN,KAAK,CAAC,IAAIG,IAAI,CAACd,WAAW,CAACY,WAAW,EAAE,CAACK,QAAQ,CAACN,KAAK,CAAC;MAChH,OAAOI,eAAe,IAAIC,aAAa;IACzC,CAAC,CAAC;EACJ;;;uBArBWb,aAAa;IAAA;EAAA;;;YAAbA,aAAa;MAAAe,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCP1BrC,EAAA,CAAAC,cAAA,aAAsB;UAEqBD,EAAA,CAAAG,MAAA,iBAAU;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UACvDJ,EAAA,CAAAC,cAAA,gBAIC;UADCD,EAAA,CAAAuC,UAAA,2BAAAC,uDAAAC,MAAA;YAAA,OAAAH,GAAA,CAAApB,gBAAA,GAAAuB,MAAA;UAAA,EAA8B;UAE9BzC,EAAA,CAAAC,cAAA,gBAAiB;UAAAD,EAAA,CAAAG,MAAA,qBAAc;UAAAH,EAAA,CAAAI,YAAA,EAAS;UACxCJ,EAAA,CAAAC,cAAA,gBAAkB;UAAAD,EAAA,CAAAG,MAAA,iBAAU;UAAAH,EAAA,CAAAI,YAAA,EAAS;UACrCJ,EAAA,CAAAC,cAAA,gBAAkB;UAAAD,EAAA,CAAAG,MAAA,cAAM;UAAAH,EAAA,CAAAI,YAAA,EAAS;UACjCJ,EAAA,CAAAC,cAAA,iBAAkB;UAAAD,EAAA,CAAAG,MAAA,eAAO;UAAAH,EAAA,CAAAI,YAAA,EAAS;UAClCJ,EAAA,CAAAC,cAAA,iBAAkB;UAAAD,EAAA,CAAAG,MAAA,aAAK;UAAAH,EAAA,CAAAI,YAAA,EAAS;UAGpCJ,EAAA,CAAAC,cAAA,cAAuB;UACYD,EAAA,CAAAG,MAAA,oBAAY;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UACrDJ,EAAA,CAAAC,cAAA,iBAME;UADAD,EAAA,CAAAuC,UAAA,2BAAAG,uDAAAD,MAAA;YAAA,OAAAH,GAAA,CAAAnB,WAAA,GAAAsB,MAAA;UAAA,EAAyB;UAL3BzC,EAAA,CAAAI,YAAA,EAME;UAEJJ,EAAA,CAAA2C,UAAA,KAAAC,6BAAA,mBAcM;UAGN5C,EAAA,CAAA2C,UAAA,KAAAE,6BAAA,kBAEM;UACR7C,EAAA,CAAAI,YAAA,EAAM;;;UAvCAJ,EAAA,CAAAK,SAAA,GAA8B;UAA9BL,EAAA,CAAAS,UAAA,YAAA6B,GAAA,CAAApB,gBAAA,CAA8B;UAgB9BlB,EAAA,CAAAK,SAAA,IAAyB;UAAzBL,EAAA,CAAAS,UAAA,YAAA6B,GAAA,CAAAnB,WAAA,CAAyB;UAGKnB,EAAA,CAAAK,SAAA,GAAqB;UAArBL,EAAA,CAAAS,UAAA,YAAA6B,GAAA,CAAAf,gBAAA,GAAqB;UAiBjDvB,EAAA,CAAAK,SAAA,GAAqC;UAArCL,EAAA,CAAAS,UAAA,SAAA6B,GAAA,CAAAf,gBAAA,GAAAuB,MAAA,OAAqC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}