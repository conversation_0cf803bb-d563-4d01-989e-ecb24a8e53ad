{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\nimport { FormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\nimport { HeaderComponent } from '../header/header.component';\nimport { HomeComponent } from '../home/<USER>';\nimport { ShopComponent } from '../shop/shop.component';\nimport { LoginComponent } from '../login/login.component';\nexport let AppModule = class AppModule {};\nAppModule = __decorate([NgModule({\n  declarations: [AppComponent, HeaderComponent, HomeComponent, ShopComponent, LoginComponent],\n  imports: [BrowserModule, AppRoutingModule, FormsModule, RouterModule],\n  providers: [],\n  bootstrap: [AppComponent]\n})], AppModule);", "map": {"version": 3, "names": ["NgModule", "BrowserModule", "AppRoutingModule", "AppComponent", "FormsModule", "RouterModule", "HeaderComponent", "HomeComponent", "ShopComponent", "LoginComponent", "AppModule", "__decorate", "declarations", "imports", "providers", "bootstrap"], "sources": ["C:\\Users\\<USER>\\Desktop\\projet angular\\Projet\\src\\app\\app.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { BrowserModule } from '@angular/platform-browser';\n\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\nimport { FormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\nimport { HeaderComponent } from '../header/header.component';\nimport { HomeComponent } from '../home/<USER>';\nimport { ShopComponent } from '../shop/shop.component';\nimport { LoginComponent } from '../login/login.component';\n\n@NgModule({\n  declarations: [\n    AppComponent,\n    HeaderComponent,\n    HomeComponent,\n    ShopComponent,\n    LoginComponent\n  ],\n  imports: [\n    BrowserModule,\n    AppRoutingModule,\n    FormsModule,\n    RouterModule\n  ],\n  providers: [],\n  bootstrap: [AppComponent]\n})\nexport class AppModule { }\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,aAAa,QAAQ,2BAA2B;AAEzD,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,eAAe,QAAQ,4BAA4B;AAC5D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,0BAA0B;AAmBlD,WAAMC,SAAS,GAAf,MAAMA,SAAS,GAAI;AAAbA,SAAS,GAAAC,UAAA,EAjBrBX,QAAQ,CAAC;EACRY,YAAY,EAAE,CACZT,YAAY,EACZG,eAAe,EACfC,aAAa,EACbC,aAAa,EACbC,cAAc,CACf;EACDI,OAAO,EAAE,CACPZ,aAAa,EACbC,gBAAgB,EAChBE,WAAW,EACXC,YAAY,CACb;EACDS,SAAS,EAAE,EAAE;EACbC,SAAS,EAAE,CAACZ,YAAY;CACzB,CAAC,C,EACWO,SAAS,CAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}