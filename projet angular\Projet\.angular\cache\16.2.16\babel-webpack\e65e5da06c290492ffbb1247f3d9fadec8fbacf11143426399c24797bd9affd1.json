{"ast": null, "code": "import { identity } from '../util/identity';\nimport { mapOneOrManyArgs } from '../util/mapOneOrManyArgs';\nimport { pipe } from '../util/pipe';\nimport { mergeMap } from './mergeMap';\nimport { toArray } from './toArray';\nexport function joinAllInternals(joinFn, project) {\n  return pipe(toArray(), mergeMap(sources => joinFn(sources)), project ? mapOneOrManyArgs(project) : identity);\n}", "map": {"version": 3, "names": ["identity", "mapOneOrManyArgs", "pipe", "mergeMap", "toArray", "joinAllInternals", "joinFn", "project", "sources"], "sources": ["C:/Users/<USER>/Desktop/projet angular/Projet/node_modules/rxjs/dist/esm/internal/operators/joinAllInternals.js"], "sourcesContent": ["import { identity } from '../util/identity';\nimport { mapOneOrManyArgs } from '../util/mapOneOrManyArgs';\nimport { pipe } from '../util/pipe';\nimport { mergeMap } from './mergeMap';\nimport { toArray } from './toArray';\nexport function joinAllInternals(joinFn, project) {\n    return pipe(toArray(), mergeMap((sources) => joinFn(sources)), project ? mapOneOrManyArgs(project) : identity);\n}\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,kBAAkB;AAC3C,SAASC,gBAAgB,QAAQ,0BAA0B;AAC3D,SAASC,IAAI,QAAQ,cAAc;AACnC,SAASC,QAAQ,QAAQ,YAAY;AACrC,SAASC,OAAO,QAAQ,WAAW;AACnC,OAAO,SAASC,gBAAgBA,CAACC,MAAM,EAAEC,OAAO,EAAE;EAC9C,OAAOL,IAAI,CAACE,OAAO,CAAC,CAAC,EAAED,QAAQ,CAAEK,OAAO,IAAKF,MAAM,CAACE,OAAO,CAAC,CAAC,EAAED,OAAO,GAAGN,gBAAgB,CAACM,OAAO,CAAC,GAAGP,QAAQ,CAAC;AAClH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}