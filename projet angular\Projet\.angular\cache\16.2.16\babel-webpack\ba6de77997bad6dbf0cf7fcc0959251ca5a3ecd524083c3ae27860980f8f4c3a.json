{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { ShopComponent } from '../shop/shop.component';\nimport { HomeComponent } from '../home/<USER>';\nimport { LognComponent } from '../logn/logn.component';\nimport { LoginComponent } from '../login/login.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: \"home\",\n  component: HomeComponent\n}, {\n  path: \"shop\",\n  component: ShopComponent\n}, {\n  path: \"login\",\n  component: LognComponent\n}, {\n  path: \"signup\",\n  component: LoginComponent\n}, {\n  path: \"**\",\n  redirectTo: \"home\",\n  pathMatch: \"full\"\n}];\nexport class AppRoutingModule {\n  static {\n    this.ɵfac = function AppRoutingModule_Factory(t) {\n      return new (t || AppRoutingModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppRoutingModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [RouterModule.forRoot(routes), RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppRoutingModule, {\n    imports: [i1.RouterModule],\n    exports: [RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "ShopComponent", "HomeComponent", "LognComponent", "LoginComponent", "routes", "path", "component", "redirectTo", "pathMatch", "AppRoutingModule", "forRoot", "imports", "i1", "exports"], "sources": ["C:\\Users\\<USER>\\Desktop\\projet angular\\Projet\\src\\app\\app-routing.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { RouterModule, Routes } from '@angular/router';\nimport { ShopComponent } from '../shop/shop.component';\nimport { HomeComponent } from '../home/<USER>';\nimport { LognComponent } from '../logn/logn.component';\nimport { LoginComponent } from '../login/login.component';\n\nconst routes: Routes = [\n  {path:\"home\",component:HomeComponent},\n  {path:\"shop\",component:ShopComponent},\n  {path:\"login\",component:LognComponent},\n  {path:\"signup\",component:LoginComponent},\n  {path:\"**\",redirectTo:\"home\",pathMatch:\"full\"}\n];\n\n@NgModule({\n  imports: [RouterModule.forRoot(routes)],\n  exports: [RouterModule]\n})\nexport class AppRoutingModule { }\n"], "mappings": "AACA,SAASA,YAAY,QAAgB,iBAAiB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,0BAA0B;;;AAEzD,MAAMC,MAAM,GAAW,CACrB;EAACC,IAAI,EAAC,MAAM;EAACC,SAAS,EAACL;AAAa,CAAC,EACrC;EAACI,IAAI,EAAC,MAAM;EAACC,SAAS,EAACN;AAAa,CAAC,EACrC;EAACK,IAAI,EAAC,OAAO;EAACC,SAAS,EAACJ;AAAa,CAAC,EACtC;EAACG,IAAI,EAAC,QAAQ;EAACC,SAAS,EAACH;AAAc,CAAC,EACxC;EAACE,IAAI,EAAC,IAAI;EAACE,UAAU,EAAC,MAAM;EAACC,SAAS,EAAC;AAAM,CAAC,CAC/C;AAMD,OAAM,MAAOC,gBAAgB;;;uBAAhBA,gBAAgB;IAAA;EAAA;;;YAAhBA;IAAgB;EAAA;;;gBAHjBV,YAAY,CAACW,OAAO,CAACN,MAAM,CAAC,EAC5BL,YAAY;IAAA;EAAA;;;2EAEXU,gBAAgB;IAAAE,OAAA,GAAAC,EAAA,CAAAb,YAAA;IAAAc,OAAA,GAFjBd,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}