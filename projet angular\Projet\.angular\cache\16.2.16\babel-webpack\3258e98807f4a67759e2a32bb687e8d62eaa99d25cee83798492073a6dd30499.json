{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class AppComponent {\n  constructor() {\n    this.title = 'Projet';\n  }\n  static {\n    this.ɵfac = function AppComponent_Factory(t) {\n      return new (t || AppComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppComponent,\n      selectors: [[\"app-root\"]],\n      decls: 18,\n      vars: 0,\n      consts: [[1, \"container\"], [1, \"content\"], [\"src\", \"assets/women's wear.png\", \"alt\", \"\", \"routerLink\", \"/home\", 1, \"logo\"], [1, \"navmenu\"], [\"routerLink\", \"/home\"], [\"routerLink\", \"/shop\"], [\"routerLink\", \"/login\"], [1, \"end-text\"]],\n      template: function AppComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"nav\")(1, \"div\", 0)(2, \"div\", 1);\n          i0.ɵɵelement(3, \"img\", 2);\n          i0.ɵɵelementStart(4, \"ul\", 3)(5, \"li\", 4);\n          i0.ɵɵtext(6, \"Home\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"li\", 5);\n          i0.ɵɵtext(8, \"Shop\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"ul\")(10, \"li\", 6);\n          i0.ɵɵtext(11, \"Log in\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(12, \"div\", 0);\n          i0.ɵɵelement(13, \"router-outlet\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(14, \"br\");\n          i0.ɵɵelementStart(15, \"div\", 7)(16, \"p\");\n          i0.ɵɵtext(17, \"Copyright \\u00A9 2025 .All Rights Reserved.Designed By ... \");\n          i0.ɵɵelementEnd()();\n        }\n      },\n      dependencies: [i1.RouterOutlet, i1.RouterLink],\n      styles: [\"nav[_ngcontent-%COMP%] {\\n  background-color: white;\\n  box-shadow: var(--shadow);\\n  position: sticky;\\n  top: 0;\\n  z-index: 1000;\\n  width: 100%;\\n}\\n\\nnav[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 15px 0;\\n}\\n\\nnav[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 120px;\\n  transition: var(--transition);\\n}\\n\\nnav[_ngcontent-%COMP%]   .navmenu[_ngcontent-%COMP%] {\\n  display: flex;\\n  list-style: none;\\n  margin: 0;\\n  padding: 0;\\n}\\n\\nnav[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  margin: 0 20px;\\n  font-weight: 500;\\n  position: relative;\\n  cursor: pointer;\\n  transition: var(--transition);\\n}\\n\\nnav[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]::after {\\n  content: '';\\n  position: absolute;\\n  bottom: -5px;\\n  left: 0;\\n  width: 0;\\n  height: 2px;\\n  background-color: var(--accent);\\n  transition: var(--transition);\\n}\\n\\nnav[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:hover {\\n  color: var(--accent);\\n}\\n\\nnav[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:hover::after {\\n  width: 100%;\\n}\\n\\n.end-text[_ngcontent-%COMP%] {\\n  background-color: var(--light);\\n  text-align: center;\\n  padding: 20px;\\n  font-size: 14px;\\n  color: var(--secondary);\\n}\\n\\n@media(max-width: 768px) {\\n  nav[_ngcontent-%COMP%]   .content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    padding: 15px 0;\\n  }\\n  \\n  nav[_ngcontent-%COMP%]   .navmenu[_ngcontent-%COMP%] {\\n    margin-top: 15px;\\n  }\\n  \\n  nav[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n    margin: 0 10px;\\n  }\\n}\\n        \\n\\n\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["AppComponent", "constructor", "title", "selectors", "decls", "vars", "consts", "template", "AppComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd"], "sources": ["C:\\Users\\<USER>\\Desktop\\projet angular\\Projet\\src\\app\\app.component.ts", "C:\\Users\\<USER>\\Desktop\\projet angular\\Projet\\src\\app\\app.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-root',\n  templateUrl: './app.component.html',\n  styleUrls: ['./app.component.css']\n})\nexport class AppComponent {\n  title = 'Projet';\n}\n", "<nav>\n    <div class=\"container\">\n        <div class=\"content\">\n            <img class=\"logo\" src=\"assets/women's wear.png\" alt=\"\"routerLink=\"/home\">\n            <ul class=\"navmenu\">\n                <li routerLink=\"/home\">Home</li>\n                <li routerLink=\"/shop\" >Shop</li>\n            </ul>\n            <ul> \n                <li routerLink=\"/login\">Log in</li>\n            </ul>\n        </div>\n    </div>\n</nav>\n<div class=\"container\"><router-outlet></router-outlet>\n</div>\n<br>\n<div class=\"end-text\">\n    <p>Copyright © 2025 .All Rights Reserved.Designed By ... </p>\n</div>"], "mappings": ";;AAOA,OAAM,MAAOA,YAAY;EALzBC,YAAA;IAME,KAAAC,KAAK,GAAG,QAAQ;;;;uBADLF,YAAY;IAAA;EAAA;;;YAAZA,YAAY;MAAAG,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCPzBE,EAAA,CAAAC,cAAA,UAAK;UAGOD,EAAA,CAAAE,SAAA,aAAyE;UACzEF,EAAA,CAAAC,cAAA,YAAoB;UACOD,EAAA,CAAAG,MAAA,WAAI;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAChCJ,EAAA,CAAAC,cAAA,YAAwB;UAAAD,EAAA,CAAAG,MAAA,WAAI;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAErCJ,EAAA,CAAAC,cAAA,SAAI;UACwBD,EAAA,CAAAG,MAAA,cAAM;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAKnDJ,EAAA,CAAAC,cAAA,cAAuB;UAAAD,EAAA,CAAAE,SAAA,qBAA+B;UACtDF,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAE,SAAA,UAAI;UACJF,EAAA,CAAAC,cAAA,cAAsB;UACfD,EAAA,CAAAG,MAAA,mEAAsD;UAAAH,EAAA,CAAAI,YAAA,EAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}