{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nexport let HomeComponent = class HomeComponent {\n  constructor(router) {\n    this.router = router;\n    this.items = Array.from({\n      length: 7\n    }, (_, i) => ({\n      id: i + 1,\n      title: `Item ${i + 1}`,\n      description: `Description ${i + 1}`,\n      price: (i + 1) * 50,\n      imgSrc: `assets/${i + 1}.jpg`\n    }));\n  }\n  navigateToShop() {\n    this.router.navigate(['/shop']);\n  }\n};\nHomeComponent = __decorate([Component({\n  selector: 'app-home',\n  templateUrl: './home.component.html',\n  styleUrls: ['./home.component.css']\n})], HomeComponent);", "map": {"version": 3, "names": ["Component", "HomeComponent", "constructor", "router", "items", "Array", "from", "length", "_", "i", "id", "title", "description", "price", "imgSrc", "navigateToShop", "navigate", "__decorate", "selector", "templateUrl", "styleUrls"], "sources": ["C:\\Users\\<USER>\\Desktop\\projet angular\\Projet\\src\\home\\home.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { Router } from '@angular/router';\n\n@Component({\n  selector: 'app-home',\n  templateUrl: './home.component.html',\n  styleUrls: ['./home.component.css']\n})\nexport class HomeComponent {\n  constructor(private router:Router){}\n  items = Array.from({ length: 7 }, (_, i) => ({\n    id: i + 1,\n    title: `Item ${i + 1}`,\n    description: `Description ${i + 1}`,\n    price: (i + 1) * 50,\n    imgSrc: `assets/${i + 1}.jpg`\n  }));  \n  navigateToShop() {\n    this.router.navigate(['/shop']);\n}\n}\n"], "mappings": ";AAAA,SAASA,SAAS,QAAQ,eAAe;AAQlC,WAAMC,aAAa,GAAnB,MAAMA,aAAa;EACxBC,YAAoBC,MAAa;IAAb,KAAAA,MAAM,GAANA,MAAM;IAC1B,KAAAC,KAAK,GAAGC,KAAK,CAACC,IAAI,CAAC;MAAEC,MAAM,EAAE;IAAC,CAAE,EAAE,CAACC,CAAC,EAAEC,CAAC,MAAM;MAC3CC,EAAE,EAAED,CAAC,GAAG,CAAC;MACTE,KAAK,EAAE,QAAQF,CAAC,GAAG,CAAC,EAAE;MACtBG,WAAW,EAAE,eAAeH,CAAC,GAAG,CAAC,EAAE;MACnCI,KAAK,EAAE,CAACJ,CAAC,GAAG,CAAC,IAAI,EAAE;MACnBK,MAAM,EAAE,UAAUL,CAAC,GAAG,CAAC;KACxB,CAAC,CAAC;EAPgC;EAQnCM,cAAcA,CAAA;IACZ,IAAI,CAACZ,MAAM,CAACa,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;EACnC;CACC;AAZYf,aAAa,GAAAgB,UAAA,EALzBjB,SAAS,CAAC;EACTkB,QAAQ,EAAE,UAAU;EACpBC,WAAW,EAAE,uBAAuB;EACpCC,SAAS,EAAE,CAAC,sBAAsB;CACnC,CAAC,C,EACWnB,aAAa,CAYzB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}