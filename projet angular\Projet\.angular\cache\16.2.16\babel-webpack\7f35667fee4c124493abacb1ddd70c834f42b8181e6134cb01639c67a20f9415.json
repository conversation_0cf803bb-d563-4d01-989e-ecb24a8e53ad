{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nexport let LognComponent = class LognComponent {\n  constructor(router) {\n    this.router = router;\n  }\n  onSubmit() {}\n};\nLognComponent = __decorate([Component({\n  selector: 'app-logn',\n  templateUrl: './logn.component.html',\n  styleUrls: ['./logn.component.css']\n})], LognComponent);", "map": {"version": 3, "names": ["Component", "LognComponent", "constructor", "router", "onSubmit", "__decorate", "selector", "templateUrl", "styleUrls"], "sources": ["C:\\Users\\<USER>\\Desktop\\projet angular\\Projet\\src\\logn\\logn.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-logn',\n  templateUrl: './logn.component.html',\n  styleUrls: ['./logn.component.css']\n})\nexport class LognComponent {\n  constructor(private router: Router) {}\n  onSubmit() {}\n}\n"], "mappings": ";AAAA,SAASA,SAAS,QAAQ,eAAe;AAOlC,WAAMC,aAAa,GAAnB,MAAMA,aAAa;EACxBC,YAAoBC,MAAc;IAAd,KAAAA,MAAM,GAANA,MAAM;EAAW;EACrCC,QAAQA,CAAA,GAAI;CACb;AAHYH,aAAa,GAAAI,UAAA,EALzBL,SAAS,CAAC;EACTM,QAAQ,EAAE,UAAU;EACpBC,WAAW,EAAE,uBAAuB;EACpCC,SAAS,EAAE,CAAC,sBAAsB;CACnC,CAAC,C,EACWP,aAAa,CAGzB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}