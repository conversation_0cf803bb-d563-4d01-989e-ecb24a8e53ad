{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common/http\";\nimport * as i3 from \"@angular/common\";\nfunction HomeComponent_div_20_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵelement(1, \"img\", 28);\n    i0.ɵɵelementStart(2, \"div\", 29)(3, \"h5\");\n    i0.ɵɵtext(4, \"Sale\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 30);\n    i0.ɵɵelement(6, \"i\", 31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 32);\n    i0.ɵɵelement(8, \"i\", 33)(9, \"i\", 33)(10, \"i\", 33)(11, \"i\", 33)(12, \"i\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 35)(14, \"h4\");\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"h4\");\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"p\");\n    i0.ɵɵtext(19, \"50dt\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r2 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate(\"alt\", item_r2.title);\n    i0.ɵɵproperty(\"src\", item_r2.imgSrc, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(14);\n    i0.ɵɵtextInterpolate(item_r2.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r2.description);\n  }\n}\nfunction HomeComponent_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵtemplate(1, HomeComponent_div_20_div_1_Template, 20, 4, \"div\", 26);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.items);\n  }\n}\nexport class HomeComponent {\n  constructor(router, http) {\n    this.router = router;\n    this.http = http;\n    this.items = [];\n  }\n  ngOnInit() {\n    this.http.get('assets/products.json').subscribe({\n      next: data => {\n        this.items = data;\n        console.log('Loaded items:', this.items); // <-- Add this\n      },\n\n      error: err => {\n        console.error('Error loading JSON:', err); // <-- Add this\n      }\n    });\n  }\n\n  navigateToShop() {\n    this.router.navigate(['/shop']);\n  }\n  static {\n    this.ɵfac = function HomeComponent_Factory(t) {\n      return new (t || HomeComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.HttpClient));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HomeComponent,\n      selectors: [[\"app-home\"]],\n      decls: 80,\n      vars: 4,\n      consts: [[1, \"section\"], [3, \"click\"], [1, \"trending-product\"], [1, \"center-text\"], [\"class\", \"grid-Products\", 4, \"ngIf\"], [1, \"client-reviews\"], [1, \"reviews\"], [\"src\", \"assets/woman.jpg\", \"alt\", \"\"], [1, \"contact\"], [1, \"contact-info\"], [1, \"first-info\"], [\"src\", \"assets/women's wear.png\", \"alt\", \"\"], [1, \"social-icon\"], [\"href\", \"https://www.facebook.com/facebook/\"], [1, \"bx\", \"bxl-facebook-circle\"], [\"href\", \"https://x.com/\"], [1, \"bx\", \"bxl-twitter\"], [\"href\", \"https://www.instagram.com/instagram/\"], [1, \"bx\", \"bxl-instagram\"], [\"href\", \"https://www.youtube.com/@YouTube\"], [1, \"bx\", \"bxl-youtube\"], [\"href\", \"https://www.linkedin.com/feed/\"], [1, \"bx\", \"bxl-linkedin\"], [1, \"second-info\"], [1, \"third-info\"], [1, \"grid-Products\"], [\"class\", \"grid-row\", 4, \"ngFor\", \"ngForOf\"], [1, \"grid-row\"], [3, \"src\", \"alt\"], [1, \"product-text\"], [1, \"heart-icon\"], [1, \"bx\", \"bx-heart\"], [1, \"ratting\"], [1, \"bx\", \"bxs-star\"], [1, \"bx\", \"bxs-star-half\"], [1, \"price\"]],\n      template: function HomeComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"h5\");\n          i0.ɵɵtext(2, \"2025 collection\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"h1\");\n          i0.ɵɵtext(4, \"New\");\n          i0.ɵɵelement(5, \"br\");\n          i0.ɵɵtext(6, \"collection 2025\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"h6\");\n          i0.ɵɵtext(8, \"There's nothing like trend\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"p\", 1);\n          i0.ɵɵlistener(\"click\", function HomeComponent_Template_p_click_9_listener() {\n            return ctx.navigateToShop();\n          });\n          i0.ɵɵtext(10, \"Shop now\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(11, \"div\", 2)(12, \"div\", 3)(13, \"h2\");\n          i0.ɵɵtext(14, \"Our tranding \");\n          i0.ɵɵelementStart(15, \"span\");\n          i0.ɵɵtext(16, \"Products\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(17, \"pre\");\n          i0.ɵɵtext(18);\n          i0.ɵɵpipe(19, \"json\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(20, HomeComponent_div_20_Template, 2, 1, \"div\", 4);\n          i0.ɵɵelement(21, \"br\")(22, \"br\");\n          i0.ɵɵelementStart(23, \"section\", 5)(24, \"div\", 6)(25, \"h3\");\n          i0.ɵɵtext(26, \"Client Reviews\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(27, \"img\", 7);\n          i0.ɵɵelementStart(28, \"p\");\n          i0.ɵɵtext(29, \"Hello, my name is Sarah, and I\\u2019ve been shopping for stylish and comfortable women\\u2019s clothing online for years. I\\u2019m always looking for outfits that reflect my personality\\u2014elegant yet practical for my busy lifestyle. I appreciate a website that's easy to navigate with clear size guides, diverse collections, and quality fabrics. Fast delivery and excellent customer service are also a must for me. A good online shopping experience keeps me coming back for more!\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(30, \"h2\");\n          i0.ɵɵtext(31, \"Sarah\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"p\");\n          i0.ɵɵtext(33, \"CEO of Addle\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelement(34, \"br\")(35, \"br\");\n          i0.ɵɵelementStart(36, \"section\", 8)(37, \"div\", 9)(38, \"div\", 10);\n          i0.ɵɵelement(39, \"img\", 11);\n          i0.ɵɵelementStart(40, \"p\");\n          i0.ɵɵtext(41, \"123 Olive Street, Sidi Bouzid, 9100\");\n          i0.ɵɵelement(42, \"br\");\n          i0.ɵɵtext(43, \" Tunisia\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"p\");\n          i0.ɵɵtext(45, \"<EMAIL>\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"div\", 12)(47, \"a\", 13);\n          i0.ɵɵelement(48, \"i\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"a\", 15);\n          i0.ɵɵelement(50, \"i\", 16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"a\", 17);\n          i0.ɵɵelement(52, \"i\", 18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(53, \"a\", 19);\n          i0.ɵɵelement(54, \"i\", 20);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(55, \"a\", 21);\n          i0.ɵɵelement(56, \"i\", 22);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(57, \"div\", 23)(58, \"h4\");\n          i0.ɵɵtext(59, \"Support\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(60, \"p\");\n          i0.ɵɵtext(61, \"About us\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(62, \"p\");\n          i0.ɵɵtext(63, \"Contact us\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(64, \"p\");\n          i0.ɵɵtext(65, \"size guide\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(66, \"p\");\n          i0.ɵɵtext(67, \"Privacy\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(68, \"p\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(69, \"div\", 24)(70, \"h4\");\n          i0.ɵɵtext(71, \"Company\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(72, \"p\");\n          i0.ɵɵtext(73, \"About\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(74, \"p\");\n          i0.ɵɵtext(75, \"Blog\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(76, \"p\");\n          i0.ɵɵtext(77, \"Affiliate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(78, \"p\");\n          i0.ɵɵtext(79, \"Login\");\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(18);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(19, 2, ctx.items));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.items.length > 0);\n        }\n      },\n      dependencies: [i3.NgForOf, i3.NgIf, i3.JsonPipe],\n      styles: [\"*[_ngcontent-%COMP%]{\\n    margin: 0;\\n    padding: 0;\\n    box-sizing: border-box;\\n    scroll-behavior: smooth;\\n    font-family: 'sans-serif';\\n    list-style: none;\\n    text-decoration: none;\\n}\\n.section[_ngcontent-%COMP%]{\\n    padding: 5% 10%;\\n    width: 100%;\\n    height: 100vh;\\n    background-image:url('banner-3.png');\\n    background-position: center;\\n    background-size:cover ;\\n    display: grid;\\n    grid-template-columns: repeat(1,1fr);\\n    align-items: center;\\n    h5{\\n        color: #a4936d;\\n        font-size: 16px;\\n        text-transform: capitalize;\\n        font-weight: 500;\\n    }\\n    h1{\\n        color: #000;\\n        font-size: 65px;\\n        text-transform: capitalize;\\n        line-height: 1.1;\\n        font-weight: 600;\\n        margin: 6px 0 10px;\\n    }\\n    h6{\\n        color: #333c65;\\n        font-size: 20px;\\n        font-style: italic;\\n        margin-bottom: 20px;\\n    }\\n    p{\\n        display: inline-block;\\n        color: #111;\\n        font-size: 16px;\\n        font-weight: 500;\\n        text-transform: capitalize;\\n        border: 2px solid #111;\\n        width: 130px;\\n        height: 50px;\\n        padding: 12px 25px;\\n        transition: all .42s ease;\\n        cursor: pointer;\\n    }\\n    p:hover{\\n        background-color: #000;\\n        color: white;\\n    }\\n}\\n.center-text[_ngcontent-%COMP%]{\\n    h2{\\n        color:#111;\\n        font-size: 28px;\\n        text-transform: capitalize;\\n        text-align: center;\\n        margin-bottom: 30px;\\n    }\\n    span{\\n        color: #a4936d;\\n\\n    }\\n}\\n.grid-Products[_ngcontent-%COMP%] {\\n    display: grid;\\n    grid-template-columns: repeat(4, 1fr);\\n    gap: 20px; \\n\\n    justify-content: space-between; \\n\\n}\\n\\n.grid-row[_ngcontent-%COMP%] {\\n  text-align: center; \\n\\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1); \\n\\n  padding: 10px; \\n\\n  border: 1px solid #ccc; \\n\\n}\\n\\n.grid-row[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    width: 100%;\\n    height: auto;\\n    border-radius: 5px; \\n\\n}\\n\\n.grid-row[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]:hover {\\n    transform: scale(0.9); \\n\\n}\\n.product-text[_ngcontent-%COMP%]{\\n    h5{\\n        position: absolute;\\n        top: 13px;\\n        left: 13px;\\n        color: #fff;\\n        font-size: 12px;\\n        font-weight: 500;\\n    }\\n}\\n.client-reviews[_ngcontent-%COMP%]{\\n    background-color: #F3F4F6;\\n}\\n.reviews[_ngcontent-%COMP%]{\\n    text-align: center;\\n    h3{\\n        color: #111;\\n        font-size: 25px;\\n        text-transform: capitalize;\\n        text-align: center;\\n        font-weight: 700;\\n    }\\n    img{\\n        width: 100px;\\n        height: auto;\\n        border-radius: 3000px;\\n        margin: 10px 0;\\n    }\\n    p{\\n        color: #707070;\\n        font-size: 16px;\\n        font-weight: 400;\\n        line-height: 25px;\\n        margin-bottom: 10px;\\n    }\\n    h2{\\n        font-size: 22px;\\n        color: #000;\\n        font-weight: 400;\\n        text-transform: capitalize;\\n        margin-bottom: 2px;\\n    }\\n}\\n@media(max-width:630px){\\n    .main-text[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{\\n        font-size: 50px;\\n        transition: .4s;\\n    }\\n    .main-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{\\n        font-size: 18px;\\n        transition: .4s;\\n    }\\n}\\n.contact[_ngcontent-%COMP%]{\\n    background-color: #fff;\\n    border: #F3F4F6,double;\\n    font-family:'Courier New', Courier, monospace;\\n}\\n.contact-info[_ngcontent-%COMP%]{\\n    display: grid;\\n    grid-template-columns: repeat(auto-fit,minmax(160px,auto));\\n    gap:3rem\\n}\\n.contact-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{\\n    color:#212529;\\n    font-size: 14px;\\n    text-transform: uppercase;\\n    margin-bottom: 10px;\\n}\\n.first-info[_ngcontent-%COMP%]{\\n    img{\\n        width: 140px;\\n        height: auto;\\n\\n    }\\n}\\n.contact-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{\\n    color:#565656;\\n    font-size: 14px;\\n    font-weight: 400;\\n    text-transform: capitalize;\\n    line-height: 1.5;\\n    margin-bottom: 10px;\\n    cursor: pointer;\\n    transition: all .42s;\\n}\\n.contact-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:hover{\\n    color: #a4936d;\\n}\\n.social-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{\\n    color: #565656;\\n    margin-right: 10px;\\n    font-size: 20px;\\n    transition: all .42s;\\n\\n}\\n.social-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]:hover{\\n    color: rgb(23, 23, 127);\\n    transform: scale(1.3);\\n}\\n.end-text[_ngcontent-%COMP%]{\\n    background-color: #edfff1;\\n    text-align: center;\\n    padding: 20px;\\n}\\n.end-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{\\n    color: #111;\\n    text-transform: capitalize;\\n\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9ob21lL2hvbWUuY29tcG9uZW50LmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtJQUNJLFNBQVM7SUFDVCxVQUFVO0lBQ1Ysc0JBQXNCO0lBQ3RCLHVCQUF1QjtJQUN2Qix5QkFBeUI7SUFDekIsZ0JBQWdCO0lBQ2hCLHFCQUFxQjtBQUN6QjtBQUNBO0lBQ0ksZUFBZTtJQUNmLFdBQVc7SUFDWCxhQUFhO0lBQ2Isb0NBQTJDO0lBQzNDLDJCQUEyQjtJQUMzQixzQkFBc0I7SUFDdEIsYUFBYTtJQUNiLG9DQUFvQztJQUNwQyxtQkFBbUI7SUFDbkI7UUFDSSxjQUFjO1FBQ2QsZUFBZTtRQUNmLDBCQUEwQjtRQUMxQixnQkFBZ0I7SUFDcEI7SUFDQTtRQUNJLFdBQVc7UUFDWCxlQUFlO1FBQ2YsMEJBQTBCO1FBQzFCLGdCQUFnQjtRQUNoQixnQkFBZ0I7UUFDaEIsa0JBQWtCO0lBQ3RCO0lBQ0E7UUFDSSxjQUFjO1FBQ2QsZUFBZTtRQUNmLGtCQUFrQjtRQUNsQixtQkFBbUI7SUFDdkI7SUFDQTtRQUNJLHFCQUFxQjtRQUNyQixXQUFXO1FBQ1gsZUFBZTtRQUNmLGdCQUFnQjtRQUNoQiwwQkFBMEI7UUFDMUIsc0JBQXNCO1FBQ3RCLFlBQVk7UUFDWixZQUFZO1FBQ1osa0JBQWtCO1FBQ2xCLHlCQUF5QjtRQUN6QixlQUFlO0lBQ25CO0lBQ0E7UUFDSSxzQkFBc0I7UUFDdEIsWUFBWTtJQUNoQjtBQUNKO0FBQ0E7SUFDSTtRQUNJLFVBQVU7UUFDVixlQUFlO1FBQ2YsMEJBQTBCO1FBQzFCLGtCQUFrQjtRQUNsQixtQkFBbUI7SUFDdkI7SUFDQTtRQUNJLGNBQWM7O0lBRWxCO0FBQ0o7QUFDQTtJQUNJLGFBQWE7SUFDYixxQ0FBcUM7SUFDckMsU0FBUyxFQUFFLDBCQUEwQjtJQUNyQyw4QkFBOEIsRUFBRSxnQkFBZ0I7QUFDcEQ7O0FBRUE7RUFDRSxrQkFBa0IsRUFBRSwyQkFBMkI7RUFDL0Msd0NBQXdDLEVBQUUsdUNBQXVDO0VBQ2pGLGFBQWEsRUFBRSwwQkFBMEI7RUFDekMsc0JBQXNCLEVBQUUseUJBQXlCO0FBQ25EOztBQUVBO0lBQ0ksV0FBVztJQUNYLFlBQVk7SUFDWixrQkFBa0IsRUFBRSw4QkFBOEI7QUFDdEQ7O0FBRUE7SUFDSSxxQkFBcUIsRUFBRSxxQkFBcUI7QUFDaEQ7QUFDQTtJQUNJO1FBQ0ksa0JBQWtCO1FBQ2xCLFNBQVM7UUFDVCxVQUFVO1FBQ1YsV0FBVztRQUNYLGVBQWU7UUFDZixnQkFBZ0I7SUFDcEI7QUFDSjtBQUNBO0lBQ0kseUJBQXlCO0FBQzdCO0FBQ0E7SUFDSSxrQkFBa0I7SUFDbEI7UUFDSSxXQUFXO1FBQ1gsZUFBZTtRQUNmLDBCQUEwQjtRQUMxQixrQkFBa0I7UUFDbEIsZ0JBQWdCO0lBQ3BCO0lBQ0E7UUFDSSxZQUFZO1FBQ1osWUFBWTtRQUNaLHFCQUFxQjtRQUNyQixjQUFjO0lBQ2xCO0lBQ0E7UUFDSSxjQUFjO1FBQ2QsZUFBZTtRQUNmLGdCQUFnQjtRQUNoQixpQkFBaUI7UUFDakIsbUJBQW1CO0lBQ3ZCO0lBQ0E7UUFDSSxlQUFlO1FBQ2YsV0FBVztRQUNYLGdCQUFnQjtRQUNoQiwwQkFBMEI7UUFDMUIsa0JBQWtCO0lBQ3RCO0FBQ0o7QUFDQTtJQUNJO1FBQ0ksZUFBZTtRQUNmLGVBQWU7SUFDbkI7SUFDQTtRQUNJLGVBQWU7UUFDZixlQUFlO0lBQ25CO0FBQ0o7QUFDQTtJQUNJLHNCQUFzQjtJQUN0QixzQkFBc0I7SUFDdEIsNkNBQTZDO0FBQ2pEO0FBQ0E7SUFDSSxhQUFhO0lBQ2IsMERBQTBEO0lBQzFEO0FBQ0o7QUFDQTtJQUNJLGFBQWE7SUFDYixlQUFlO0lBQ2YseUJBQXlCO0lBQ3pCLG1CQUFtQjtBQUN2QjtBQUNBO0lBQ0k7UUFDSSxZQUFZO1FBQ1osWUFBWTs7SUFFaEI7QUFDSjtBQUNBO0lBQ0ksYUFBYTtJQUNiLGVBQWU7SUFDZixnQkFBZ0I7SUFDaEIsMEJBQTBCO0lBQzFCLGdCQUFnQjtJQUNoQixtQkFBbUI7SUFDbkIsZUFBZTtJQUNmLG9CQUFvQjtBQUN4QjtBQUNBO0lBQ0ksY0FBYztBQUNsQjtBQUNBO0lBQ0ksY0FBYztJQUNkLGtCQUFrQjtJQUNsQixlQUFlO0lBQ2Ysb0JBQW9COztBQUV4QjtBQUNBO0lBQ0ksdUJBQXVCO0lBQ3ZCLHFCQUFxQjtBQUN6QjtBQUNBO0lBQ0kseUJBQXlCO0lBQ3pCLGtCQUFrQjtJQUNsQixhQUFhO0FBQ2pCO0FBQ0E7SUFDSSxXQUFXO0lBQ1gsMEJBQTBCOztBQUU5QiIsInNvdXJjZXNDb250ZW50IjpbIip7XHJcbiAgICBtYXJnaW46IDA7XHJcbiAgICBwYWRkaW5nOiAwO1xyXG4gICAgYm94LXNpemluZzogYm9yZGVyLWJveDtcclxuICAgIHNjcm9sbC1iZWhhdmlvcjogc21vb3RoO1xyXG4gICAgZm9udC1mYW1pbHk6ICdzYW5zLXNlcmlmJztcclxuICAgIGxpc3Qtc3R5bGU6IG5vbmU7XHJcbiAgICB0ZXh0LWRlY29yYXRpb246IG5vbmU7XHJcbn1cclxuLnNlY3Rpb257XHJcbiAgICBwYWRkaW5nOiA1JSAxMCU7XHJcbiAgICB3aWR0aDogMTAwJTtcclxuICAgIGhlaWdodDogMTAwdmg7XHJcbiAgICBiYWNrZ3JvdW5kLWltYWdlOnVybCgnaW1hZ2VzL2Jhbm5lci0zLnBuZycpO1xyXG4gICAgYmFja2dyb3VuZC1wb3NpdGlvbjogY2VudGVyO1xyXG4gICAgYmFja2dyb3VuZC1zaXplOmNvdmVyIDtcclxuICAgIGRpc3BsYXk6IGdyaWQ7XHJcbiAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IHJlcGVhdCgxLDFmcik7XHJcbiAgICBhbGlnbi1pdGVtczogY2VudGVyO1xyXG4gICAgaDV7XHJcbiAgICAgICAgY29sb3I6ICNhNDkzNmQ7XHJcbiAgICAgICAgZm9udC1zaXplOiAxNnB4O1xyXG4gICAgICAgIHRleHQtdHJhbnNmb3JtOiBjYXBpdGFsaXplO1xyXG4gICAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7XHJcbiAgICB9XHJcbiAgICBoMXtcclxuICAgICAgICBjb2xvcjogIzAwMDtcclxuICAgICAgICBmb250LXNpemU6IDY1cHg7XHJcbiAgICAgICAgdGV4dC10cmFuc2Zvcm06IGNhcGl0YWxpemU7XHJcbiAgICAgICAgbGluZS1oZWlnaHQ6IDEuMTtcclxuICAgICAgICBmb250LXdlaWdodDogNjAwO1xyXG4gICAgICAgIG1hcmdpbjogNnB4IDAgMTBweDtcclxuICAgIH1cclxuICAgIGg2e1xyXG4gICAgICAgIGNvbG9yOiAjMzMzYzY1O1xyXG4gICAgICAgIGZvbnQtc2l6ZTogMjBweDtcclxuICAgICAgICBmb250LXN0eWxlOiBpdGFsaWM7XHJcbiAgICAgICAgbWFyZ2luLWJvdHRvbTogMjBweDtcclxuICAgIH1cclxuICAgIHB7XHJcbiAgICAgICAgZGlzcGxheTogaW5saW5lLWJsb2NrO1xyXG4gICAgICAgIGNvbG9yOiAjMTExO1xyXG4gICAgICAgIGZvbnQtc2l6ZTogMTZweDtcclxuICAgICAgICBmb250LXdlaWdodDogNTAwO1xyXG4gICAgICAgIHRleHQtdHJhbnNmb3JtOiBjYXBpdGFsaXplO1xyXG4gICAgICAgIGJvcmRlcjogMnB4IHNvbGlkICMxMTE7XHJcbiAgICAgICAgd2lkdGg6IDEzMHB4O1xyXG4gICAgICAgIGhlaWdodDogNTBweDtcclxuICAgICAgICBwYWRkaW5nOiAxMnB4IDI1cHg7XHJcbiAgICAgICAgdHJhbnNpdGlvbjogYWxsIC40MnMgZWFzZTtcclxuICAgICAgICBjdXJzb3I6IHBvaW50ZXI7XHJcbiAgICB9XHJcbiAgICBwOmhvdmVye1xyXG4gICAgICAgIGJhY2tncm91bmQtY29sb3I6ICMwMDA7XHJcbiAgICAgICAgY29sb3I6IHdoaXRlO1xyXG4gICAgfVxyXG59XHJcbi5jZW50ZXItdGV4dHtcclxuICAgIGgye1xyXG4gICAgICAgIGNvbG9yOiMxMTE7XHJcbiAgICAgICAgZm9udC1zaXplOiAyOHB4O1xyXG4gICAgICAgIHRleHQtdHJhbnNmb3JtOiBjYXBpdGFsaXplO1xyXG4gICAgICAgIHRleHQtYWxpZ246IGNlbnRlcjtcclxuICAgICAgICBtYXJnaW4tYm90dG9tOiAzMHB4O1xyXG4gICAgfVxyXG4gICAgc3BhbntcclxuICAgICAgICBjb2xvcjogI2E0OTM2ZDtcclxuXHJcbiAgICB9XHJcbn1cclxuLmdyaWQtUHJvZHVjdHMge1xyXG4gICAgZGlzcGxheTogZ3JpZDtcclxuICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KDQsIDFmcik7XHJcbiAgICBnYXA6IDIwcHg7IC8qIFNwYWNpbmcgYmV0d2VlbiBpdGVtcyAqL1xyXG4gICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOyAvKiBBbGlnbiBpdGVtcyAqL1xyXG59XHJcblxyXG4uZ3JpZC1yb3cge1xyXG4gIHRleHQtYWxpZ246IGNlbnRlcjsgLyogQ2VudGVyIHRleHQgYW5kIGltYWdlcyAqL1xyXG4gIGJveC1zaGFkb3c6IDAgMnB4IDVweCByZ2JhKDAsIDAsIDAsIDAuMSk7IC8qIE9wdGlvbmFsOiBBZGQgYSBzaGFkb3cgZm9yIHN0eWxpbmcgKi9cclxuICBwYWRkaW5nOiAxMHB4OyAvKiBPcHRpb25hbDogQWRkIHBhZGRpbmcgKi9cclxuICBib3JkZXI6IDFweCBzb2xpZCAjY2NjOyAvKiBPcHRpb25hbDogQWRkIGJvcmRlciAqL1xyXG59XHJcblxyXG4uZ3JpZC1yb3cgaW1nIHtcclxuICAgIHdpZHRoOiAxMDAlO1xyXG4gICAgaGVpZ2h0OiBhdXRvO1xyXG4gICAgYm9yZGVyLXJhZGl1czogNXB4OyAvKiBPcHRpb25hbDogUm91bmRlZCBjb3JuZXJzICovXHJcbn1cclxuXHJcbi5ncmlkLXJvdyBpbWc6aG92ZXIge1xyXG4gICAgdHJhbnNmb3JtOiBzY2FsZSgwLjkpOyAvKiBBZGQgaG92ZXIgZWZmZWN0ICovXHJcbn1cclxuLnByb2R1Y3QtdGV4dHtcclxuICAgIGg1e1xyXG4gICAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTtcclxuICAgICAgICB0b3A6IDEzcHg7XHJcbiAgICAgICAgbGVmdDogMTNweDtcclxuICAgICAgICBjb2xvcjogI2ZmZjtcclxuICAgICAgICBmb250LXNpemU6IDEycHg7XHJcbiAgICAgICAgZm9udC13ZWlnaHQ6IDUwMDtcclxuICAgIH1cclxufVxyXG4uY2xpZW50LXJldmlld3N7XHJcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjRjNGNEY2O1xyXG59XHJcbi5yZXZpZXdze1xyXG4gICAgdGV4dC1hbGlnbjogY2VudGVyO1xyXG4gICAgaDN7XHJcbiAgICAgICAgY29sb3I6ICMxMTE7XHJcbiAgICAgICAgZm9udC1zaXplOiAyNXB4O1xyXG4gICAgICAgIHRleHQtdHJhbnNmb3JtOiBjYXBpdGFsaXplO1xyXG4gICAgICAgIHRleHQtYWxpZ246IGNlbnRlcjtcclxuICAgICAgICBmb250LXdlaWdodDogNzAwO1xyXG4gICAgfVxyXG4gICAgaW1ne1xyXG4gICAgICAgIHdpZHRoOiAxMDBweDtcclxuICAgICAgICBoZWlnaHQ6IGF1dG87XHJcbiAgICAgICAgYm9yZGVyLXJhZGl1czogMzAwMHB4O1xyXG4gICAgICAgIG1hcmdpbjogMTBweCAwO1xyXG4gICAgfVxyXG4gICAgcHtcclxuICAgICAgICBjb2xvcjogIzcwNzA3MDtcclxuICAgICAgICBmb250LXNpemU6IDE2cHg7XHJcbiAgICAgICAgZm9udC13ZWlnaHQ6IDQwMDtcclxuICAgICAgICBsaW5lLWhlaWdodDogMjVweDtcclxuICAgICAgICBtYXJnaW4tYm90dG9tOiAxMHB4O1xyXG4gICAgfVxyXG4gICAgaDJ7XHJcbiAgICAgICAgZm9udC1zaXplOiAyMnB4O1xyXG4gICAgICAgIGNvbG9yOiAjMDAwO1xyXG4gICAgICAgIGZvbnQtd2VpZ2h0OiA0MDA7XHJcbiAgICAgICAgdGV4dC10cmFuc2Zvcm06IGNhcGl0YWxpemU7XHJcbiAgICAgICAgbWFyZ2luLWJvdHRvbTogMnB4O1xyXG4gICAgfVxyXG59XHJcbkBtZWRpYShtYXgtd2lkdGg6NjMwcHgpe1xyXG4gICAgLm1haW4tdGV4dCBoMXtcclxuICAgICAgICBmb250LXNpemU6IDUwcHg7XHJcbiAgICAgICAgdHJhbnNpdGlvbjogLjRzO1xyXG4gICAgfVxyXG4gICAgLm1haW4tdGV4dCBwe1xyXG4gICAgICAgIGZvbnQtc2l6ZTogMThweDtcclxuICAgICAgICB0cmFuc2l0aW9uOiAuNHM7XHJcbiAgICB9XHJcbn1cclxuLmNvbnRhY3R7XHJcbiAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZmZmO1xyXG4gICAgYm9yZGVyOiAjRjNGNEY2LGRvdWJsZTtcclxuICAgIGZvbnQtZmFtaWx5OidDb3VyaWVyIE5ldycsIENvdXJpZXIsIG1vbm9zcGFjZTtcclxufVxyXG4uY29udGFjdC1pbmZve1xyXG4gICAgZGlzcGxheTogZ3JpZDtcclxuICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KGF1dG8tZml0LG1pbm1heCgxNjBweCxhdXRvKSk7XHJcbiAgICBnYXA6M3JlbVxyXG59XHJcbi5jb250YWN0LWluZm8gaDR7XHJcbiAgICBjb2xvcjojMjEyNTI5O1xyXG4gICAgZm9udC1zaXplOiAxNHB4O1xyXG4gICAgdGV4dC10cmFuc2Zvcm06IHVwcGVyY2FzZTtcclxuICAgIG1hcmdpbi1ib3R0b206IDEwcHg7XHJcbn1cclxuLmZpcnN0LWluZm97XHJcbiAgICBpbWd7XHJcbiAgICAgICAgd2lkdGg6IDE0MHB4O1xyXG4gICAgICAgIGhlaWdodDogYXV0bztcclxuXHJcbiAgICB9XHJcbn1cclxuLmNvbnRhY3QtaW5mbyBwe1xyXG4gICAgY29sb3I6IzU2NTY1NjtcclxuICAgIGZvbnQtc2l6ZTogMTRweDtcclxuICAgIGZvbnQtd2VpZ2h0OiA0MDA7XHJcbiAgICB0ZXh0LXRyYW5zZm9ybTogY2FwaXRhbGl6ZTtcclxuICAgIGxpbmUtaGVpZ2h0OiAxLjU7XHJcbiAgICBtYXJnaW4tYm90dG9tOiAxMHB4O1xyXG4gICAgY3Vyc29yOiBwb2ludGVyO1xyXG4gICAgdHJhbnNpdGlvbjogYWxsIC40MnM7XHJcbn1cclxuLmNvbnRhY3QtaW5mbyBwOmhvdmVye1xyXG4gICAgY29sb3I6ICNhNDkzNmQ7XHJcbn1cclxuLnNvY2lhbC1pY29uIGl7XHJcbiAgICBjb2xvcjogIzU2NTY1NjtcclxuICAgIG1hcmdpbi1yaWdodDogMTBweDtcclxuICAgIGZvbnQtc2l6ZTogMjBweDtcclxuICAgIHRyYW5zaXRpb246IGFsbCAuNDJzO1xyXG5cclxufVxyXG4uc29jaWFsLWljb24gaTpob3ZlcntcclxuICAgIGNvbG9yOiByZ2IoMjMsIDIzLCAxMjcpO1xyXG4gICAgdHJhbnNmb3JtOiBzY2FsZSgxLjMpO1xyXG59XHJcbi5lbmQtdGV4dHtcclxuICAgIGJhY2tncm91bmQtY29sb3I6ICNlZGZmZjE7XHJcbiAgICB0ZXh0LWFsaWduOiBjZW50ZXI7XHJcbiAgICBwYWRkaW5nOiAyMHB4O1xyXG59XHJcbi5lbmQtdGV4dCBwe1xyXG4gICAgY29sb3I6ICMxMTE7XHJcbiAgICB0ZXh0LXRyYW5zZm9ybTogY2FwaXRhbGl6ZTtcclxuXHJcbn0iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵpropertyInterpolate", "item_r2", "title", "ɵɵproperty", "imgSrc", "ɵɵsanitizeUrl", "ɵɵtextInterpolate", "description", "ɵɵtemplate", "HomeComponent_div_20_div_1_Template", "ctx_r0", "items", "HomeComponent", "constructor", "router", "http", "ngOnInit", "get", "subscribe", "next", "data", "console", "log", "error", "err", "navigateToShop", "navigate", "ɵɵdirectiveInject", "i1", "Router", "i2", "HttpClient", "selectors", "decls", "vars", "consts", "template", "HomeComponent_Template", "rf", "ctx", "ɵɵlistener", "HomeComponent_Template_p_click_9_listener", "HomeComponent_div_20_Template", "ɵɵpipeBind1", "length"], "sources": ["C:\\Users\\<USER>\\Desktop\\projet angular\\Projet\\src\\home\\home.component.ts", "C:\\Users\\<USER>\\Desktop\\projet angular\\Projet\\src\\home\\home.component.html"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\nimport { Component } from '@angular/core';\nimport { Router } from '@angular/router';\n\n@Component({\n  selector: 'app-home',\n  templateUrl: './home.component.html',\n  styleUrls: ['./home.component.css']\n})\nexport class HomeComponent {\n  items: any[] = [];\n\n  constructor(private router: Router, private http: HttpClient) {}\n\n  ngOnInit(): void {\n    this.http.get<any[]>('assets/products.json').subscribe({\n      next: (data) => {\n        this.items = data;\n        console.log('Loaded items:', this.items); // <-- Add this\n      },\n      error: (err) => {\n        console.error('Error loading JSON:', err); // <-- Add this\n      }\n    });}\n\n  navigateToShop() {\n    this.router.navigate(['/shop']);\n}\n}\n", "<div class=\"section\"><h5>2025 collection</h5>\n    <h1>New<br>collection 2025</h1>\n    <h6 >There's nothing like trend</h6>\n    <p (click)=\"navigateToShop()\">Shop now</p></div>\n    <div class=\"trending-product\">\n        <div class=\"center-text\">\n            <h2>Our tranding <span>Products</span></h2>\n        </div>\n        <pre>{{ items | json }}</pre>\n\n<div class=\"grid-Products\" *ngIf=\"items.length > 0\">\n  <div class=\"grid-row\" *ngFor=\"let item of items\">\n    <img [src]=\"item.imgSrc\" alt=\"{{ item.title }}\">\n\n    <div class=\"product-text\">\n      <h5>Sale</h5>\n    </div>\n\n    <div class=\"heart-icon\">\n      <i class='bx bx-heart'></i>\n    </div>\n\n    <div class=\"ratting\">\n      <i class='bx bxs-star'></i>\n      <i class='bx bxs-star'></i>\n      <i class='bx bxs-star'></i>\n      <i class='bx bxs-star'></i>\n      <i class='bx bxs-star-half'></i>\n    </div>\n\n    <div class=\"price\">\n      <h4>{{ item.title }}</h4>\n      <h4>{{ item.description }}</h4>\n      <p>50dt</p>\n    </div>\n  </div>\n</div>\n\n        <br><br>\n        <section class=\"client-reviews\">\n            <div class=\"reviews\">\n                <h3>Client Reviews</h3>\n                <img src=\"assets/woman.jpg\"  alt=\"\">\n                <p>Hello, my name is Sarah, and I’ve been shopping for stylish and comfortable women’s clothing online for years. I’m always looking for outfits that reflect my personality—elegant yet practical for my busy lifestyle. I appreciate a website that's easy to navigate with clear size guides, diverse collections, and quality fabrics. Fast delivery and excellent customer service are also a must for me. A good online shopping experience keeps me coming back for more!</p>\n                <h2>Sarah</h2>\n                <p>CEO of Addle</p>\n            </div>\n        </section>\n    </div>\n    <br><br>\n<section class=\"contact\">\n    <div class=\"contact-info\">\n        <div class=\"first-info\">\n            <img src=\"assets/women's wear.png\" alt=\"\">\n            <p>123 Olive Street, Sidi Bouzid, 9100<br> Tunisia</p>\n            <p><EMAIL></p>\n            <div class=\"social-icon\">\n                <a href=\"https://www.facebook.com/facebook/\"><i class='bx bxl-facebook-circle'></i></a>\n                <a href=\"https://x.com/\"><i class='bx bxl-twitter'></i></a>\n                <a href=\"https://www.instagram.com/instagram/\"><i class='bx bxl-instagram'></i></a>\n                <a href=\"https://www.youtube.com/@YouTube\"><i class='bx bxl-youtube'></i></a>\n                <a href=\"https://www.linkedin.com/feed/\"><i class='bx bxl-linkedin'></i></a>\n            </div>\n        </div>\n        <div class=\"second-info\">\n            <h4>Support</h4>\n            <p>About us</p>\n            <p>Contact us</p>\n            <p>size guide</p>\n            <p>Privacy</p>\n            <p></p>\n        </div>\n        <div class=\"third-info\">\n            <h4>Company</h4>\n            <p>About</p>\n            <p>Blog</p>\n            <p>Affiliate</p>\n            <p>Login</p>\n        </div>\n    </div>\n</section>\n"], "mappings": ";;;;;;ICWEA,EAAA,CAAAC,cAAA,cAAiD;IAC/CD,EAAA,CAAAE,SAAA,cAAgD;IAEhDF,EAAA,CAAAC,cAAA,cAA0B;IACpBD,EAAA,CAAAG,MAAA,WAAI;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAGfJ,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAE,SAAA,YAA2B;IAC7BF,EAAA,CAAAI,YAAA,EAAM;IAENJ,EAAA,CAAAC,cAAA,cAAqB;IACnBD,EAAA,CAAAE,SAAA,YAA2B;IAK7BF,EAAA,CAAAI,YAAA,EAAM;IAENJ,EAAA,CAAAC,cAAA,eAAmB;IACbD,EAAA,CAAAG,MAAA,IAAgB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACzBJ,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAG,MAAA,IAAsB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC/BJ,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAG,MAAA,YAAI;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;IArBYJ,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAAM,qBAAA,QAAAC,OAAA,CAAAC,KAAA,CAAsB;IAA1CR,EAAA,CAAAS,UAAA,QAAAF,OAAA,CAAAG,MAAA,EAAAV,EAAA,CAAAW,aAAA,CAAmB;IAmBlBX,EAAA,CAAAK,SAAA,IAAgB;IAAhBL,EAAA,CAAAY,iBAAA,CAAAL,OAAA,CAAAC,KAAA,CAAgB;IAChBR,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAAY,iBAAA,CAAAL,OAAA,CAAAM,WAAA,CAAsB;;;;;IAtBhCb,EAAA,CAAAC,cAAA,cAAoD;IAClDD,EAAA,CAAAc,UAAA,IAAAC,mCAAA,mBAwBM;IACRf,EAAA,CAAAI,YAAA,EAAM;;;;IAzBmCJ,EAAA,CAAAK,SAAA,GAAQ;IAARL,EAAA,CAAAS,UAAA,YAAAO,MAAA,CAAAC,KAAA,CAAQ;;;ADFjD,OAAM,MAAOC,aAAa;EAGxBC,YAAoBC,MAAc,EAAUC,IAAgB;IAAxC,KAAAD,MAAM,GAANA,MAAM;IAAkB,KAAAC,IAAI,GAAJA,IAAI;IAFhD,KAAAJ,KAAK,GAAU,EAAE;EAE8C;EAE/DK,QAAQA,CAAA;IACN,IAAI,CAACD,IAAI,CAACE,GAAG,CAAQ,sBAAsB,CAAC,CAACC,SAAS,CAAC;MACrDC,IAAI,EAAGC,IAAI,IAAI;QACb,IAAI,CAACT,KAAK,GAAGS,IAAI;QACjBC,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,IAAI,CAACX,KAAK,CAAC,CAAC,CAAC;MAC5C,CAAC;;MACDY,KAAK,EAAGC,GAAG,IAAI;QACbH,OAAO,CAACE,KAAK,CAAC,qBAAqB,EAAEC,GAAG,CAAC,CAAC,CAAC;MAC7C;KACD,CAAC;EAAC;;EAELC,cAAcA,CAAA;IACZ,IAAI,CAACX,MAAM,CAACY,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;EACnC;;;uBAlBad,aAAa,EAAAlB,EAAA,CAAAiC,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAnC,EAAA,CAAAiC,iBAAA,CAAAG,EAAA,CAAAC,UAAA;IAAA;EAAA;;;YAAbnB,aAAa;MAAAoB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCT1B5C,EAAA,CAAAC,cAAA,aAAqB;UAAID,EAAA,CAAAG,MAAA,sBAAe;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACzCJ,EAAA,CAAAC,cAAA,SAAI;UAAAD,EAAA,CAAAG,MAAA,UAAG;UAAAH,EAAA,CAAAE,SAAA,SAAI;UAAAF,EAAA,CAAAG,MAAA,sBAAe;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAC/BJ,EAAA,CAAAC,cAAA,SAAK;UAAAD,EAAA,CAAAG,MAAA,iCAA0B;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACpCJ,EAAA,CAAAC,cAAA,WAA8B;UAA3BD,EAAA,CAAA8C,UAAA,mBAAAC,0CAAA;YAAA,OAASF,GAAA,CAAAd,cAAA,EAAgB;UAAA,EAAC;UAAC/B,EAAA,CAAAG,MAAA,gBAAQ;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAC1CJ,EAAA,CAAAC,cAAA,cAA8B;UAElBD,EAAA,CAAAG,MAAA,qBAAa;UAAAH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAG,MAAA,gBAAQ;UAAAH,EAAA,CAAAI,YAAA,EAAO;UAE1CJ,EAAA,CAAAC,cAAA,WAAK;UAAAD,EAAA,CAAAG,MAAA,IAAkB;;UAAAH,EAAA,CAAAI,YAAA,EAAM;UAErCJ,EAAA,CAAAc,UAAA,KAAAkC,6BAAA,iBA0BM;UAEEhD,EAAA,CAAAE,SAAA,UAAI;UACJF,EAAA,CAAAC,cAAA,kBAAgC;UAEpBD,EAAA,CAAAG,MAAA,sBAAc;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACvBJ,EAAA,CAAAE,SAAA,cAAoC;UACpCF,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,yeAA6c;UAAAH,EAAA,CAAAI,YAAA,EAAI;UACpdJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,aAAK;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACdJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,oBAAY;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAI/BJ,EAAA,CAAAE,SAAA,UAAI;UACRF,EAAA,CAAAC,cAAA,kBAAyB;UAGbD,EAAA,CAAAE,SAAA,eAA0C;UAC1CF,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,2CAAmC;UAAAH,EAAA,CAAAE,SAAA,UAAI;UAACF,EAAA,CAAAG,MAAA,gBAAO;UAAAH,EAAA,CAAAI,YAAA,EAAI;UACtDJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,qCAA6B;UAAAH,EAAA,CAAAI,YAAA,EAAI;UACpCJ,EAAA,CAAAC,cAAA,eAAyB;UACwBD,EAAA,CAAAE,SAAA,aAAsC;UAAAF,EAAA,CAAAI,YAAA,EAAI;UACvFJ,EAAA,CAAAC,cAAA,aAAyB;UAAAD,EAAA,CAAAE,SAAA,aAA8B;UAAAF,EAAA,CAAAI,YAAA,EAAI;UAC3DJ,EAAA,CAAAC,cAAA,aAA+C;UAAAD,EAAA,CAAAE,SAAA,aAAgC;UAAAF,EAAA,CAAAI,YAAA,EAAI;UACnFJ,EAAA,CAAAC,cAAA,aAA2C;UAAAD,EAAA,CAAAE,SAAA,aAA8B;UAAAF,EAAA,CAAAI,YAAA,EAAI;UAC7EJ,EAAA,CAAAC,cAAA,aAAyC;UAAAD,EAAA,CAAAE,SAAA,aAA+B;UAAAF,EAAA,CAAAI,YAAA,EAAI;UAGpFJ,EAAA,CAAAC,cAAA,eAAyB;UACjBD,EAAA,CAAAG,MAAA,eAAO;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAChBJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,gBAAQ;UAAAH,EAAA,CAAAI,YAAA,EAAI;UACfJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,kBAAU;UAAAH,EAAA,CAAAI,YAAA,EAAI;UACjBJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,kBAAU;UAAAH,EAAA,CAAAI,YAAA,EAAI;UACjBJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,eAAO;UAAAH,EAAA,CAAAI,YAAA,EAAI;UACdJ,EAAA,CAAAE,SAAA,SAAO;UACXF,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAC,cAAA,eAAwB;UAChBD,EAAA,CAAAG,MAAA,eAAO;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAChBJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,aAAK;UAAAH,EAAA,CAAAI,YAAA,EAAI;UACZJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,YAAI;UAAAH,EAAA,CAAAI,YAAA,EAAI;UACXJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,iBAAS;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAChBJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,aAAK;UAAAH,EAAA,CAAAI,YAAA,EAAI;;;UArEXJ,EAAA,CAAAK,SAAA,IAAkB;UAAlBL,EAAA,CAAAY,iBAAA,CAAAZ,EAAA,CAAAiD,WAAA,QAAAJ,GAAA,CAAA5B,KAAA,EAAkB;UAEHjB,EAAA,CAAAK,SAAA,GAAsB;UAAtBL,EAAA,CAAAS,UAAA,SAAAoC,GAAA,CAAA5B,KAAA,CAAAiC,MAAA,KAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}