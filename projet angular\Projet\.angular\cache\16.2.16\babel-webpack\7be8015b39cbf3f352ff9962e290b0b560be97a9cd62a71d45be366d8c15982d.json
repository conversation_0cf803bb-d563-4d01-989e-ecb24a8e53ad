{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nexport let LoginComponent = class LoginComponent {\n  // Advances to the next step when user interacts with an input\n  checkStep(currentStep) {\n    // Ensure step is advanced only when the input is filled\n    if (currentStep === 1 && this.username) {\n      this.step = 2;\n    } else if (currentStep === 2 && this.governorate) {\n      this.step = 3;\n    } else if (currentStep === 3 && this.gender) {\n      this.step = 4;\n    } else if (currentStep === 4 && this.email) {\n      this.step = 5;\n    } else if (currentStep === 5 && this.phone) {\n      this.step = 6;\n    } else if (currentStep === 6 && this.password && this.confirmPassword) {\n      this.step = 7;\n    }\n  }\n  constructor(router) {\n    this.router = router;\n    this.step = 1; // Tracks the current visible field\n    this.username = '';\n    this.governorate = '';\n    this.gender = '';\n    this.email = '';\n    this.phone = '';\n    this.password = '';\n    this.confirmPassword = '';\n    this.isOver18 = false;\n    this.governorates = ['<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>i', '<PERSON>f', '<PERSON><PERSON>a', '<PERSON><PERSON><PERSON>', '<PERSON>denine', '<PERSON>stir', '<PERSON>beul', 'Sfax', '<PERSON>i <PERSON><PERSON>id', '<PERSON>liana', 'Sousse', '<PERSON>taouine', 'Tozeur', '<PERSON>nis', 'Zaghouan'];\n  }\n  canSubmit() {\n    // <PERSON> if all conditions are met\n    return this.step >= 7 && this.password === this.confirmPassword && this.isOver18;\n  }\n  onSubmit() {\n    // If conditions are met, navigate to the home page\n    if (this.canSubmit()) {\n      this.router.navigate(['/home']);\n    } else {\n      alert('Please ensure all fields are filled out correctly!');\n    }\n  }\n};\nLoginComponent = __decorate([Component({\n  selector: 'app-login',\n  templateUrl: './login.component.html',\n  styleUrls: ['./login.component.css']\n})], LoginComponent);", "map": {"version": 3, "names": ["Component", "LoginComponent", "checkStep", "currentStep", "username", "step", "governorate", "gender", "email", "phone", "password", "confirmPassword", "constructor", "router", "isOver18", "governorates", "canSubmit", "onSubmit", "navigate", "alert", "__decorate", "selector", "templateUrl", "styleUrls"], "sources": ["C:\\Users\\<USER>\\Desktop\\projet angular\\Projet\\src\\login\\login.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { Router } from '@angular/router';\n\n@Component({\n  selector: 'app-login',\n  templateUrl: './login.component.html',\n  styleUrls: ['./login.component.css']\n})\nexport class LoginComponent {\n  step: number = 1; // Tracks the current visible field\n  username: string = '';\n  governorate: string = '';\n  gender: string = '';\n  email: string = '';\n  phone: string = '';\n  password: string = '';\n  confirmPassword: string = '';\n  isOver18: boolean = false;\n\n  governorates: string[] = [\n    '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON> Arous', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>',\n    '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>',\n    '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>x', '<PERSON><PERSON> Bouzid', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>',\n    '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>',\n  ];\n  // Advances to the next step when user interacts with an input\n  checkStep(currentStep: number): void {\n    // Ensure step is advanced only when the input is filled\n    if (currentStep === 1 && this.username) {\n      this.step = 2;\n    } else if (currentStep === 2 && this.governorate) {\n      this.step = 3;}\n      else if (currentStep === 3 && this.gender) {\n      this.step = 4;\n    } else if (currentStep === 4 && this.email ) {\n      this.step = 5;\n    } else if (currentStep === 5 && this.phone) {\n      this.step = 6;\n    } else if (currentStep === 6 && this.password && this.confirmPassword) {\n      this.step = 7;\n    }\n  }\n\n  constructor(private router: Router) {}\n\n  canSubmit(): boolean {\n    // Check if all conditions are met\n    return (\n      this.step >= 7 &&\n      this.password === this.confirmPassword &&\n      this.isOver18\n    );\n  }\n\n  onSubmit() {\n    // If conditions are met, navigate to the home page\n    if (this.canSubmit()) {\n      this.router.navigate(['/home']);\n  } else {\n    alert('Please ensure all fields are filled out correctly!');\n  }\n}\n}\n"], "mappings": ";AAAA,SAASA,SAAS,QAAQ,eAAe;AAQlC,WAAMC,cAAc,GAApB,MAAMA,cAAc;EAiBzB;EACAC,SAASA,CAACC,WAAmB;IAC3B;IACA,IAAIA,WAAW,KAAK,CAAC,IAAI,IAAI,CAACC,QAAQ,EAAE;MACtC,IAAI,CAACC,IAAI,GAAG,CAAC;KACd,MAAM,IAAIF,WAAW,KAAK,CAAC,IAAI,IAAI,CAACG,WAAW,EAAE;MAChD,IAAI,CAACD,IAAI,GAAG,CAAC;KAAE,MACV,IAAIF,WAAW,KAAK,CAAC,IAAI,IAAI,CAACI,MAAM,EAAE;MAC3C,IAAI,CAACF,IAAI,GAAG,CAAC;KACd,MAAM,IAAIF,WAAW,KAAK,CAAC,IAAI,IAAI,CAACK,KAAK,EAAG;MAC3C,IAAI,CAACH,IAAI,GAAG,CAAC;KACd,MAAM,IAAIF,WAAW,KAAK,CAAC,IAAI,IAAI,CAACM,KAAK,EAAE;MAC1C,IAAI,CAACJ,IAAI,GAAG,CAAC;KACd,MAAM,IAAIF,WAAW,KAAK,CAAC,IAAI,IAAI,CAACO,QAAQ,IAAI,IAAI,CAACC,eAAe,EAAE;MACrE,IAAI,CAACN,IAAI,GAAG,CAAC;;EAEjB;EAEAO,YAAoBC,MAAc;IAAd,KAAAA,MAAM,GAANA,MAAM;IAlC1B,KAAAR,IAAI,GAAW,CAAC,CAAC,CAAC;IAClB,KAAAD,QAAQ,GAAW,EAAE;IACrB,KAAAE,WAAW,GAAW,EAAE;IACxB,KAAAC,MAAM,GAAW,EAAE;IACnB,KAAAC,KAAK,GAAW,EAAE;IAClB,KAAAC,KAAK,GAAW,EAAE;IAClB,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAC,eAAe,GAAW,EAAE;IAC5B,KAAAG,QAAQ,GAAY,KAAK;IAEzB,KAAAC,YAAY,GAAa,CACvB,QAAQ,EAAE,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,UAAU,EACtE,UAAU,EAAE,WAAW,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,EACzE,UAAU,EAAE,QAAQ,EAAE,MAAM,EAAE,aAAa,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,EAC7E,QAAQ,EAAE,OAAO,EAAE,UAAU,CAC9B;EAmBoC;EAErCC,SAASA,CAAA;IACP;IACA,OACE,IAAI,CAACX,IAAI,IAAI,CAAC,IACd,IAAI,CAACK,QAAQ,KAAK,IAAI,CAACC,eAAe,IACtC,IAAI,CAACG,QAAQ;EAEjB;EAEAG,QAAQA,CAAA;IACN;IACA,IAAI,IAAI,CAACD,SAAS,EAAE,EAAE;MACpB,IAAI,CAACH,MAAM,CAACK,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;KAClC,MAAM;MACLC,KAAK,CAAC,oDAAoD,CAAC;;EAE/D;CACC;AAtDYlB,cAAc,GAAAmB,UAAA,EAL1BpB,SAAS,CAAC;EACTqB,QAAQ,EAAE,WAAW;EACrBC,WAAW,EAAE,wBAAwB;EACrCC,SAAS,EAAE,CAAC,uBAAuB;CACpC,CAAC,C,EACWtB,cAAc,CAsD1B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}