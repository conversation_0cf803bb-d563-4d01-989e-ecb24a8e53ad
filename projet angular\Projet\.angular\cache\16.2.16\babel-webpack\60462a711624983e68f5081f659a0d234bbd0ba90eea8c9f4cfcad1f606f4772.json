{"ast": null, "code": "export function arrRemove(arr, item) {\n  if (arr) {\n    const index = arr.indexOf(item);\n    0 <= index && arr.splice(index, 1);\n  }\n}", "map": {"version": 3, "names": ["arr<PERSON><PERSON><PERSON>", "arr", "item", "index", "indexOf", "splice"], "sources": ["C:/Users/<USER>/Desktop/projet angular/Projet/node_modules/rxjs/dist/esm/internal/util/arrRemove.js"], "sourcesContent": ["export function arrRemove(arr, item) {\n    if (arr) {\n        const index = arr.indexOf(item);\n        0 <= index && arr.splice(index, 1);\n    }\n}\n"], "mappings": "AAAA,OAAO,SAASA,SAASA,CAACC,GAAG,EAAEC,IAAI,EAAE;EACjC,IAAID,GAAG,EAAE;IACL,MAAME,KAAK,GAAGF,GAAG,CAACG,OAAO,CAACF,IAAI,CAAC;IAC/B,CAAC,IAAIC,KAAK,IAAIF,GAAG,CAACI,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;EACtC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}