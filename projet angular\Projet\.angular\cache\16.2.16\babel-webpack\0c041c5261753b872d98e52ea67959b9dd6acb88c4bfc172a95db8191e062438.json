{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nfunction LoginComponent_option_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const gov_r2 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(gov_r2);\n  }\n}\nfunction LoginComponent_div_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.errorMessage, \" \");\n  }\n}\nexport class LoginComponent {\n  // Advances to the next step when user interacts with an input\n  checkStep(currentStep) {\n    // Ensure step is advanced only when the input is filled\n    if (currentStep === 1 && this.username) {\n      this.step = 2;\n    } else if (currentStep === 2 && this.governorate) {\n      this.step = 3;\n    } else if (currentStep === 3 && this.gender) {\n      this.step = 4;\n    } else if (currentStep === 4 && this.email && this.phone) {\n      this.step = 5;\n    } else if (currentStep === 5) {\n      if (this.password === this.confirmPassword) {\n        this.step = 6;\n      }\n    }\n  }\n  constructor(router) {\n    this.router = router;\n    this.step = 1; // Tracks the current visible field\n    this.username = '';\n    this.governorate = '';\n    this.gender = '';\n    this.email = '';\n    this.phone = '';\n    this.password = '';\n    this.confirmPassword = '';\n    this.errorMessage = \"\";\n    this.governorates = ['Ariana', 'Béja', 'Ben Arous', 'Bizerte', 'Gabès', 'Gafsa', 'Jendouba', 'Kairouan', 'Kasserine', 'Kebili', 'Kef', 'Mahdia', 'Manouba', 'Medenine', 'Monastir', 'Nabeul', 'Sfax', 'Sidi Bouzid', 'Siliana', 'Sousse', 'Tataouine', 'Tozeur', 'Tunis', 'Zaghouan'];\n  }\n  onSubmit() {\n    if (this.password !== this.confirmPassword) {\n      this.errorMessage = \"Passwords do not match. Please try again.\";\n      return; // Stop further execution\n    }\n\n    this.router.navigate(['/home']);\n  }\n  static {\n    this.ɵfac = function LoginComponent_Factory(t) {\n      return new (t || LoginComponent)(i0.ɵɵdirectiveInject(i1.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LoginComponent,\n      selectors: [[\"app-login\"]],\n      decls: 53,\n      vars: 33,\n      consts: [[\"align\", \"center\", 1, \"register\"], [\"src\", \"assets/women's wear.png\", \"alt\", \"Logo\"], [\"align\", \"center\", 1, \"container\"], [\"type\", \"text\", \"name\", \"username\", 3, \"ngModel\", \"ngModelChange\", \"input\"], [\"colspan\", \"2\", \"align\", \"center\"], [3, \"hidden\"], [\"name\", \"governorate\", 3, \"ngModel\", \"hidden\", \"disabled\", \"ngModelChange\", \"change\"], [4, \"ngFor\", \"ngForOf\"], [\"colspan\", \"2\"], [\"type\", \"radio\", \"name\", \"gender\", \"value\", \"Male\", 3, \"ngModel\", \"hidden\", \"disabled\", \"ngModelChange\", \"change\"], [3, \"hidden\", \"change\"], [\"type\", \"radio\", \"name\", \"gender\", \"value\", \"Female\", 3, \"ngModel\", \"hidden\", \"disabled\", \"ngModelChange\", \"change\"], [\"type\", \"text\", 3, \"ngModel\", \"hidden\", \"disabled\", \"ngModelChange\", \"input\"], [\"type\", \"password\", \"name\", \"password\", 3, \"ngModel\", \"hidden\", \"disabled\", \"ngModelChange\", \"input\"], [\"type\", \"password\", \"name\", \"confirmPassword\", 3, \"ngModel\", \"hidden\", \"disabled\", \"ngModelChange\", \"input\"], [\"type\", \"button\", 2, \"padding\", \"5px 45px\", 3, \"disabled\", \"click\"], [\"type\", \"button\", 2, \"padding\", \"5px 45px\"], [\"style\", \"color: red;\", 4, \"ngIf\"], [2, \"color\", \"red\"]],\n      template: function LoginComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵelement(1, \"img\", 1);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(2, \"div\", 2)(3, \"fieldset\")(4, \"legend\");\n          i0.ɵɵtext(5, \"Register\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"form\")(7, \"table\")(8, \"tr\")(9, \"td\")(10, \"p\");\n          i0.ɵɵtext(11, \"Username\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"input\", 3);\n          i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_12_listener($event) {\n            return ctx.username = $event;\n          })(\"input\", function LoginComponent_Template_input_input_12_listener() {\n            return ctx.checkStep(1);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(13, \"tr\")(14, \"td\", 4)(15, \"p\", 5);\n          i0.ɵɵtext(16, \"Governorate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"select\", 6);\n          i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_select_ngModelChange_17_listener($event) {\n            return ctx.governorate = $event;\n          })(\"change\", function LoginComponent_Template_select_change_17_listener() {\n            return ctx.checkStep(2);\n          });\n          i0.ɵɵtemplate(18, LoginComponent_option_18_Template, 2, 1, \"option\", 7);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(19, \"tr\")(20, \"td\", 8)(21, \"p\", 5);\n          i0.ɵɵtext(22, \"Gender\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"input\", 9);\n          i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_23_listener($event) {\n            return ctx.gender = $event;\n          })(\"change\", function LoginComponent_Template_input_change_23_listener() {\n            return ctx.checkStep(3);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"span\", 10);\n          i0.ɵɵlistener(\"change\", function LoginComponent_Template_span_change_24_listener() {\n            return ctx.checkStep(3);\n          });\n          i0.ɵɵtext(25, \"Male\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"input\", 11);\n          i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_26_listener($event) {\n            return ctx.gender = $event;\n          })(\"change\", function LoginComponent_Template_input_change_26_listener() {\n            return ctx.checkStep(3);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"span\", 10);\n          i0.ɵɵlistener(\"change\", function LoginComponent_Template_span_change_27_listener() {\n            return ctx.checkStep(3);\n          });\n          i0.ɵɵtext(28, \"Female\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(29, \"tr\")(30, \"td\")(31, \"p\", 5);\n          i0.ɵɵtext(32, \"Mail account\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"input\", 12);\n          i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_33_listener($event) {\n            return ctx.email = $event;\n          })(\"input\", function LoginComponent_Template_input_input_33_listener() {\n            return ctx.checkStep(4);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(34, \"td\")(35, \"p\", 5);\n          i0.ɵɵtext(36, \"Phone number\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(37, \"input\", 12);\n          i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_37_listener($event) {\n            return ctx.phone = $event;\n          })(\"input\", function LoginComponent_Template_input_input_37_listener() {\n            return ctx.checkStep(4);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(38, \"tr\")(39, \"td\")(40, \"p\", 5);\n          i0.ɵɵtext(41, \"Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"input\", 13);\n          i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_42_listener($event) {\n            return ctx.password = $event;\n          })(\"input\", function LoginComponent_Template_input_input_42_listener() {\n            return ctx.checkStep(6);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(43, \"td\")(44, \"p\", 5);\n          i0.ɵɵtext(45, \"Confirm Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"input\", 14);\n          i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_46_listener($event) {\n            return ctx.confirmPassword = $event;\n          })(\"input\", function LoginComponent_Template_input_input_46_listener() {\n            return ctx.checkStep(6);\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(47, \"div\")(48, \"button\", 15);\n          i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_48_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵtext(49, \"Submit\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(50, \"button\", 16);\n          i0.ɵɵtext(51, \"Reset\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(52, LoginComponent_div_52_Template, 2, 1, \"div\", 17);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"ngModel\", ctx.username);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"hidden\", !ctx.username);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngModel\", ctx.governorate)(\"hidden\", !ctx.username)(\"disabled\", !ctx.username);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.governorates);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"hidden\", !ctx.governorate);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngModel\", ctx.gender)(\"hidden\", !ctx.governorate)(\"disabled\", !ctx.governorate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"hidden\", !ctx.governorate);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngModel\", ctx.gender)(\"hidden\", !ctx.governorate)(\"disabled\", !ctx.governorate);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"hidden\", !ctx.governorate);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"hidden\", !ctx.gender);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngModel\", ctx.email)(\"hidden\", !ctx.gender)(\"disabled\", !ctx.gender);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"hidden\", !ctx.gender);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngModel\", ctx.phone)(\"hidden\", !ctx.gender)(\"disabled\", !ctx.gender);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"hidden\", !ctx.gender);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngModel\", ctx.password)(\"hidden\", !ctx.gender)(\"disabled\", !ctx.gender);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"hidden\", !ctx.password);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngModel\", ctx.confirmPassword)(\"hidden\", !ctx.password)(\"disabled\", !ctx.password);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", !ctx.confirmPassword);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.errorMessage);\n        }\n      },\n      dependencies: [i2.NgForOf, i2.NgIf, i3.ɵNgNoValidate, i3.NgSelectOption, i3.ɵNgSelectMultipleOption, i3.DefaultValueAccessor, i3.SelectControlValueAccessor, i3.RadioControlValueAccessor, i3.NgControlStatus, i3.NgControlStatusGroup, i3.NgModel, i3.NgForm],\n      styles: [\".auth-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  min-height: 85vh;\\n  padding: 50px 20px;\\n  background: linear-gradient(135deg, var(--light-pink), var(--medium-pink));\\n}\\n\\n.auth-card[_ngcontent-%COMP%] {\\n  width: 100%;\\n  max-width: 500px;\\n  background-color: white;\\n  border-radius: 20px;\\n  box-shadow: 0 15px 35px rgba(240, 98, 146, 0.2);\\n  overflow: hidden;\\n  position: relative;\\n  border: 2px solid var(--primary);\\n}\\n\\n.auth-card[_ngcontent-%COMP%]::before {\\n  content: '';\\n  position: absolute;\\n  top: -5px;\\n  left: -5px;\\n  right: -5px;\\n  bottom: -5px;\\n  background: linear-gradient(45deg, var(--primary), var(--secondary), var(--accent));\\n  z-index: -1;\\n  border-radius: 25px;\\n  opacity: 0.5;\\n}\\n\\n.auth-header[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 35px 20px 25px;\\n  background: linear-gradient(to bottom, var(--light-pink), white);\\n  border-bottom: 2px dashed var(--primary);\\n}\\n\\n.auth-header[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 140px;\\n  height: auto;\\n  margin-bottom: 25px;\\n  filter: drop-shadow(0 5px 10px rgba(240, 98, 146, 0.3));\\n  transition: all 0.3s ease;\\n}\\n\\n.auth-header[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n}\\n\\n.auth-header[_ngcontent-%COMP%]   legend[_ngcontent-%COMP%] {\\n  font-size: 32px;\\n  color: var(--dark-pink);\\n  font-weight: 700;\\n  font-family: var(--font-cursive);\\n  margin-bottom: 10px;\\n  position: relative;\\n  display: inline-block;\\n}\\n\\n.auth-header[_ngcontent-%COMP%]   legend[_ngcontent-%COMP%]::before, .auth-header[_ngcontent-%COMP%]   legend[_ngcontent-%COMP%]::after {\\n  content: '\\u2665';\\n  position: absolute;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  color: var(--primary);\\n  font-size: 20px;\\n}\\n\\n.auth-header[_ngcontent-%COMP%]   legend[_ngcontent-%COMP%]::before {\\n  left: -30px;\\n}\\n\\n.auth-header[_ngcontent-%COMP%]   legend[_ngcontent-%COMP%]::after {\\n  right: -30px;\\n}\\n\\n.auth-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: var(--secondary);\\n  font-size: 16px;\\n  font-style: italic;\\n  margin-top: 10px;\\n}\\n\\n.auth-form[_ngcontent-%COMP%] {\\n  padding: 35px;\\n}\\n\\n.form-group[_ngcontent-%COMP%] {\\n  margin-bottom: 25px;\\n}\\n\\n.form-group[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  margin-bottom: 10px;\\n  font-weight: 600;\\n  color: var(--dark-pink);\\n  font-size: 16px;\\n}\\n\\n.form-control[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 14px 18px;\\n  border: 2px solid #f8bbd0;\\n  border-radius: 15px;\\n  font-size: 16px;\\n  transition: all 0.3s ease;\\n  background-color: rgba(252, 228, 236, 0.3);\\n}\\n\\n.form-control[_ngcontent-%COMP%]:focus {\\n  border-color: var(--accent);\\n  outline: none;\\n  box-shadow: 0 0 0 4px rgba(255, 128, 171, 0.2);\\n  background-color: white;\\n}\\n\\nselect.form-control[_ngcontent-%COMP%] {\\n  appearance: none;\\n  background-image: url(\\\"data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23ad1457' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M6 9l6 6 6-6'/%3E%3C/svg%3E\\\");\\n  background-repeat: no-repeat;\\n  background-position: right 18px center;\\n  background-size: 16px;\\n}\\n\\ninput[type=\\\"radio\\\"][_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n  accent-color: var(--primary);\\n  transform: scale(1.2);\\n}\\n\\ninput[type=\\\"radio\\\"][_ngcontent-%COMP%]    + span[_ngcontent-%COMP%] {\\n  margin-right: 20px;\\n  color: var(--dark-pink);\\n  font-weight: 500;\\n}\\n\\n.btn-auth[_ngcontent-%COMP%] {\\n  width: 100%;\\n  padding: 14px;\\n  background-color: var(--primary);\\n  color: white;\\n  border: none;\\n  border-radius: 15px;\\n  font-size: 18px;\\n  font-weight: 600;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  position: relative;\\n  overflow: hidden;\\n  z-index: 1;\\n  margin-top: 10px;\\n  box-shadow: 0 5px 15px rgba(240, 98, 146, 0.3);\\n}\\n\\n.btn-auth[_ngcontent-%COMP%]::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 0;\\n  height: 100%;\\n  background-color: var(--accent);\\n  transition: all 0.4s ease;\\n  z-index: -1;\\n  border-radius: 15px;\\n}\\n\\n.btn-auth[_ngcontent-%COMP%]:hover::before {\\n  width: 100%;\\n}\\n\\n.btn-auth[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-3px);\\n  box-shadow: 0 8px 20px rgba(240, 98, 146, 0.4);\\n}\\n\\n.btn-auth[_ngcontent-%COMP%]:disabled {\\n  background-color: #ccc;\\n  cursor: not-allowed;\\n  box-shadow: none;\\n}\\n\\n.btn-auth[_ngcontent-%COMP%]:disabled:hover {\\n  transform: none;\\n}\\n\\n.auth-footer[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 20px;\\n  border-top: 2px dashed #f8bbd0;\\n  background: linear-gradient(to top, var(--light-pink), white);\\n}\\n\\n.auth-footer[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: var(--accent);\\n  font-weight: 600;\\n  transition: all 0.3s ease;\\n}\\n\\n.auth-footer[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  color: var(--dark-pink);\\n  text-decoration: underline;\\n}\\n\\n.error-message[_ngcontent-%COMP%] {\\n  color: #e91e63;\\n  font-size: 14px;\\n  margin-top: 5px;\\n  font-weight: 500;\\n}\\n.register[_ngcontent-%COMP%]   img\\n@media[_ngcontent-%COMP%]   (max-width[_ngcontent-%COMP%]:   576px)[_ngcontent-%COMP%] {\\n  .auth-card {\\n    max-width: 100%;\\n  }\\n  \\n  .auth-header img {\\n    width: 120px;\\n  }\\n  \\n  .auth-header legend {\\n    font-size: 28px;\\n  }\\n  \\n  .auth-form {\\n    padding: 25px;\\n  }\\n}\\n    \\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "gov_r2", "ɵɵtextInterpolate1", "ctx_r1", "errorMessage", "LoginComponent", "checkStep", "currentStep", "username", "step", "governorate", "gender", "email", "phone", "password", "confirmPassword", "constructor", "router", "governorates", "onSubmit", "navigate", "ɵɵdirectiveInject", "i1", "Router", "selectors", "decls", "vars", "consts", "template", "LoginComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵlistener", "LoginComponent_Template_input_ngModelChange_12_listener", "$event", "LoginComponent_Template_input_input_12_listener", "LoginComponent_Template_select_ngModelChange_17_listener", "LoginComponent_Template_select_change_17_listener", "ɵɵtemplate", "LoginComponent_option_18_Template", "LoginComponent_Template_input_ngModelChange_23_listener", "LoginComponent_Template_input_change_23_listener", "LoginComponent_Template_span_change_24_listener", "LoginComponent_Template_input_ngModelChange_26_listener", "LoginComponent_Template_input_change_26_listener", "LoginComponent_Template_span_change_27_listener", "LoginComponent_Template_input_ngModelChange_33_listener", "LoginComponent_Template_input_input_33_listener", "LoginComponent_Template_input_ngModelChange_37_listener", "LoginComponent_Template_input_input_37_listener", "LoginComponent_Template_input_ngModelChange_42_listener", "LoginComponent_Template_input_input_42_listener", "LoginComponent_Template_input_ngModelChange_46_listener", "LoginComponent_Template_input_input_46_listener", "LoginComponent_Template_button_click_48_listener", "LoginComponent_div_52_Template", "ɵɵproperty"], "sources": ["C:\\Users\\<USER>\\Desktop\\projet angular\\Projet\\src\\login\\login.component.ts", "C:\\Users\\<USER>\\Desktop\\projet angular\\Projet\\src\\login\\login.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { Router } from '@angular/router';\n\n@Component({\n  selector: 'app-login',\n  templateUrl: './login.component.html',\n  styleUrls: ['./login.component.css']\n})\nexport class LoginComponent {\n  step: number = 1; // Tracks the current visible field\n  username: string = '';\n  governorate: string = '';\n  gender: string = '';\n  email: string = '';\n  phone: string = '';\n  password: string = '';\n  confirmPassword: string = '';\n  errorMessage: string = \"\";\n\n  governorates: string[] = [\n    '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON> Arous', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>',\n    '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>',\n    '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Sfax', '<PERSON><PERSON> Bouzid', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>',\n    '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>',\n  ];\n  // Advances to the next step when user interacts with an input\n  checkStep(currentStep: number): void {\n    // Ensure step is advanced only when the input is filled\n    if (currentStep === 1 && this.username) {\n      this.step = 2;\n    } else if (currentStep === 2 && this.governorate) {\n      this.step = 3;}\n      else if (currentStep === 3 && this.gender) {\n      this.step = 4;\n    } else if (currentStep === 4 && this.email && this.phone) {\n      this.step = 5;\n    } else if (currentStep === 5) {\n      if (this.password === this.confirmPassword) {\n        this.step = 6;\n      }\n    }\n  }    \n\n  constructor(private router: Router) {}\n\n  onSubmit() {\n    if (this.password !== this.confirmPassword) {\n      this.errorMessage = \"Passwords do not match. Please try again.\";\n      return; // Stop further execution\n    }\n    this.router.navigate(['/home']);\n}\n}\n", "<div class=\"register\" align=\"center\">\n    <img src=\"assets/women's wear.png\" alt=\"Logo\" />\n</div>\n<div class=\"container\" align=\"center\">\n    <fieldset>\n        <legend>Register</legend>\n        <form>\n            <table>\n                <!-- Username Field -->\n                <tr>\n                    <td>\n                        <p>Username</p>\n                        <input type=\"text\" name=\"username\" [(ngModel)]=\"username\" (input)=\"checkStep(1)\" />\n                    </td>\n                </tr>\n            \n                <!-- Governorate Field -->\n                <tr>\n                    <td colspan=\"2\" align=\"center\">\n                        <p [hidden]=\"!username\">Governorate</p>\n                        <select name=\"governorate\" [(ngModel)]=\"governorate\" [hidden]=\"!username\" [disabled]=\"!username\" (change)=\"checkStep(2)\">\n                            <option *ngFor=\"let gov of governorates\">{{ gov }}</option>\n                        </select>\n                    </td>\n                </tr>\n            \n                <!-- Gender Fields -->\n                <tr>\n                    <td colspan=\"2\">\n                        <p [hidden]=\"!governorate\" >Gender</p>\n                        <input type=\"radio\" name=\"gender\" [(ngModel)]=\"gender\" value=\"Male\" [hidden]=\"!governorate\"  [disabled]=\"!governorate\" (change)=\"checkStep(3)\" /> <span  [hidden]=\"!governorate\"  (change)=\"checkStep(3)\" >Male</span>\n                        <input type=\"radio\" name=\"gender\" [(ngModel)]=\"gender\" value=\"Female\" [hidden]=\"!governorate\"  [disabled]=\"!governorate\" (change)=\"checkStep(3)\" /> <span  [hidden]=\"!governorate\"  (change)=\"checkStep(3)\" >Female</span>\n                    </td>\n                </tr>\n            \n                <!-- Email and Phone Fields -->\n                <tr>\n                    <td>\n                        <p [hidden]=\"!gender\">Mail account</p>\n                        <input type=\"text\" [(ngModel)]=\"email\" [hidden]=\"!gender\"[disabled]=\"!gender\"  (input)=\"checkStep(4)\" />\n                    </td>\n                    <td>\n                        <p [hidden]=\"!gender\">Phone number</p>\n                        <input type=\"text\" [(ngModel)]=\"phone\" [hidden]=\"!gender\" [disabled]=\"!gender\" (input)=\"checkStep(4)\" />\n                    </td>\n                </tr>\n            \n                <!-- Password Fields -->\n                <tr>\n                    <td>\n                        <p [hidden]=\"!gender\">Password</p>\n                        <input type=\"password\" name=\"password\" [(ngModel)]=\"password\" [hidden]=\"!gender\" [disabled]=\"!gender\" (input)=\"checkStep(6)\" />\n                    </td>\n                    <td>\n                        <p [hidden]=\"!password\">Confirm Password</p>\n                        <input type=\"password\" name=\"confirmPassword\" [(ngModel)]=\"confirmPassword\" [hidden]=\"!password\" [disabled]=\"!password\" (input)=\"checkStep(6)\" />\n                    </td>\n                </tr>\n            </table>\n            \n\n            <!-- Terms and Submit -->\n            <div>\n                <button type=\"button\" style=\"padding: 5px 45px;\" [disabled]=\"!confirmPassword\"(click)=\"onSubmit()\">Submit</button>\n                <button type=\"button\" style=\"padding: 5px 45px;\">Reset</button>\n            </div>\n            <div *ngIf=\"errorMessage\" style=\"color: red;\">\n                {{ errorMessage }}\n              </div>\n        </form>\n    </fieldset>\n</div>\n"], "mappings": ";;;;;;ICqB4BA,EAAA,CAAAC,cAAA,aAAyC;IAAAD,EAAA,CAAAE,MAAA,GAAS;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAAlBH,EAAA,CAAAI,SAAA,GAAS;IAATJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAS;;;;;IA6ClEN,EAAA,CAAAC,cAAA,cAA8C;IAC1CD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAO,kBAAA,MAAAC,MAAA,CAAAC,YAAA,MACF;;;AD5Dd,OAAM,MAAOC,cAAc;EAiBzB;EACAC,SAASA,CAACC,WAAmB;IAC3B;IACA,IAAIA,WAAW,KAAK,CAAC,IAAI,IAAI,CAACC,QAAQ,EAAE;MACtC,IAAI,CAACC,IAAI,GAAG,CAAC;KACd,MAAM,IAAIF,WAAW,KAAK,CAAC,IAAI,IAAI,CAACG,WAAW,EAAE;MAChD,IAAI,CAACD,IAAI,GAAG,CAAC;KAAE,MACV,IAAIF,WAAW,KAAK,CAAC,IAAI,IAAI,CAACI,MAAM,EAAE;MAC3C,IAAI,CAACF,IAAI,GAAG,CAAC;KACd,MAAM,IAAIF,WAAW,KAAK,CAAC,IAAI,IAAI,CAACK,KAAK,IAAI,IAAI,CAACC,KAAK,EAAE;MACxD,IAAI,CAACJ,IAAI,GAAG,CAAC;KACd,MAAM,IAAIF,WAAW,KAAK,CAAC,EAAE;MAC5B,IAAI,IAAI,CAACO,QAAQ,KAAK,IAAI,CAACC,eAAe,EAAE;QAC1C,IAAI,CAACN,IAAI,GAAG,CAAC;;;EAGnB;EAEAO,YAAoBC,MAAc;IAAd,KAAAA,MAAM,GAANA,MAAM;IAlC1B,KAAAR,IAAI,GAAW,CAAC,CAAC,CAAC;IAClB,KAAAD,QAAQ,GAAW,EAAE;IACrB,KAAAE,WAAW,GAAW,EAAE;IACxB,KAAAC,MAAM,GAAW,EAAE;IACnB,KAAAC,KAAK,GAAW,EAAE;IAClB,KAAAC,KAAK,GAAW,EAAE;IAClB,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAC,eAAe,GAAW,EAAE;IAC5B,KAAAX,YAAY,GAAW,EAAE;IAEzB,KAAAc,YAAY,GAAa,CACvB,QAAQ,EAAE,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,UAAU,EACtE,UAAU,EAAE,WAAW,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,EACzE,UAAU,EAAE,QAAQ,EAAE,MAAM,EAAE,aAAa,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,EAC7E,QAAQ,EAAE,OAAO,EAAE,UAAU,CAC9B;EAmBoC;EAErCC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACL,QAAQ,KAAK,IAAI,CAACC,eAAe,EAAE;MAC1C,IAAI,CAACX,YAAY,GAAG,2CAA2C;MAC/D,OAAO,CAAC;;;IAEV,IAAI,CAACa,MAAM,CAACG,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;EACnC;;;uBA3Caf,cAAc,EAAAV,EAAA,CAAA0B,iBAAA,CAAAC,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAdlB,cAAc;MAAAmB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCR3BnC,EAAA,CAAAC,cAAA,aAAqC;UACjCD,EAAA,CAAAqC,SAAA,aAAgD;UACpDrC,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,aAAsC;UAEtBD,EAAA,CAAAE,MAAA,eAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACzBH,EAAA,CAAAC,cAAA,WAAM;UAKaD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACfH,EAAA,CAAAC,cAAA,gBAAmF;UAAhDD,EAAA,CAAAsC,UAAA,2BAAAC,wDAAAC,MAAA;YAAA,OAAAJ,GAAA,CAAAvB,QAAA,GAAA2B,MAAA;UAAA,EAAsB,mBAAAC,gDAAA;YAAA,OAAUL,GAAA,CAAAzB,SAAA,CAAU,CAAC,CAAC;UAAA,EAAtB;UAAzDX,EAAA,CAAAG,YAAA,EAAmF;UAK3FH,EAAA,CAAAC,cAAA,UAAI;UAE4BD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACvCH,EAAA,CAAAC,cAAA,iBAAyH;UAA9FD,EAAA,CAAAsC,UAAA,2BAAAI,yDAAAF,MAAA;YAAA,OAAAJ,GAAA,CAAArB,WAAA,GAAAyB,MAAA;UAAA,EAAyB,oBAAAG,kDAAA;YAAA,OAAuDP,GAAA,CAAAzB,SAAA,CAAU,CAAC,CAAC;UAAA,EAAnE;UAChDX,EAAA,CAAA4C,UAAA,KAAAC,iCAAA,oBAA2D;UAC/D7C,EAAA,CAAAG,YAAA,EAAS;UAKjBH,EAAA,CAAAC,cAAA,UAAI;UAEgCD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACtCH,EAAA,CAAAC,cAAA,gBAAiJ;UAA/GD,EAAA,CAAAsC,UAAA,2BAAAQ,wDAAAN,MAAA;YAAA,OAAAJ,GAAA,CAAApB,MAAA,GAAAwB,MAAA;UAAA,EAAoB,oBAAAO,iDAAA;YAAA,OAA2EX,GAAA,CAAAzB,SAAA,CAAU,CAAC,CAAC;UAAA,EAAvF;UAAtDX,EAAA,CAAAG,YAAA,EAAiJ;UAACH,EAAA,CAAAC,cAAA,gBAAyD;UAAzBD,EAAA,CAAAsC,UAAA,oBAAAU,gDAAA;YAAA,OAAUZ,GAAA,CAAAzB,SAAA,CAAU,CAAC,CAAC;UAAA,EAAC;UAAEX,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAO;UACtNH,EAAA,CAAAC,cAAA,iBAAmJ;UAAjHD,EAAA,CAAAsC,UAAA,2BAAAW,wDAAAT,MAAA;YAAA,OAAAJ,GAAA,CAAApB,MAAA,GAAAwB,MAAA;UAAA,EAAoB,oBAAAU,iDAAA;YAAA,OAA6Ed,GAAA,CAAAzB,SAAA,CAAU,CAAC,CAAC;UAAA,EAAzF;UAAtDX,EAAA,CAAAG,YAAA,EAAmJ;UAACH,EAAA,CAAAC,cAAA,gBAAyD;UAAzBD,EAAA,CAAAsC,UAAA,oBAAAa,gDAAA;YAAA,OAAUf,GAAA,CAAAzB,SAAA,CAAU,CAAC,CAAC;UAAA,EAAC;UAAEX,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAKlOH,EAAA,CAAAC,cAAA,UAAI;UAE0BD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACtCH,EAAA,CAAAC,cAAA,iBAAwG;UAArFD,EAAA,CAAAsC,UAAA,2BAAAc,wDAAAZ,MAAA;YAAA,OAAAJ,GAAA,CAAAnB,KAAA,GAAAuB,MAAA;UAAA,EAAmB,mBAAAa,gDAAA;YAAA,OAAkDjB,GAAA,CAAAzB,SAAA,CAAU,CAAC,CAAC;UAAA,EAA9D;UAAtCX,EAAA,CAAAG,YAAA,EAAwG;UAE5GH,EAAA,CAAAC,cAAA,UAAI;UACsBD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACtCH,EAAA,CAAAC,cAAA,iBAAwG;UAArFD,EAAA,CAAAsC,UAAA,2BAAAgB,wDAAAd,MAAA;YAAA,OAAAJ,GAAA,CAAAlB,KAAA,GAAAsB,MAAA;UAAA,EAAmB,mBAAAe,gDAAA;YAAA,OAAkDnB,GAAA,CAAAzB,SAAA,CAAU,CAAC,CAAC;UAAA,EAA9D;UAAtCX,EAAA,CAAAG,YAAA,EAAwG;UAKhHH,EAAA,CAAAC,cAAA,UAAI;UAE0BD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAClCH,EAAA,CAAAC,cAAA,iBAA+H;UAAxFD,EAAA,CAAAsC,UAAA,2BAAAkB,wDAAAhB,MAAA;YAAA,OAAAJ,GAAA,CAAAjB,QAAA,GAAAqB,MAAA;UAAA,EAAsB,mBAAAiB,gDAAA;YAAA,OAAkDrB,GAAA,CAAAzB,SAAA,CAAU,CAAC,CAAC;UAAA,EAA9D;UAA7DX,EAAA,CAAAG,YAAA,EAA+H;UAEnIH,EAAA,CAAAC,cAAA,UAAI;UACwBD,EAAA,CAAAE,MAAA,wBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC5CH,EAAA,CAAAC,cAAA,iBAAiJ;UAAnGD,EAAA,CAAAsC,UAAA,2BAAAoB,wDAAAlB,MAAA;YAAA,OAAAJ,GAAA,CAAAhB,eAAA,GAAAoB,MAAA;UAAA,EAA6B,mBAAAmB,gDAAA;YAAA,OAAsDvB,GAAA,CAAAzB,SAAA,CAAU,CAAC,CAAC;UAAA,EAAlE;UAA3EX,EAAA,CAAAG,YAAA,EAAiJ;UAO7JH,EAAA,CAAAC,cAAA,WAAK;UAC6ED,EAAA,CAAAsC,UAAA,mBAAAsB,iDAAA;YAAA,OAASxB,GAAA,CAAAZ,QAAA,EAAU;UAAA,EAAC;UAACxB,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAClHH,EAAA,CAAAC,cAAA,kBAAiD;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAEnEH,EAAA,CAAA4C,UAAA,KAAAiB,8BAAA,kBAEQ;UACZ7D,EAAA,CAAAG,YAAA,EAAO;;;UAzD4CH,EAAA,CAAAI,SAAA,IAAsB;UAAtBJ,EAAA,CAAA8D,UAAA,YAAA1B,GAAA,CAAAvB,QAAA,CAAsB;UAOtDb,EAAA,CAAAI,SAAA,GAAoB;UAApBJ,EAAA,CAAA8D,UAAA,YAAA1B,GAAA,CAAAvB,QAAA,CAAoB;UACIb,EAAA,CAAAI,SAAA,GAAyB;UAAzBJ,EAAA,CAAA8D,UAAA,YAAA1B,GAAA,CAAArB,WAAA,CAAyB,YAAAqB,GAAA,CAAAvB,QAAA,eAAAuB,GAAA,CAAAvB,QAAA;UACxBb,EAAA,CAAAI,SAAA,GAAe;UAAfJ,EAAA,CAAA8D,UAAA,YAAA1B,GAAA,CAAAb,YAAA,CAAe;UAQxCvB,EAAA,CAAAI,SAAA,GAAuB;UAAvBJ,EAAA,CAAA8D,UAAA,YAAA1B,GAAA,CAAArB,WAAA,CAAuB;UACQf,EAAA,CAAAI,SAAA,GAAoB;UAApBJ,EAAA,CAAA8D,UAAA,YAAA1B,GAAA,CAAApB,MAAA,CAAoB,YAAAoB,GAAA,CAAArB,WAAA,eAAAqB,GAAA,CAAArB,WAAA;UAAmGf,EAAA,CAAAI,SAAA,GAAuB;UAAvBJ,EAAA,CAAA8D,UAAA,YAAA1B,GAAA,CAAArB,WAAA,CAAuB;UAC9If,EAAA,CAAAI,SAAA,GAAoB;UAApBJ,EAAA,CAAA8D,UAAA,YAAA1B,GAAA,CAAApB,MAAA,CAAoB,YAAAoB,GAAA,CAAArB,WAAA,eAAAqB,GAAA,CAAArB,WAAA;UAAqGf,EAAA,CAAAI,SAAA,GAAuB;UAAvBJ,EAAA,CAAA8D,UAAA,YAAA1B,GAAA,CAAArB,WAAA,CAAuB;UAO/Kf,EAAA,CAAAI,SAAA,GAAkB;UAAlBJ,EAAA,CAAA8D,UAAA,YAAA1B,GAAA,CAAApB,MAAA,CAAkB;UACFhB,EAAA,CAAAI,SAAA,GAAmB;UAAnBJ,EAAA,CAAA8D,UAAA,YAAA1B,GAAA,CAAAnB,KAAA,CAAmB,YAAAmB,GAAA,CAAApB,MAAA,eAAAoB,GAAA,CAAApB,MAAA;UAGnChB,EAAA,CAAAI,SAAA,GAAkB;UAAlBJ,EAAA,CAAA8D,UAAA,YAAA1B,GAAA,CAAApB,MAAA,CAAkB;UACFhB,EAAA,CAAAI,SAAA,GAAmB;UAAnBJ,EAAA,CAAA8D,UAAA,YAAA1B,GAAA,CAAAlB,KAAA,CAAmB,YAAAkB,GAAA,CAAApB,MAAA,eAAAoB,GAAA,CAAApB,MAAA;UAOnChB,EAAA,CAAAI,SAAA,GAAkB;UAAlBJ,EAAA,CAAA8D,UAAA,YAAA1B,GAAA,CAAApB,MAAA,CAAkB;UACkBhB,EAAA,CAAAI,SAAA,GAAsB;UAAtBJ,EAAA,CAAA8D,UAAA,YAAA1B,GAAA,CAAAjB,QAAA,CAAsB,YAAAiB,GAAA,CAAApB,MAAA,eAAAoB,GAAA,CAAApB,MAAA;UAG1DhB,EAAA,CAAAI,SAAA,GAAoB;UAApBJ,EAAA,CAAA8D,UAAA,YAAA1B,GAAA,CAAAjB,QAAA,CAAoB;UACuBnB,EAAA,CAAAI,SAAA,GAA6B;UAA7BJ,EAAA,CAAA8D,UAAA,YAAA1B,GAAA,CAAAhB,eAAA,CAA6B,YAAAgB,GAAA,CAAAjB,QAAA,eAAAiB,GAAA,CAAAjB,QAAA;UAQlCnB,EAAA,CAAAI,SAAA,GAA6B;UAA7BJ,EAAA,CAAA8D,UAAA,cAAA1B,GAAA,CAAAhB,eAAA,CAA6B;UAG5EpB,EAAA,CAAAI,SAAA,GAAkB;UAAlBJ,EAAA,CAAA8D,UAAA,SAAA1B,GAAA,CAAA3B,YAAA,CAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}