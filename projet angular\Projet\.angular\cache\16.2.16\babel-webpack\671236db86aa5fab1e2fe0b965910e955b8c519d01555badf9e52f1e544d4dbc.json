{"ast": null, "code": "import { BrowserModule } from '@angular/platform-browser';\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\nimport { FormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\nimport { HeaderComponent } from '../header/header.component';\nimport { HomeComponent } from '../home/<USER>';\nimport { ShopComponent } from '../shop/shop.component';\nimport { LoginComponent } from '../login/login.component';\nimport * as i0 from \"@angular/core\";\nexport class AppModule {\n  static {\n    this.ɵfac = function AppModule_Factory(t) {\n      return new (t || AppModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppModule,\n      bootstrap: [AppComponent]\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [BrowserModule, AppRoutingModule, FormsModule, RouterModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppModule, {\n    declarations: [AppComponent, HeaderComponent, HomeComponent, ShopComponent, LoginComponent],\n    imports: [BrowserModule, AppRoutingModule, FormsModule, RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["BrowserModule", "AppRoutingModule", "AppComponent", "FormsModule", "RouterModule", "HeaderComponent", "HomeComponent", "ShopComponent", "LoginComponent", "AppModule", "bootstrap", "declarations", "imports"], "sources": ["C:\\Users\\<USER>\\Desktop\\projet angular\\Projet\\src\\app\\app.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { BrowserModule } from '@angular/platform-browser';\n\nimport { AppRoutingModule } from './app-routing.module';\nimport { AppComponent } from './app.component';\nimport { FormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\nimport { HeaderComponent } from '../header/header.component';\nimport { HomeComponent } from '../home/<USER>';\nimport { ShopComponent } from '../shop/shop.component';\nimport { LoginComponent } from '../login/login.component';\n\n@NgModule({\n  declarations: [\n    AppComponent,\n    HeaderComponent,\n    HomeComponent,\n    ShopComponent,\n    LoginComponent\n  ],\n  imports: [\n    BrowserModule,\n    AppRoutingModule,\n    FormsModule,\n    RouterModule\n  ],\n  providers: [],\n  bootstrap: [AppComponent]\n})\nexport class AppModule { }\n"], "mappings": "AACA,SAASA,aAAa,QAAQ,2BAA2B;AAEzD,SAASC,gBAAgB,QAAQ,sBAAsB;AACvD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,eAAe,QAAQ,4BAA4B;AAC5D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,0BAA0B;;AAmBzD,OAAM,MAAOC,SAAS;;;uBAATA,SAAS;IAAA;EAAA;;;YAATA,SAAS;MAAAC,SAAA,GAFRR,YAAY;IAAA;EAAA;;;gBANtBF,aAAa,EACbC,gBAAgB,EAChBE,WAAW,EACXC,YAAY;IAAA;EAAA;;;2EAKHK,SAAS;IAAAE,YAAA,GAflBT,YAAY,EACZG,eAAe,EACfC,aAAa,EACbC,aAAa,EACbC,cAAc;IAAAI,OAAA,GAGdZ,aAAa,EACbC,gBAAgB,EAChBE,WAAW,EACXC,YAAY;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}