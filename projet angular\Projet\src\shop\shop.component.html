<div class="box mt-5">
    <div class="my-2 w-25">
      <label for="categories" class="my-1">Categories</label>
      <select
        id="categories"
        class="form-control"
        [(ngModel)]="selectedCategory"
      >
        <option value="">All Categories</option>
        <option value="1">for summer</option>
        <option value="2">shorts</option>
        <option value="3">jackets</option>
        <option value="4">shoes</option>
      </select>
    </div>
    <div class="my-2 w-50">
      <label for="search" class="my-1">Search Items</label>
      <input
        id="search"
        type="text"
        class="form-control"
        placeholder="Search by title..."
        [(ngModel)]="searchQuery"
      />
    </div>
    <div class="row" *ngFor="let item of getFilteredItems()">
      <div class="col-md-3 col-sm-12">
      <div class="item d-flex">
          <img [src]="item.imgSrc" alt={{item.imgSrc}}>
          <div class="body">
            <h4>{{ item.title }}</h4>
            <p>{{ item.description }}</p>
          </div>
          <div class="px-3 mb-2 d-flex justify-content-between align-items-center">
            <button class="btn btn-success">Add to Cart</button>
            <span>{{ item.price }} DT</span>
          </div>
        </div>
      </div>
    </div>
  
    <!-- Show a message if no items match the filter -->
    <div *ngIf="getFilteredItems().length === 0" class="text-center mt-4">
      <p>No items available for the selected category.</p>
    </div>
  </div>
  
