{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common/http\";\nimport * as i3 from \"@angular/common\";\nfunction HomeComponent_div_17_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵelement(1, \"img\", 28);\n    i0.ɵɵelementStart(2, \"div\", 29)(3, \"span\", 30);\n    i0.ɵɵtext(4, \"Sale\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 31)(6, \"div\", 32);\n    i0.ɵɵelement(7, \"i\", 33)(8, \"i\", 33)(9, \"i\", 33)(10, \"i\", 33)(11, \"i\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 35)(13, \"h4\");\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"titlecase\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"h4\");\n    i0.ɵɵtext(17);\n    i0.ɵɵpipe(18, \"slice\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"p\");\n    i0.ɵɵtext(20);\n    i0.ɵɵpipe(21, \"currency\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const item_r2 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate(\"alt\", item_r2.title);\n    i0.ɵɵproperty(\"src\", item_r2.imgSrc, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(13);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(15, 6, item_r2.title));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", i0.ɵɵpipeBind3(18, 8, item_r2.description, 0, 50), \"\", item_r2.description.length > 50 ? \"...\" : \"\", \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind3(21, 12, 50, \"TND\", \"symbol\"));\n  }\n}\nfunction HomeComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵtemplate(1, HomeComponent_div_17_div_1_Template, 22, 16, \"div\", 26);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.items);\n  }\n}\nexport class HomeComponent {\n  constructor(router, http) {\n    this.router = router;\n    this.http = http;\n    this.items = [];\n  }\n  ngOnInit() {\n    this.http.get('assets/product.json').subscribe({\n      next: response => {\n        this.items = response.products;\n        console.log('Loaded items:', this.items);\n      },\n      error: err => {\n        console.error('Error loading JSON:', err);\n        // Add proper error handling\n        this.items = []; // Provide fallback empty array\n      }\n    });\n  }\n\n  navigateToShop() {\n    this.router.navigate(['/shop']);\n  }\n  static {\n    this.ɵfac = function HomeComponent_Factory(t) {\n      return new (t || HomeComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.HttpClient));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HomeComponent,\n      selectors: [[\"app-home\"]],\n      decls: 77,\n      vars: 1,\n      consts: [[1, \"section\"], [1, \"btn-shop\", 3, \"click\"], [1, \"trending-product\"], [1, \"center-text\"], [\"class\", \"grid-Products\", 4, \"ngIf\"], [1, \"client-reviews\"], [1, \"reviews\"], [\"src\", \"assets/woman.jpg\", \"alt\", \"\"], [1, \"contact\"], [1, \"contact-info\"], [1, \"first-info\"], [\"src\", \"assets/women's wear.png\", \"alt\", \"\"], [1, \"social-icon\"], [\"href\", \"https://www.facebook.com/facebook/\"], [1, \"bx\", \"bxl-facebook-circle\"], [\"href\", \"https://x.com/\"], [1, \"bx\", \"bxl-twitter\"], [\"href\", \"https://www.instagram.com/instagram/\"], [1, \"bx\", \"bxl-instagram\"], [\"href\", \"https://www.youtube.com/@YouTube\"], [1, \"bx\", \"bxl-youtube\"], [\"href\", \"https://www.linkedin.com/feed/\"], [1, \"bx\", \"bxl-linkedin\"], [1, \"second-info\"], [1, \"third-info\"], [1, \"grid-Products\"], [\"class\", \"grid-row\", 4, \"ngFor\", \"ngForOf\"], [1, \"grid-row\"], [3, \"src\", \"alt\"], [1, \"product-text\"], [1, \"badge\"], [1, \"product-info\"], [1, \"ratting\"], [1, \"bx\", \"bxs-star\"], [1, \"bx\", \"bxs-star-half\"], [1, \"price\"]],\n      template: function HomeComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"h5\");\n          i0.ɵɵtext(2, \"2025 collection\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"h1\");\n          i0.ɵɵtext(4, \"New\");\n          i0.ɵɵelement(5, \"br\");\n          i0.ɵɵtext(6, \"collection 2025\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"h6\");\n          i0.ɵɵtext(8, \"There's nothing like trend\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"p\", 1);\n          i0.ɵɵlistener(\"click\", function HomeComponent_Template_p_click_9_listener() {\n            return ctx.navigateToShop();\n          });\n          i0.ɵɵtext(10, \"Shop now\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(11, \"div\", 2)(12, \"div\", 3)(13, \"h2\");\n          i0.ɵɵtext(14, \"Our tranding \");\n          i0.ɵɵelementStart(15, \"span\");\n          i0.ɵɵtext(16, \"Products\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(17, HomeComponent_div_17_Template, 2, 1, \"div\", 4);\n          i0.ɵɵelement(18, \"br\")(19, \"br\");\n          i0.ɵɵelementStart(20, \"section\", 5)(21, \"div\", 6)(22, \"h3\");\n          i0.ɵɵtext(23, \"Client Reviews\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(24, \"img\", 7);\n          i0.ɵɵelementStart(25, \"p\");\n          i0.ɵɵtext(26, \"Hello, my name is Sarah, and I\\u2019ve been shopping for stylish and comfortable women\\u2019s clothing online for years. I\\u2019m always looking for outfits that reflect my personality\\u2014elegant yet practical for my busy lifestyle. I appreciate a website that's easy to navigate with clear size guides, diverse collections, and quality fabrics. Fast delivery and excellent customer service are also a must for me. A good online shopping experience keeps me coming back for more!\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"h2\");\n          i0.ɵɵtext(28, \"Sarah\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"p\");\n          i0.ɵɵtext(30, \"CEO of Addle\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelement(31, \"br\")(32, \"br\");\n          i0.ɵɵelementStart(33, \"section\", 8)(34, \"div\", 9)(35, \"div\", 10);\n          i0.ɵɵelement(36, \"img\", 11);\n          i0.ɵɵelementStart(37, \"p\");\n          i0.ɵɵtext(38, \"123 Olive Street, Sidi Bouzid, 9100\");\n          i0.ɵɵelement(39, \"br\");\n          i0.ɵɵtext(40, \" Tunisia\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"p\");\n          i0.ɵɵtext(42, \"<EMAIL>\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"div\", 12)(44, \"a\", 13);\n          i0.ɵɵelement(45, \"i\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"a\", 15);\n          i0.ɵɵelement(47, \"i\", 16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(48, \"a\", 17);\n          i0.ɵɵelement(49, \"i\", 18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(50, \"a\", 19);\n          i0.ɵɵelement(51, \"i\", 20);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(52, \"a\", 21);\n          i0.ɵɵelement(53, \"i\", 22);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(54, \"div\", 23)(55, \"h4\");\n          i0.ɵɵtext(56, \"Support\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(57, \"p\");\n          i0.ɵɵtext(58, \"About us\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(59, \"p\");\n          i0.ɵɵtext(60, \"Contact us\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(61, \"p\");\n          i0.ɵɵtext(62, \"size guide\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(63, \"p\");\n          i0.ɵɵtext(64, \"Privacy\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(65, \"p\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(66, \"div\", 24)(67, \"h4\");\n          i0.ɵɵtext(68, \"Company\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(69, \"p\");\n          i0.ɵɵtext(70, \"About\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(71, \"p\");\n          i0.ɵɵtext(72, \"Blog\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(73, \"p\");\n          i0.ɵɵtext(74, \"Affiliate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(75, \"p\");\n          i0.ɵɵtext(76, \"Login\");\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(17);\n          i0.ɵɵproperty(\"ngIf\", ctx.items.length > 0);\n        }\n      },\n      dependencies: [i3.NgForOf, i3.NgIf, i3.SlicePipe, i3.TitleCasePipe, i3.CurrencyPipe],\n      styles: [\"*[_ngcontent-%COMP%]{\\n    margin: 0;\\n    padding: 0;\\n    box-sizing: border-box;\\n    scroll-behavior: smooth;\\n    font-family: 'sans-serif';\\n    list-style: none;\\n    text-decoration: none;\\n}\\n\\n\\n.section[_ngcontent-%COMP%] {\\n  padding: 5% 10%;\\n  width: 100%;\\n  min-height: 80vh;\\n  background-image: linear-gradient(rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.8)), url('banner-3.png');\\n  background-position: center;\\n  background-size: cover;\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: center;\\n  position: relative;\\n}\\n\\n.section[_ngcontent-%COMP%]::before {\\n  content: '';\\n  position: absolute;\\n  bottom: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 70px;\\n  background-image: url(\\\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1440 320'%3E%3Cpath fill='%23f8f5ff' fill-opacity='1' d='M0,96L48,112C96,128,192,160,288,186.7C384,213,480,235,576,224C672,213,768,171,864,149.3C960,128,1056,128,1152,149.3C1248,171,1344,213,1392,234.7L1440,256L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z'%3E%3C/path%3E%3C/svg%3E\\\");\\n  background-size: cover;\\n  background-repeat: no-repeat;\\n}\\n\\n.section[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  color: var(--secondary);\\n  font-size: 18px;\\n  text-transform: uppercase;\\n  letter-spacing: 3px;\\n  font-weight: 500;\\n  position: relative;\\n  display: inline-block;\\n  margin-bottom: 10px;\\n}\\n\\n.section[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]::after {\\n  content: '\\u273F';\\n  position: absolute;\\n  right: -25px;\\n  color: var(--primary);\\n}\\n\\n.section[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  background: linear-gradient(to right, var(--primary), var(--secondary));\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  font-size: 4rem;\\n  line-height: 1.1;\\n  font-weight: 700;\\n  margin: 15px 0;\\n  font-family: var(--font-cursive);\\n  text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.8);\\n}\\n\\n.section[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  color: var(--dark);\\n  font-size: 22px;\\n  font-style: italic;\\n  margin-bottom: 30px;\\n}\\n\\n.section[_ngcontent-%COMP%]   .btn-shop[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  padding: 14px 35px;\\n  background: linear-gradient(to right, var(--primary), var(--secondary));\\n  color: white;\\n  font-weight: 600;\\n  text-transform: uppercase;\\n  letter-spacing: 1px;\\n  cursor: pointer;\\n  transition: var(--transition);\\n  border-radius: 30px;\\n  box-shadow: 0 5px 15px rgba(147, 112, 219, 0.3);\\n  position: relative;\\n  overflow: hidden;\\n  z-index: 1;\\n}\\n\\n.section[_ngcontent-%COMP%]   .btn-shop[_ngcontent-%COMP%]::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 0%;\\n  height: 100%;\\n  background: linear-gradient(to right, var(--secondary), var(--primary));\\n  transition: all 0.5s ease;\\n  z-index: -1;\\n  border-radius: 30px;\\n}\\n\\n.section[_ngcontent-%COMP%]   .btn-shop[_ngcontent-%COMP%]:hover::before {\\n  width: 100%;\\n}\\n\\n.section[_ngcontent-%COMP%]   .btn-shop[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n  box-shadow: 0 8px 20px rgba(147, 112, 219, 0.4);\\n}\\n\\n\\n.center-text[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin: 70px 0 50px;\\n}\\n\\n.center-text[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 36px;\\n  position: relative;\\n  display: inline-block;\\n  color: var(--primary);\\n  font-family: var(--font-cursive);\\n}\\n\\n.center-text[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]::before, .center-text[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]::after {\\n  content: '\\u2740';\\n  position: absolute;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  color: var(--accent);\\n  font-size: 24px;\\n}\\n\\n.center-text[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]::before {\\n  left: -40px;\\n}\\n\\n.center-text[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]::after {\\n  right: -40px;\\n}\\n\\n.center-text[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  color: var(--secondary);\\n}\\n\\n.grid-Products[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\\n  gap: 30px;\\n  padding: 0 20px;\\n}\\n\\n.grid-row[_ngcontent-%COMP%] {\\n  background-color: white;\\n  border-radius: 15px;\\n  overflow: hidden;\\n  box-shadow: var(--shadow);\\n  transition: var(--transition);\\n  position: relative;\\n}\\n\\n.grid-row[_ngcontent-%COMP%]::before {\\n  content: '';\\n  position: absolute;\\n  top: -5px;\\n  left: -5px;\\n  right: -5px;\\n  bottom: -5px;\\n  background: linear-gradient(45deg, var(--primary), var(--secondary), var(--accent));\\n  z-index: -1;\\n  border-radius: 20px;\\n  opacity: 0;\\n  transition: var(--transition);\\n}\\n\\n.grid-row[_ngcontent-%COMP%]:hover::before {\\n  opacity: 1;\\n}\\n\\n.grid-row[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-10px) scale(1.02);\\n  box-shadow: 0 15px 30px rgba(147, 112, 219, 0.2);\\n}\\n\\n.grid-row[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 250px;\\n  object-fit: cover;\\n  transition: var(--transition);\\n}\\n\\n.grid-row[_ngcontent-%COMP%]:hover   img[_ngcontent-%COMP%] {\\n  transform: scale(1.05);\\n}\\n\\n.product-text[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 15px;\\n  right: 15px;\\n}\\n\\n.badge[_ngcontent-%COMP%] {\\n  background: linear-gradient(to right, var(--primary), var(--secondary));\\n  color: white;\\n  padding: 5px 12px;\\n  border-radius: 20px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  box-shadow: 0 3px 8px rgba(147, 112, 219, 0.3);\\n}\\n\\n.product-info[_ngcontent-%COMP%] {\\n  padding: 15px;\\n}\\n\\n.ratting[_ngcontent-%COMP%] {\\n  margin-bottom: 10px;\\n}\\n\\n.ratting[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: var(--accent);\\n  font-size: 16px;\\n  margin-right: 2px;\\n}\\n\\n.price[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  color: var(--dark);\\n  margin-bottom: 5px;\\n  text-transform: capitalize;\\n}\\n\\n.price[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: var(--primary);\\n  font-weight: 600;\\n  font-size: 18px;\\n}\\n\\n\\n.client-reviews[_ngcontent-%COMP%] {\\n  background-color: var(--light);\\n  padding: 80px 20px;\\n  margin-top: 80px;\\n  position: relative;\\n}\\n\\n.client-reviews[_ngcontent-%COMP%]::before, .client-reviews[_ngcontent-%COMP%]::after {\\n  content: '';\\n  position: absolute;\\n  width: 100%;\\n  height: 30px;\\n  background-image: url(\\\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1440 320'%3E%3Cpath fill='%23ffffff' fill-opacity='1' d='M0,96L48,112C96,128,192,160,288,186.7C384,213,480,235,576,224C672,213,768,171,864,149.3C960,128,1056,128,1152,149.3C1248,171,1344,213,1392,234.7L1440,256L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z'%3E%3C/path%3E%3C/svg%3E\\\");\\n  background-size: cover;\\n  background-repeat: no-repeat;\\n}\\n\\n.client-reviews[_ngcontent-%COMP%]::before {\\n  top: -30px;\\n  transform: rotate(180deg);\\n}\\n\\n.client-reviews[_ngcontent-%COMP%]::after {\\n  bottom: -30px;\\n}\\n\\n.reviews[_ngcontent-%COMP%] {\\n  max-width: 800px;\\n  margin: 0 auto;\\n  text-align: center;\\n  background-color: white;\\n  padding: 40px;\\n  border-radius: 20px;\\n  box-shadow: var(--shadow);\\n  position: relative;\\n}\\n\\n.reviews[_ngcontent-%COMP%]::before, .reviews[_ngcontent-%COMP%]::after {\\n  content: '\\\"';\\n  position: absolute;\\n  font-size: 100px;\\n  color: var(--secondary);\\n  opacity: 0.2;\\n  font-family: serif;\\n  line-height: 1;\\n}\\n\\n.reviews[_ngcontent-%COMP%]::before {\\n  top: 20px;\\n  left: 20px;\\n}\\n\\n.reviews[_ngcontent-%COMP%]::after {\\n  bottom: 0;\\n  right: 20px;\\n  transform: rotate(180deg);\\n}\\n\\n.reviews[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 32px;\\n  margin-bottom: 40px;\\n  position: relative;\\n  display: inline-block;\\n  color: var(--primary);\\n  font-family: var(--font-cursive);\\n}\\n\\n.reviews[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]::after {\\n  content: '';\\n  position: absolute;\\n  bottom: -10px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  width: 70px;\\n  height: 3px;\\n  background: linear-gradient(to right, var(--secondary), var(--primary), var(--secondary));\\n}\\n\\n.reviews[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100px;\\n  height: 100px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n  border: 5px solid white;\\n  box-shadow: 0 0 0 3px var(--secondary);\\n  margin: 20px auto;\\n}\\n\\n.reviews[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 16px;\\n  line-height: 1.8;\\n  margin-bottom: 20px;\\n  font-style: italic;\\n}\\n\\n.reviews[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 22px;\\n  color: var(--primary);\\n  margin-bottom: 5px;\\n}\\n\\n\\n.contact[_ngcontent-%COMP%] {\\n  padding: 80px 20px;\\n  background-color: white;\\n}\\n\\n.contact-info[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n  gap: 40px;\\n}\\n\\n.contact-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: var(--primary);\\n  font-size: 20px;\\n  text-transform: uppercase;\\n  margin-bottom: 20px;\\n  position: relative;\\n  display: inline-block;\\n}\\n\\n.contact-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]::after {\\n  content: '';\\n  position: absolute;\\n  bottom: -8px;\\n  left: 0;\\n  width: 40px;\\n  height: 2px;\\n  background: linear-gradient(to right, var(--primary), var(--secondary));\\n}\\n\\n.first-info[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 140px;\\n  margin-bottom: 20px;\\n}\\n\\n.contact-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  margin-bottom: 10px;\\n  transition: var(--transition);\\n  position: relative;\\n  padding-left: 25px;\\n}\\n\\n.contact-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]::before {\\n  content: '\\u2740';\\n  position: absolute;\\n  left: 0;\\n  color: var(--secondary);\\n  font-size: 14px;\\n}\\n\\n.contact-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:hover {\\n  color: var(--primary);\\n  transform: translateX(5px);\\n}\\n\\n.social-icon[_ngcontent-%COMP%] {\\n  display: flex;\\n  margin-top: 20px;\\n}\\n\\n.social-icon[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center\\n}\\n\\n\\n.contact[_ngcontent-%COMP%] {\\n  padding: 80px 20px;\\n  background-color: white;\\n}\\n\\n.contact-info[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n  gap: 40px;\\n}\\n\\n.contact-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: var(--primary);\\n  font-size: 18px;\\n  text-transform: uppercase;\\n  margin-bottom: 20px;\\n  position: relative;\\n}\\n\\n.contact-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]::after {\\n  content: '';\\n  position: absolute;\\n  bottom: -8px;\\n  left: 0;\\n  width: 40px;\\n  height: 2px;\\n  background-color: var(--accent);\\n}\\n\\n.first-info[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 140px;\\n  margin-bottom: 20px;\\n}\\n\\n.contact-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  margin-bottom: 10px;\\n  transition: var(--transition);\\n}\\n\\n.contact-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:hover {\\n  color: var(--accent);\\n  transform: translateX(5px);\\n}\\n\\n.social-icon[_ngcontent-%COMP%] {\\n  display: flex;\\n  margin-top: 20px;\\n}\\n\\n.social-icon[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 40px;\\n  height: 40px;\\n  background-color: var(--light);\\n  border-radius: 50%;\\n  margin-right: 10px;\\n  transition: var(--transition);\\n}\\n\\n.social-icon[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  background-color: var(--accent);\\n  color: white;\\n  transform: translateY(-5px);\\n}\\n\\n.social-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n}\\n\\n@media (max-width: 768px) {\\n  .section[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n    font-size: 2.5rem;\\n  }\\n  \\n  .grid-Products[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\\n  }\\n}\\n.end-text[_ngcontent-%COMP%]{\\n    background-color: #edfff1;\\n    text-align: center;\\n    padding: 20px;\\n}\\n.end-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{\\n    color: #111;\\n    text-transform: capitalize;\\n\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵpropertyInterpolate", "item_r2", "title", "ɵɵproperty", "imgSrc", "ɵɵsanitizeUrl", "ɵɵtextInterpolate", "ɵɵpipeBind1", "ɵɵtextInterpolate2", "ɵɵpipeBind3", "description", "length", "ɵɵtemplate", "HomeComponent_div_17_div_1_Template", "ctx_r0", "items", "HomeComponent", "constructor", "router", "http", "ngOnInit", "get", "subscribe", "next", "response", "products", "console", "log", "error", "err", "navigateToShop", "navigate", "ɵɵdirectiveInject", "i1", "Router", "i2", "HttpClient", "selectors", "decls", "vars", "consts", "template", "HomeComponent_Template", "rf", "ctx", "ɵɵlistener", "HomeComponent_Template_p_click_9_listener", "HomeComponent_div_17_Template"], "sources": ["C:\\Users\\<USER>\\Desktop\\projet angular\\Projet\\src\\home\\home.component.ts", "C:\\Users\\<USER>\\Desktop\\projet angular\\Projet\\src\\home\\home.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { HttpClient } from '@angular/common/http';\n\n// Define the Produit interface\ninterface Produit {\n  id: number;\n  title: string;\n  description: string;\n  imgSrc: string;\n}\n\n@Component({\n  selector: 'app-home',\n  templateUrl: './home.component.html',\n  styleUrls: ['./home.component.css']\n})\nexport class HomeComponent implements OnInit {\n  items: Produit[] = [];\n\n  constructor(private router: Router, private http: HttpClient) {}\n\n  ngOnInit() {\n    this.http.get<{products: Produit[]}>('assets/product.json').subscribe({\n      next: (response: {products: Produit[]}) => {\n        this.items = response.products;\n        console.log('Loaded items:', this.items);\n      },\n      error: (err: any) => {\n        console.error('Error loading JSON:', err);\n        // Add proper error handling\n        this.items = []; // Provide fallback empty array\n      }\n    });\n  }\n\n  navigateToShop() {\n    this.router.navigate(['/shop']);\n  }\n}\n\n\n\n", "<div class=\"section\">\n    <h5>2025 collection</h5>\n    <h1>New<br>collection 2025</h1>\n    <h6>There's nothing like trend</h6>\n    <p class=\"btn-shop\" (click)=\"navigateToShop()\">Shop now</p>\n</div>\n\n<div class=\"trending-product\">\n    <div class=\"center-text\">\n        <h2>Our tranding <span>Products</span></h2>\n    </div>\n    \n    <div class=\"grid-Products\" *ngIf=\"items.length > 0\">\n        <div class=\"grid-row\" *ngFor=\"let item of items\">\n            <img [src]=\"item.imgSrc\" alt=\"{{ item.title }}\">\n\n            <div class=\"product-text\">\n                <span class=\"badge\">Sale</span>\n            </div>\n\n            <div class=\"product-info\">\n                <div class=\"ratting\">\n                    <i class='bx bxs-star'></i>\n                    <i class='bx bxs-star'></i>\n                    <i class='bx bxs-star'></i>\n                    <i class='bx bxs-star'></i>\n                    <i class='bx bxs-star-half'></i>\n                </div>\n\n                <div class=\"price\">\n                    <h4>{{ item.title | titlecase }}</h4>\n                    <h4>{{ item.description | slice:0:50 }}{{ item.description.length > 50 ? '...' : '' }}</h4>\n                    <p>{{ 50 | currency:'TND':'symbol' }}</p>\n\n                </div>\n            </div>\n        </div>\n    </div>\n\n    <br><br>\n    <section class=\"client-reviews\">\n        <div class=\"reviews\">\n            <h3>Client Reviews</h3>\n            <img src=\"assets/woman.jpg\"  alt=\"\">\n            <p>Hello, my name is Sarah, and I’ve been shopping for stylish and comfortable women’s clothing online for years. I’m always looking for outfits that reflect my personality—elegant yet practical for my busy lifestyle. I appreciate a website that's easy to navigate with clear size guides, diverse collections, and quality fabrics. Fast delivery and excellent customer service are also a must for me. A good online shopping experience keeps me coming back for more!</p>\n            <h2>Sarah</h2>\n            <p>CEO of Addle</p>\n        </div>\n    </section>\n</div>\n<br><br>\n<section class=\"contact\">\n    <div class=\"contact-info\">\n        <div class=\"first-info\">\n            <img src=\"assets/women's wear.png\" alt=\"\">\n            <p>123 Olive Street, Sidi Bouzid, 9100<br> Tunisia</p>\n            <p>Herwardrobe&#64;gmail.com</p>\n            <div class=\"social-icon\">\n                <a href=\"https://www.facebook.com/facebook/\"><i class='bx bxl-facebook-circle'></i></a>\n                <a href=\"https://x.com/\"><i class='bx bxl-twitter'></i></a>\n                <a href=\"https://www.instagram.com/instagram/\"><i class='bx bxl-instagram'></i></a>\n                <a href=\"https://www.youtube.com/@YouTube\"><i class='bx bxl-youtube'></i></a>\n                <a href=\"https://www.linkedin.com/feed/\"><i class='bx bxl-linkedin'></i></a>\n            </div>\n        </div>\n        <div class=\"second-info\">\n            <h4>Support</h4>\n            <p>About us</p>\n            <p>Contact us</p>\n            <p>size guide</p>\n            <p>Privacy</p>\n            <p></p>\n        </div>\n        <div class=\"third-info\">\n            <h4>Company</h4>\n            <p>About</p>\n            <p>Blog</p>\n            <p>Affiliate</p>\n            <p>Login</p>\n        </div>\n    </div>\n</section>\n\n\n"], "mappings": ";;;;;;ICaQA,EAAA,CAAAC,cAAA,cAAiD;IAC7CD,EAAA,CAAAE,SAAA,cAAgD;IAEhDF,EAAA,CAAAC,cAAA,cAA0B;IACFD,EAAA,CAAAG,MAAA,WAAI;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAGnCJ,EAAA,CAAAC,cAAA,cAA0B;IAElBD,EAAA,CAAAE,SAAA,YAA2B;IAK/BF,EAAA,CAAAI,YAAA,EAAM;IAENJ,EAAA,CAAAC,cAAA,eAAmB;IACXD,EAAA,CAAAG,MAAA,IAA4B;;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACrCJ,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAG,MAAA,IAAkF;;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC3FJ,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAG,MAAA,IAAkC;;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;IAlBxBJ,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAAM,qBAAA,QAAAC,OAAA,CAAAC,KAAA,CAAsB;IAA1CR,EAAA,CAAAS,UAAA,QAAAF,OAAA,CAAAG,MAAA,EAAAV,EAAA,CAAAW,aAAA,CAAmB;IAgBZX,EAAA,CAAAK,SAAA,IAA4B;IAA5BL,EAAA,CAAAY,iBAAA,CAAAZ,EAAA,CAAAa,WAAA,QAAAN,OAAA,CAAAC,KAAA,EAA4B;IAC5BR,EAAA,CAAAK,SAAA,GAAkF;IAAlFL,EAAA,CAAAc,kBAAA,KAAAd,EAAA,CAAAe,WAAA,QAAAR,OAAA,CAAAS,WAAA,cAAAT,OAAA,CAAAS,WAAA,CAAAC,MAAA,uBAAkF;IACnFjB,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAAY,iBAAA,CAAAZ,EAAA,CAAAe,WAAA,8BAAkC;;;;;IApBrDf,EAAA,CAAAC,cAAA,cAAoD;IAChDD,EAAA,CAAAkB,UAAA,IAAAC,mCAAA,oBAuBM;IACVnB,EAAA,CAAAI,YAAA,EAAM;;;;IAxBqCJ,EAAA,CAAAK,SAAA,GAAQ;IAARL,EAAA,CAAAS,UAAA,YAAAW,MAAA,CAAAC,KAAA,CAAQ;;;ADIvD,OAAM,MAAOC,aAAa;EAGxBC,YAAoBC,MAAc,EAAUC,IAAgB;IAAxC,KAAAD,MAAM,GAANA,MAAM;IAAkB,KAAAC,IAAI,GAAJA,IAAI;IAFhD,KAAAJ,KAAK,GAAc,EAAE;EAE0C;EAE/DK,QAAQA,CAAA;IACN,IAAI,CAACD,IAAI,CAACE,GAAG,CAAwB,qBAAqB,CAAC,CAACC,SAAS,CAAC;MACpEC,IAAI,EAAGC,QAA+B,IAAI;QACxC,IAAI,CAACT,KAAK,GAAGS,QAAQ,CAACC,QAAQ;QAC9BC,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,IAAI,CAACZ,KAAK,CAAC;MAC1C,CAAC;MACDa,KAAK,EAAGC,GAAQ,IAAI;QAClBH,OAAO,CAACE,KAAK,CAAC,qBAAqB,EAAEC,GAAG,CAAC;QACzC;QACA,IAAI,CAACd,KAAK,GAAG,EAAE,CAAC,CAAC;MACnB;KACD,CAAC;EACJ;;EAEAe,cAAcA,CAAA;IACZ,IAAI,CAACZ,MAAM,CAACa,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;EACjC;;;uBArBWf,aAAa,EAAAtB,EAAA,CAAAsC,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAxC,EAAA,CAAAsC,iBAAA,CAAAG,EAAA,CAAAC,UAAA;IAAA;EAAA;;;YAAbpB,aAAa;MAAAqB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCjB1BjD,EAAA,CAAAC,cAAA,aAAqB;UACbD,EAAA,CAAAG,MAAA,sBAAe;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACxBJ,EAAA,CAAAC,cAAA,SAAI;UAAAD,EAAA,CAAAG,MAAA,UAAG;UAAAH,EAAA,CAAAE,SAAA,SAAI;UAAAF,EAAA,CAAAG,MAAA,sBAAe;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAC/BJ,EAAA,CAAAC,cAAA,SAAI;UAAAD,EAAA,CAAAG,MAAA,iCAA0B;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACnCJ,EAAA,CAAAC,cAAA,WAA+C;UAA3BD,EAAA,CAAAmD,UAAA,mBAAAC,0CAAA;YAAA,OAASF,GAAA,CAAAd,cAAA,EAAgB;UAAA,EAAC;UAACpC,EAAA,CAAAG,MAAA,gBAAQ;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAG/DJ,EAAA,CAAAC,cAAA,cAA8B;UAElBD,EAAA,CAAAG,MAAA,qBAAa;UAAAH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAG,MAAA,gBAAQ;UAAAH,EAAA,CAAAI,YAAA,EAAO;UAG1CJ,EAAA,CAAAkB,UAAA,KAAAmC,6BAAA,iBAyBM;UAENrD,EAAA,CAAAE,SAAA,UAAI;UACJF,EAAA,CAAAC,cAAA,kBAAgC;UAEpBD,EAAA,CAAAG,MAAA,sBAAc;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACvBJ,EAAA,CAAAE,SAAA,cAAoC;UACpCF,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,yeAA6c;UAAAH,EAAA,CAAAI,YAAA,EAAI;UACpdJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,aAAK;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACdJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,oBAAY;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAI/BJ,EAAA,CAAAE,SAAA,UAAI;UACJF,EAAA,CAAAC,cAAA,kBAAyB;UAGbD,EAAA,CAAAE,SAAA,eAA0C;UAC1CF,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,2CAAmC;UAAAH,EAAA,CAAAE,SAAA,UAAI;UAACF,EAAA,CAAAG,MAAA,gBAAO;UAAAH,EAAA,CAAAI,YAAA,EAAI;UACtDJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,6BAAyB;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAChCJ,EAAA,CAAAC,cAAA,eAAyB;UACwBD,EAAA,CAAAE,SAAA,aAAsC;UAAAF,EAAA,CAAAI,YAAA,EAAI;UACvFJ,EAAA,CAAAC,cAAA,aAAyB;UAAAD,EAAA,CAAAE,SAAA,aAA8B;UAAAF,EAAA,CAAAI,YAAA,EAAI;UAC3DJ,EAAA,CAAAC,cAAA,aAA+C;UAAAD,EAAA,CAAAE,SAAA,aAAgC;UAAAF,EAAA,CAAAI,YAAA,EAAI;UACnFJ,EAAA,CAAAC,cAAA,aAA2C;UAAAD,EAAA,CAAAE,SAAA,aAA8B;UAAAF,EAAA,CAAAI,YAAA,EAAI;UAC7EJ,EAAA,CAAAC,cAAA,aAAyC;UAAAD,EAAA,CAAAE,SAAA,aAA+B;UAAAF,EAAA,CAAAI,YAAA,EAAI;UAGpFJ,EAAA,CAAAC,cAAA,eAAyB;UACjBD,EAAA,CAAAG,MAAA,eAAO;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAChBJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,gBAAQ;UAAAH,EAAA,CAAAI,YAAA,EAAI;UACfJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,kBAAU;UAAAH,EAAA,CAAAI,YAAA,EAAI;UACjBJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,kBAAU;UAAAH,EAAA,CAAAI,YAAA,EAAI;UACjBJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,eAAO;UAAAH,EAAA,CAAAI,YAAA,EAAI;UACdJ,EAAA,CAAAE,SAAA,SAAO;UACXF,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAC,cAAA,eAAwB;UAChBD,EAAA,CAAAG,MAAA,eAAO;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAChBJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,aAAK;UAAAH,EAAA,CAAAI,YAAA,EAAI;UACZJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,YAAI;UAAAH,EAAA,CAAAI,YAAA,EAAI;UACXJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,iBAAS;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAChBJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,aAAK;UAAAH,EAAA,CAAAI,YAAA,EAAI;;;UAlEQJ,EAAA,CAAAK,SAAA,IAAsB;UAAtBL,EAAA,CAAAS,UAAA,SAAAyC,GAAA,CAAA7B,KAAA,CAAAJ,MAAA,KAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}