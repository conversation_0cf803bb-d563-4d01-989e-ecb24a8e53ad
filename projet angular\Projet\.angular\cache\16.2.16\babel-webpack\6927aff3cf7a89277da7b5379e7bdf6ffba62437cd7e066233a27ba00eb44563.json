{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class AppComponent {\n  constructor() {\n    this.title = 'Projet';\n  }\n  static {\n    this.ɵfac = function AppComponent_Factory(t) {\n      return new (t || AppComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppComponent,\n      selectors: [[\"app-root\"]],\n      decls: 18,\n      vars: 0,\n      consts: [[1, \"container\"], [1, \"content\"], [\"src\", \"assets/women's wear.png\", \"alt\", \"\", \"routerLink\", \"/home\", 1, \"logo\"], [1, \"navmenu\"], [\"routerLink\", \"/home\"], [\"routerLink\", \"/shop\"], [\"routerLink\", \"/login\"], [1, \"end-text\"]],\n      template: function AppComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"nav\")(1, \"div\", 0)(2, \"div\", 1);\n          i0.ɵɵelement(3, \"img\", 2);\n          i0.ɵɵelementStart(4, \"ul\", 3)(5, \"li\", 4);\n          i0.ɵɵtext(6, \"Home\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"li\", 5);\n          i0.ɵɵtext(8, \"Shop\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"ul\")(10, \"li\", 6);\n          i0.ɵɵtext(11, \"Log in\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(12, \"div\", 0);\n          i0.ɵɵelement(13, \"router-outlet\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(14, \"br\");\n          i0.ɵɵelementStart(15, \"div\", 7)(16, \"p\");\n          i0.ɵɵtext(17, \"Copyright \\u00A9 2024 .All Rights Reserved.Designed By ... \");\n          i0.ɵɵelementEnd()();\n        }\n      },\n      dependencies: [i1.RouterOutlet, i1.RouterLink],\n      styles: [\"nav[_ngcontent-%COMP%]{\\n    background-color: ;\\n    top: 0;\\n    z-index: 1000;\\n    width: 100%;\\n    img{\\n        width: 100px;\\n    }\\n    .content{\\n        display: flex;\\n        justify-content: space-between;\\n        align-items: center;\\n        padding:10px 0;\\n        ul{\\n            list-style: none;\\n            display: flex;\\n            justify-content: space-between;\\n            width: auto;\\n            margin: 0;\\n            padding: 0;\\n            li{\\n                cursor: pointer;\\n                color:#000;\\n                text-decoration: none;\\n                margin: 0 10px;\\n                \\n            }\\n            li:hover{\\n                color: #a4936d;\\n                transform: scale(1.1);\\n            }\\n        }\\n    }\\n}\\n.end-text[_ngcontent-%COMP%]{\\n    background-color: #edfff1;\\n    text-align: center;\\n    padding: 20px;\\n}\\n.end-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{\\n    color: #111;\\n    text-transform: capitalize;\\n\\n}\\n@media(max-width:890px){\\n    nav[_ngcontent-%COMP%]{\\n        padding:20px 3%;\\n        transition: .4s;\\n    }\\n}\\n@media(max-width:750px){\\n    .navmenu[_ngcontent-%COMP%]{\\n        position: absolute;\\n        top:100%;\\n        right: 0;\\n        width: 300px;\\n        height: 130vh;\\n        background: #dbdfdc;\\n        display: flex;\\n        flex-direction: column;\\n        align-items: center;\\n        padding: 120px 30px;\\n        transition: all .42s;\\n        a{\\n            display: block;\\n            margin: 18px 0;\\n        }\\n    }\\n}\\n        \\n\\n\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["AppComponent", "constructor", "title", "selectors", "decls", "vars", "consts", "template", "AppComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd"], "sources": ["C:\\Users\\<USER>\\Desktop\\projet angular\\Projet\\src\\app\\app.component.ts", "C:\\Users\\<USER>\\Desktop\\projet angular\\Projet\\src\\app\\app.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-root',\n  templateUrl: './app.component.html',\n  styleUrls: ['./app.component.css']\n})\nexport class AppComponent {\n  title = 'Projet';\n}\n", "<nav>\n    <div class=\"container\">\n        <div class=\"content\">\n            <img class=\"logo\" src=\"assets/women's wear.png\" alt=\"\"routerLink=\"/home\">\n            <ul class=\"navmenu\">\n                <li routerLink=\"/home\">Home</li>\n                <li routerLink=\"/shop\" >Shop</li>\n            </ul>\n            <ul> \n                <li routerLink=\"/login\">Log in</li>\n            </ul>\n        </div>\n    </div>\n</nav>\n<div class=\"container\"><router-outlet></router-outlet>\n</div>\n<br>\n<div class=\"end-text\">\n    <p>Copyright © 2024 .All Rights Reserved.Designed By ... </p>\n</div>"], "mappings": ";;AAOA,OAAM,MAAOA,YAAY;EALzBC,YAAA;IAME,KAAAC,KAAK,GAAG,QAAQ;;;;uBADLF,YAAY;IAAA;EAAA;;;YAAZA,YAAY;MAAAG,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCPzBE,EAAA,CAAAC,cAAA,UAAK;UAGOD,EAAA,CAAAE,SAAA,aAAyE;UACzEF,EAAA,CAAAC,cAAA,YAAoB;UACOD,EAAA,CAAAG,MAAA,WAAI;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAChCJ,EAAA,CAAAC,cAAA,YAAwB;UAAAD,EAAA,CAAAG,MAAA,WAAI;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAErCJ,EAAA,CAAAC,cAAA,SAAI;UACwBD,EAAA,CAAAG,MAAA,cAAM;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAKnDJ,EAAA,CAAAC,cAAA,cAAuB;UAAAD,EAAA,CAAAE,SAAA,qBAA+B;UACtDF,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAE,SAAA,UAAI;UACJF,EAAA,CAAAC,cAAA,cAAsB;UACfD,EAAA,CAAAG,MAAA,mEAAsD;UAAAH,EAAA,CAAAI,YAAA,EAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}