{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nexport let ShopComponent = class ShopComponent {\n  constructor() {\n    this.selectedCategory = '';\n    this.searchQuery = '';\n    this.items = [{\n      id: 1,\n      title: 'Item 1',\n      description: 'Description 1',\n      price: 50,\n      category: '1',\n      imgSrc: 'assets/4.jpg'\n    }, {\n      id: 2,\n      title: 'Item 2',\n      description: 'Description 2',\n      price: 60,\n      category: '2',\n      imgSrc: 'assets/2.jpg'\n    }, {\n      id: 3,\n      title: 'Item 3',\n      description: 'Description 3',\n      price: 70,\n      category: '1',\n      imgSrc: 'assets/7.jpg'\n    }, {\n      id: 4,\n      title: 'Item 4',\n      description: 'Description 4',\n      price: 80,\n      category: '3',\n      imgSrc: 'assets/8.jpg'\n    }];\n  }\n  getFilteredItems() {\n    return this.items.filter(item => !this.selectedCategory || item.category === this.selectedCategory);\n  }\n  performSearch() {\n    return this.items.filter(item => item.title.toLowerCase().includes(this.searchQuery.toLowerCase()) || item.description.toLowerCase().includes(this.searchQuery.toLowerCase()));\n  }\n};\nShopComponent = __decorate([Component({\n  selector: 'app-shop',\n  templateUrl: './shop.component.html',\n  styleUrls: ['./shop.component.css']\n})], ShopComponent);", "map": {"version": 3, "names": ["Component", "ShopComponent", "constructor", "selectedCate<PERSON><PERSON>", "searchQuery", "items", "id", "title", "description", "price", "category", "imgSrc", "getFilteredItems", "filter", "item", "performSearch", "toLowerCase", "includes", "__decorate", "selector", "templateUrl", "styleUrls"], "sources": ["C:\\Users\\<USER>\\Desktop\\projet angular\\Projet\\src\\shop\\shop.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-shop',\n  templateUrl: './shop.component.html',\n  styleUrls: ['./shop.component.css']\n})\nexport class ShopComponent {\n  selectedCategory: string = '';\n  searchQuery: string = '';\n  items = [\n    { id: 1, title: 'Item 1', description: 'Description 1', price: 50, category: '1', imgSrc:'assets/4.jpg'},\n    { id: 2, title: 'Item 2', description: 'Description 2', price: 60, category: '2', imgSrc:'assets/2.jpg'  },\n    { id: 3, title: 'Item 3', description: 'Description 3', price: 70, category: '1', imgSrc:'assets/7.jpg'  },\n    { id: 4, title: 'Item 4', description: 'Description 4', price: 80, category: '3', imgSrc:'assets/8.jpg'  }\n  ];\n\n  getFilteredItems() {\n    return this.items.filter(item => !this.selectedCategory || item.category === this.selectedCategory);\n  }\n  performSearch() {\n    return this.items.filter(item =>\n      item.title.toLowerCase().includes(this.searchQuery.toLowerCase()) || item.description.toLowerCase().includes(this.searchQuery.toLowerCase()));\n  }\n}\n"], "mappings": ";AAAA,SAASA,SAAS,QAAQ,eAAe;AAOlC,WAAMC,aAAa,GAAnB,MAAMA,aAAa;EAAnBC,YAAA;IACL,KAAAC,gBAAgB,GAAW,EAAE;IAC7B,KAAAC,WAAW,GAAW,EAAE;IACxB,KAAAC,KAAK,GAAG,CACN;MAAEC,EAAE,EAAE,CAAC;MAAEC,KAAK,EAAE,QAAQ;MAAEC,WAAW,EAAE,eAAe;MAAEC,KAAK,EAAE,EAAE;MAAEC,QAAQ,EAAE,GAAG;MAAEC,MAAM,EAAC;IAAc,CAAC,EACxG;MAAEL,EAAE,EAAE,CAAC;MAAEC,KAAK,EAAE,QAAQ;MAAEC,WAAW,EAAE,eAAe;MAAEC,KAAK,EAAE,EAAE;MAAEC,QAAQ,EAAE,GAAG;MAAEC,MAAM,EAAC;IAAc,CAAG,EAC1G;MAAEL,EAAE,EAAE,CAAC;MAAEC,KAAK,EAAE,QAAQ;MAAEC,WAAW,EAAE,eAAe;MAAEC,KAAK,EAAE,EAAE;MAAEC,QAAQ,EAAE,GAAG;MAAEC,MAAM,EAAC;IAAc,CAAG,EAC1G;MAAEL,EAAE,EAAE,CAAC;MAAEC,KAAK,EAAE,QAAQ;MAAEC,WAAW,EAAE,eAAe;MAAEC,KAAK,EAAE,EAAE;MAAEC,QAAQ,EAAE,GAAG;MAAEC,MAAM,EAAC;IAAc,CAAG,CAC3G;EASH;EAPEC,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACP,KAAK,CAACQ,MAAM,CAACC,IAAI,IAAI,CAAC,IAAI,CAACX,gBAAgB,IAAIW,IAAI,CAACJ,QAAQ,KAAK,IAAI,CAACP,gBAAgB,CAAC;EACrG;EACAY,aAAaA,CAAA;IACX,OAAO,IAAI,CAACV,KAAK,CAACQ,MAAM,CAACC,IAAI,IAC3BA,IAAI,CAACP,KAAK,CAACS,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACb,WAAW,CAACY,WAAW,EAAE,CAAC,IAAIF,IAAI,CAACN,WAAW,CAACQ,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAACb,WAAW,CAACY,WAAW,EAAE,CAAC,CAAC;EACjJ;CACD;AAjBYf,aAAa,GAAAiB,UAAA,EALzBlB,SAAS,CAAC;EACTmB,QAAQ,EAAE,UAAU;EACpBC,WAAW,EAAE,uBAAuB;EACpCC,SAAS,EAAE,CAAC,sBAAsB;CACnC,CAAC,C,EACWpB,aAAa,CAiBzB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}