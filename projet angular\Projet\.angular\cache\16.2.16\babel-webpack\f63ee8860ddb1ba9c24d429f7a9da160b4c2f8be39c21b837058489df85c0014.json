{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nexport class LoginComponent {\n  constructor() {\n    this.username = '';\n  }\n  static {\n    this.ɵfac = function LoginComponent_Factory(t) {\n      return new (t || LoginComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LoginComponent,\n      selectors: [[\"app-login\"]],\n      decls: 13,\n      vars: 3,\n      consts: [[\"align\", \"center\"], [\"src\", \"assets/logo.jpg\", \"alt\", \"Logo\"], [\"align\", \"center\", 1, \"container\"], [\"for\", \"username\"], [\"type\", \"text\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"password\", 3, \"hidden\"], [\"type\", \"password\", 3, \"hidden\"]],\n      template: function LoginComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵelement(1, \"img\", 1);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(2, \"div\", 2)(3, \"fieldset\")(4, \"legend\");\n          i0.ɵɵtext(5, \"Register\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"form\")(7, \"label\", 3);\n          i0.ɵɵtext(8, \"Username\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"input\", 4);\n          i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_9_listener($event) {\n            return ctx.username = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"label\", 5);\n          i0.ɵɵtext(11, \"Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(12, \"input\", 6);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngModel\", ctx.username);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"hidden\", !ctx.username);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"hidden\", !ctx.username);\n        }\n      },\n      dependencies: [i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.NgModel, i1.NgForm],\n      styles: [\"img[_ngcontent-%COMP%]{\\n    width: 150px;\\n    height: 150px;\\n} \\n.container[_ngcontent-%COMP%]{\\n    border-style:solid ;\\n    border-color: #950b0b;\\n    \\n}\\nlegend[_ngcontent-%COMP%]{\\n    font-size:50px;\\n    font-family: 'Trebuchet MS'; \\n    font-style: italic;\\n    font-weight:bold; \\n    color: rgb(22, 21, 21);\\n}\\np[_ngcontent-%COMP%]{\\n    font-size:15px;\\n    font-family: 'Lucida Sans Unicode';\\n    font-style: italic;  \\n    color: rgb(22, 21, 21);\\n    \\n}\\nselect[_ngcontent-%COMP%]{\\n    padding:1px 45px;\\n}\\n    \\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9sb2dpbi9sb2dpbi5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0lBQ0ksWUFBWTtJQUNaLGFBQWE7QUFDakI7QUFDQTtJQUNJLG1CQUFtQjtJQUNuQixxQkFBcUI7O0FBRXpCO0FBQ0E7SUFDSSxjQUFjO0lBQ2QsMkJBQTJCO0lBQzNCLGtCQUFrQjtJQUNsQixnQkFBZ0I7SUFDaEIsc0JBQXNCO0FBQzFCO0FBQ0E7SUFDSSxjQUFjO0lBQ2Qsa0NBQWtDO0lBQ2xDLGtCQUFrQjtJQUNsQixzQkFBc0I7O0FBRTFCO0FBQ0E7SUFDSSxnQkFBZ0I7QUFDcEIiLCJzb3VyY2VzQ29udGVudCI6WyJpbWd7XHJcbiAgICB3aWR0aDogMTUwcHg7XHJcbiAgICBoZWlnaHQ6IDE1MHB4O1xyXG59IFxyXG4uY29udGFpbmVye1xyXG4gICAgYm9yZGVyLXN0eWxlOnNvbGlkIDtcclxuICAgIGJvcmRlci1jb2xvcjogIzk1MGIwYjtcclxuICAgIFxyXG59XHJcbmxlZ2VuZHtcclxuICAgIGZvbnQtc2l6ZTo1MHB4O1xyXG4gICAgZm9udC1mYW1pbHk6ICdUcmVidWNoZXQgTVMnOyBcclxuICAgIGZvbnQtc3R5bGU6IGl0YWxpYztcclxuICAgIGZvbnQtd2VpZ2h0OmJvbGQ7IFxyXG4gICAgY29sb3I6IHJnYigyMiwgMjEsIDIxKTtcclxufVxyXG5we1xyXG4gICAgZm9udC1zaXplOjE1cHg7XHJcbiAgICBmb250LWZhbWlseTogJ0x1Y2lkYSBTYW5zIFVuaWNvZGUnO1xyXG4gICAgZm9udC1zdHlsZTogaXRhbGljOyAgXHJcbiAgICBjb2xvcjogcmdiKDIyLCAyMSwgMjEpO1xyXG4gICAgXHJcbn1cclxuc2VsZWN0e1xyXG4gICAgcGFkZGluZzoxcHggNDVweDtcclxufVxyXG4gICAgIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["LoginComponent", "constructor", "username", "selectors", "decls", "vars", "consts", "template", "LoginComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵlistener", "LoginComponent_Template_input_ngModelChange_9_listener", "$event", "ɵɵadvance", "ɵɵproperty"], "sources": ["C:\\Users\\<USER>\\Desktop\\projet angular\\Projet\\src\\login\\login.component.ts", "C:\\Users\\<USER>\\Desktop\\projet angular\\Projet\\src\\login\\login.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-login',\n  templateUrl: './login.component.html',\n  styleUrls: ['./login.component.css']\n})\nexport class LoginComponent {\n  username: string = '';\n}\n", "<div align=\"center\">\n    <img src=\"assets/logo.jpg\" alt=\"Logo\" />\n</div>\n<div class=\"container\" align=\"center\">\n    <fieldset>\n        <legend>Register</legend>\n        <form>\n            <label for=\"username\">Username</label>\n            <input type=\"text\" [(ngModel)]=\"username\">\n            <label for=\"password\" [hidden]=\"!username\">Password</label>\n            <input type=\"password\" [hidden]=\"!username\">\n        </form>\n    </fieldset>\n</div>\n"], "mappings": ";;AAOA,OAAM,MAAOA,cAAc;EAL3BC,YAAA;IAME,KAAAC,QAAQ,GAAW,EAAE;;;;uBADVF,cAAc;IAAA;EAAA;;;YAAdA,cAAc;MAAAG,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCP3BE,EAAA,CAAAC,cAAA,aAAoB;UAChBD,EAAA,CAAAE,SAAA,aAAwC;UAC5CF,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,aAAsC;UAEtBD,EAAA,CAAAI,MAAA,eAAQ;UAAAJ,EAAA,CAAAG,YAAA,EAAS;UACzBH,EAAA,CAAAC,cAAA,WAAM;UACoBD,EAAA,CAAAI,MAAA,eAAQ;UAAAJ,EAAA,CAAAG,YAAA,EAAQ;UACtCH,EAAA,CAAAC,cAAA,eAA0C;UAAvBD,EAAA,CAAAK,UAAA,2BAAAC,uDAAAC,MAAA;YAAA,OAAAR,GAAA,CAAAR,QAAA,GAAAgB,MAAA;UAAA,EAAsB;UAAzCP,EAAA,CAAAG,YAAA,EAA0C;UAC1CH,EAAA,CAAAC,cAAA,gBAA2C;UAAAD,EAAA,CAAAI,MAAA,gBAAQ;UAAAJ,EAAA,CAAAG,YAAA,EAAQ;UAC3DH,EAAA,CAAAE,SAAA,gBAA4C;UAChDF,EAAA,CAAAG,YAAA,EAAO;;;UAHgBH,EAAA,CAAAQ,SAAA,GAAsB;UAAtBR,EAAA,CAAAS,UAAA,YAAAV,GAAA,CAAAR,QAAA,CAAsB;UACnBS,EAAA,CAAAQ,SAAA,GAAoB;UAApBR,EAAA,CAAAS,UAAA,YAAAV,GAAA,CAAAR,QAAA,CAAoB;UACnBS,EAAA,CAAAQ,SAAA,GAAoB;UAApBR,EAAA,CAAAS,UAAA,YAAAV,GAAA,CAAAR,QAAA,CAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}