{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class GlobalErrorHandler {\n  handleError(error) {\n    // Log to console\n    console.error('An error occurred:', error);\n    // You could also send to a logging service\n    // this.loggingService.logError(error);\n  }\n\n  static {\n    this.ɵfac = function GlobalErrorHandler_Factory(t) {\n      return new (t || GlobalErrorHandler)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: GlobalErrorHandler,\n      factory: GlobalErrorHandler.ɵfac\n    });\n  }\n}", "map": {"version": 3, "names": ["GlobalErrorHandler", "handleError", "error", "console", "factory", "ɵfac"], "sources": ["C:\\Users\\<USER>\\Desktop\\projet angular\\Projet\\src\\app\\global-error-handler.ts"], "sourcesContent": ["import { ErrorHandler, Injectable } from '@angular/core';\n\n@Injectable()\nexport class GlobalErrorHandler implements ErrorHandler {\n  handleError(error: any): void {\n    // Log to console\n    console.error('An error occurred:', error);\n    \n    // You could also send to a logging service\n    // this.loggingService.logError(error);\n  }\n}"], "mappings": ";AAGA,OAAM,MAAOA,kBAAkB;EAC7BC,WAAWA,CAACC,KAAU;IACpB;IACAC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;IAE1C;IACA;EACF;;;;uBAPWF,kBAAkB;IAAA;EAAA;;;aAAlBA,kBAAkB;MAAAI,OAAA,EAAlBJ,kBAAkB,CAAAK;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}