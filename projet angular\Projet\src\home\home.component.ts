import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { HttpClient } from '@angular/common/http';

// Define the Produit interface
interface Produit {
  id: number;
  title: string;
  description: string;
  imgSrc: string;
}

@Component({
  selector: 'app-home',
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.css']
})
export class HomeComponent implements OnInit {
  items: Produit[] = [];

  constructor(private router: Router, private http: HttpClient) {}

  ngOnInit() {
    this.http.get<{products: Produit[]}>('assets/product.json').subscribe({
      next: (response: {products: Produit[]}) => {
        this.items = response.products;
        console.log('Loaded items:', this.items);
      },
      error: (err: any) => {
        console.error('Error loading JSON:', err);
        // Add proper error handling
        this.items = []; // Provide fallback empty array
      }
    });
  }

  navigateToShop() {
    this.router.navigate(['/shop']);
  }
}



