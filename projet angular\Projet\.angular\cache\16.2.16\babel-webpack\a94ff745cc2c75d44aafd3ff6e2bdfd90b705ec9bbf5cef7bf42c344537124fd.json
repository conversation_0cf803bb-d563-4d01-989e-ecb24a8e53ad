{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/common/http\";\nimport * as i3 from \"@angular/common\";\nfunction HomeComponent_div_17_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵelement(1, \"img\", 28);\n    i0.ɵɵelementStart(2, \"div\", 29)(3, \"span\", 30);\n    i0.ɵɵtext(4, \"Sale\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 31)(6, \"div\", 32);\n    i0.ɵɵelement(7, \"i\", 33)(8, \"i\", 33)(9, \"i\", 33)(10, \"i\", 33)(11, \"i\", 34);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 35)(13, \"h4\");\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"titlecase\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"h4\");\n    i0.ɵɵtext(17);\n    i0.ɵɵpipe(18, \"slice\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"p\");\n    i0.ɵɵtext(20);\n    i0.ɵɵpipe(21, \"currency\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const item_r2 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵpropertyInterpolate(\"alt\", item_r2.title);\n    i0.ɵɵproperty(\"src\", item_r2.imgSrc, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(13);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(15, 6, item_r2.title));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", i0.ɵɵpipeBind3(18, 8, item_r2.description, 0, 50), \"\", item_r2.description.length > 50 ? \"...\" : \"\", \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind3(21, 12, 50, \"TND\", \"symbol\"));\n  }\n}\nfunction HomeComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵtemplate(1, HomeComponent_div_17_div_1_Template, 22, 16, \"div\", 26);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.items);\n  }\n}\nexport class HomeComponent {\n  constructor(router, http) {\n    this.router = router;\n    this.http = http;\n    this.items = [];\n  }\n  ngOnInit() {\n    this.http.get('assets/product.json').subscribe({\n      next: response => {\n        this.items = response.products;\n        console.log('Loaded items:', this.items);\n      },\n      error: err => {\n        console.error('Error loading JSON:', err);\n        // Add proper error handling\n        this.items = []; // Provide fallback empty array\n      }\n    });\n  }\n\n  navigateToShop() {\n    this.router.navigate(['/shop']);\n  }\n  static {\n    this.ɵfac = function HomeComponent_Factory(t) {\n      return new (t || HomeComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.HttpClient));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HomeComponent,\n      selectors: [[\"app-home\"]],\n      decls: 77,\n      vars: 1,\n      consts: [[1, \"section\"], [1, \"btn-shop\", 3, \"click\"], [1, \"trending-product\"], [1, \"center-text\"], [\"class\", \"grid-Products\", 4, \"ngIf\"], [1, \"client-reviews\"], [1, \"reviews\"], [\"src\", \"assets/woman.jpg\", \"alt\", \"\"], [1, \"contact\"], [1, \"contact-info\"], [1, \"first-info\"], [\"src\", \"assets/women's wear.png\", \"alt\", \"\"], [1, \"social-icon\"], [\"href\", \"https://www.facebook.com/facebook/\"], [1, \"bx\", \"bxl-facebook-circle\"], [\"href\", \"https://x.com/\"], [1, \"bx\", \"bxl-twitter\"], [\"href\", \"https://www.instagram.com/instagram/\"], [1, \"bx\", \"bxl-instagram\"], [\"href\", \"https://www.youtube.com/@YouTube\"], [1, \"bx\", \"bxl-youtube\"], [\"href\", \"https://www.linkedin.com/feed/\"], [1, \"bx\", \"bxl-linkedin\"], [1, \"second-info\"], [1, \"third-info\"], [1, \"grid-Products\"], [\"class\", \"grid-row\", 4, \"ngFor\", \"ngForOf\"], [1, \"grid-row\"], [3, \"src\", \"alt\"], [1, \"product-text\"], [1, \"badge\"], [1, \"product-info\"], [1, \"ratting\"], [1, \"bx\", \"bxs-star\"], [1, \"bx\", \"bxs-star-half\"], [1, \"price\"]],\n      template: function HomeComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"h5\");\n          i0.ɵɵtext(2, \"2025 collection\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"h1\");\n          i0.ɵɵtext(4, \"New\");\n          i0.ɵɵelement(5, \"br\");\n          i0.ɵɵtext(6, \"collection 2025\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"h6\");\n          i0.ɵɵtext(8, \"There's nothing like trend\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"p\", 1);\n          i0.ɵɵlistener(\"click\", function HomeComponent_Template_p_click_9_listener() {\n            return ctx.navigateToShop();\n          });\n          i0.ɵɵtext(10, \"Shop now\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(11, \"div\", 2)(12, \"div\", 3)(13, \"h2\");\n          i0.ɵɵtext(14, \"Our tranding \");\n          i0.ɵɵelementStart(15, \"span\");\n          i0.ɵɵtext(16, \"Products\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(17, HomeComponent_div_17_Template, 2, 1, \"div\", 4);\n          i0.ɵɵelement(18, \"br\")(19, \"br\");\n          i0.ɵɵelementStart(20, \"section\", 5)(21, \"div\", 6)(22, \"h3\");\n          i0.ɵɵtext(23, \"Client Reviews\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(24, \"img\", 7);\n          i0.ɵɵelementStart(25, \"p\");\n          i0.ɵɵtext(26, \"Hello, my name is Sarah, and I\\u2019ve been shopping for stylish and comfortable women\\u2019s clothing online for years. I\\u2019m always looking for outfits that reflect my personality\\u2014elegant yet practical for my busy lifestyle. I appreciate a website that's easy to navigate with clear size guides, diverse collections, and quality fabrics. Fast delivery and excellent customer service are also a must for me. A good online shopping experience keeps me coming back for more!\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"h2\");\n          i0.ɵɵtext(28, \"Sarah\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"p\");\n          i0.ɵɵtext(30, \"CEO of Addle\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelement(31, \"br\")(32, \"br\");\n          i0.ɵɵelementStart(33, \"section\", 8)(34, \"div\", 9)(35, \"div\", 10);\n          i0.ɵɵelement(36, \"img\", 11);\n          i0.ɵɵelementStart(37, \"p\");\n          i0.ɵɵtext(38, \"123 Olive Street, Sidi Bouzid, 9100\");\n          i0.ɵɵelement(39, \"br\");\n          i0.ɵɵtext(40, \" Tunisia\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"p\");\n          i0.ɵɵtext(42, \"<EMAIL>\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"div\", 12)(44, \"a\", 13);\n          i0.ɵɵelement(45, \"i\", 14);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"a\", 15);\n          i0.ɵɵelement(47, \"i\", 16);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(48, \"a\", 17);\n          i0.ɵɵelement(49, \"i\", 18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(50, \"a\", 19);\n          i0.ɵɵelement(51, \"i\", 20);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(52, \"a\", 21);\n          i0.ɵɵelement(53, \"i\", 22);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(54, \"div\", 23)(55, \"h4\");\n          i0.ɵɵtext(56, \"Support\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(57, \"p\");\n          i0.ɵɵtext(58, \"About us\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(59, \"p\");\n          i0.ɵɵtext(60, \"Contact us\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(61, \"p\");\n          i0.ɵɵtext(62, \"size guide\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(63, \"p\");\n          i0.ɵɵtext(64, \"Privacy\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(65, \"p\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(66, \"div\", 24)(67, \"h4\");\n          i0.ɵɵtext(68, \"Company\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(69, \"p\");\n          i0.ɵɵtext(70, \"About\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(71, \"p\");\n          i0.ɵɵtext(72, \"Blog\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(73, \"p\");\n          i0.ɵɵtext(74, \"Affiliate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(75, \"p\");\n          i0.ɵɵtext(76, \"Login\");\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(17);\n          i0.ɵɵproperty(\"ngIf\", ctx.items.length > 0);\n        }\n      },\n      dependencies: [i3.NgForOf, i3.NgIf, i3.SlicePipe, i3.TitleCasePipe, i3.CurrencyPipe],\n      styles: [\"*[_ngcontent-%COMP%]{\\n    margin: 0;\\n    padding: 0;\\n    box-sizing: border-box;\\n    scroll-behavior: smooth;\\n    font-family: 'sans-serif';\\n    list-style: none;\\n    text-decoration: none;\\n}\\n\\n\\n.section[_ngcontent-%COMP%] {\\n  padding: 5% 10%;\\n  width: 100%;\\n  min-height: 80vh;\\n  background-image: linear-gradient(rgba(45, 45, 45, 0.8), rgba(45, 45, 45, 0.9)), url('banner-3.png');\\n  background-position: center;\\n  background-size: cover;\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: center;\\n  position: relative;\\n}\\n\\n.section[_ngcontent-%COMP%]::before {\\n  content: '';\\n  position: absolute;\\n  bottom: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 70px;\\n  background-image: url(\\\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1440 320'%3E%3Cpath fill='%233a2a33' fill-opacity='1' d='M0,96L48,112C96,128,192,160,288,186.7C384,213,480,235,576,224C672,213,768,171,864,149.3C960,128,1056,128,1152,149.3C1248,171,1344,213,1392,234.7L1440,256L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z'%3E%3C/path%3E%3C/svg%3E\\\");\\n  background-size: cover;\\n  background-repeat: no-repeat;\\n}\\n\\n.section[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  color: var(--accent);\\n  font-size: 18px;\\n  text-transform: uppercase;\\n  letter-spacing: 3px;\\n  font-weight: 500;\\n  position: relative;\\n  display: inline-block;\\n  margin-bottom: 10px;\\n}\\n\\n.section[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]::after {\\n  content: '\\u2665';\\n  position: absolute;\\n  right: -25px;\\n  color: var(--primary);\\n}\\n\\n.section[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  color: var(--primary);\\n  font-size: 4rem;\\n  line-height: 1.1;\\n  font-weight: 700;\\n  margin: 15px 0;\\n  font-family: var(--font-cursive);\\n  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);\\n}\\n\\n.section[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  color: var(--light);\\n  font-size: 22px;\\n  font-style: italic;\\n  margin-bottom: 30px;\\n}\\n\\n.section[_ngcontent-%COMP%]   .btn-shop[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  padding: 14px 35px;\\n  background-color: var(--primary);\\n  color: var(--dark);\\n  font-weight: 600;\\n  text-transform: uppercase;\\n  letter-spacing: 1px;\\n  cursor: pointer;\\n  transition: var(--transition);\\n  border-radius: 30px;\\n  box-shadow: 0 5px 15px rgba(255, 107, 156, 0.3);\\n  position: relative;\\n  overflow: hidden;\\n  z-index: 1;\\n}\\n\\n.section[_ngcontent-%COMP%]   .btn-shop[_ngcontent-%COMP%]::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 0%;\\n  height: 100%;\\n  background-color: var(--secondary);\\n  transition: all 0.5s ease;\\n  z-index: -1;\\n  border-radius: 30px;\\n}\\n\\n.section[_ngcontent-%COMP%]   .btn-shop[_ngcontent-%COMP%]:hover::before {\\n  width: 100%;\\n}\\n\\n.section[_ngcontent-%COMP%]   .btn-shop[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-5px);\\n  box-shadow: 0 8px 20px rgba(255, 107, 156, 0.4);\\n}\\n\\n\\n.center-text[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin: 70px 0 50px;\\n}\\n\\n.center-text[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 36px;\\n  position: relative;\\n  display: inline-block;\\n  color: var(--primary);\\n  font-family: var(--font-cursive);\\n}\\n\\n.center-text[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]::before, .center-text[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]::after {\\n  content: '\\u2665';\\n  position: absolute;\\n  top: 50%;\\n  transform: translateY(-50%);\\n  color: var(--accent);\\n  font-size: 24px;\\n}\\n\\n.center-text[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]::before {\\n  left: -40px;\\n}\\n\\n.center-text[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]::after {\\n  right: -40px;\\n}\\n\\n.center-text[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  color: var(--secondary);\\n}\\n\\n.grid-Products[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));\\n  gap: 30px;\\n  padding: 0 20px;\\n}\\n\\n.grid-row[_ngcontent-%COMP%] {\\n  background-color: var(--medium);\\n  border-radius: 15px;\\n  overflow: hidden;\\n  box-shadow: var(--shadow);\\n  transition: var(--transition);\\n  position: relative;\\n}\\n\\n.grid-row[_ngcontent-%COMP%]::before {\\n  content: '';\\n  position: absolute;\\n  top: -5px;\\n  left: -5px;\\n  right: -5px;\\n  bottom: -5px;\\n  background: linear-gradient(45deg, var(--primary), var(--secondary), var(--accent));\\n  z-index: -1;\\n  border-radius: 20px;\\n  opacity: 0;\\n  transition: var(--transition);\\n}\\n\\n.grid-row[_ngcontent-%COMP%]:hover::before {\\n  opacity: 1;\\n}\\n\\n.grid-row[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-10px) scale(1.02);\\n  box-shadow: 0 15px 30px rgba(255, 107, 156, 0.2);\\n}\\n\\n.grid-row[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 250px;\\n  object-fit: cover;\\n  transition: var(--transition);\\n}\\n\\n.grid-row[_ngcontent-%COMP%]:hover   img[_ngcontent-%COMP%] {\\n  transform: scale(1.05);\\n}\\n\\n.product-text[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 15px;\\n  right: 15px;\\n}\\n\\n.badge[_ngcontent-%COMP%] {\\n  background-color: var(--primary);\\n  color: var(--dark);\\n  padding: 5px 12px;\\n  border-radius: 20px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  box-shadow: 0 3px 8px rgba(255, 107, 156, 0.3);\\n}\\n\\n.product-info[_ngcontent-%COMP%] {\\n  padding: 15px;\\n}\\n\\n.ratting[_ngcontent-%COMP%] {\\n  margin-bottom: 10px;\\n}\\n\\n.ratting[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: var(--accent);\\n  font-size: 16px;\\n  margin-right: 2px;\\n}\\n\\n.price[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  color: var(--light);\\n  margin-bottom: 5px;\\n  text-transform: capitalize;\\n}\\n\\n.price[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: var(--primary);\\n  font-weight: 600;\\n  font-size: 18px;\\n}\\n\\n\\n.client-reviews[_ngcontent-%COMP%] {\\n  background-color: var(--dark-pink);\\n  padding: 80px 20px;\\n  margin-top: 80px;\\n  position: relative;\\n}\\n\\n.client-reviews[_ngcontent-%COMP%]::before, .client-reviews[_ngcontent-%COMP%]::after {\\n  content: '';\\n  position: absolute;\\n  width: 100%;\\n  height: 30px;\\n  background-image: url(\\\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1440 320'%3E%3Cpath fill='%233a2a33' fill-opacity='1' d='M0,96L48,112C96,128,192,160,288,186.7C384,213,480,235,576,224C672,213,768,171,864,149.3C960,128,1056,128,1152,149.3C1248,171,1344,213,1392,234.7L1440,256L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z'%3E%3C/path%3E%3C/svg%3E\\\");\\n  background-size: cover;\\n  background-repeat: no-repeat;\\n}\\n\\n.client-reviews[_ngcontent-%COMP%]::before {\\n  top: -30px;\\n  transform: rotate(180deg);\\n}\\n\\n.client-reviews[_ngcontent-%COMP%]::after {\\n  bottom: -30px;\\n}\\n\\n.reviews[_ngcontent-%COMP%] {\\n  max-width: 800px;\\n  margin: 0 auto;\\n  text-align: center;\\n  background-color: var(--medium);\\n  padding: 40px;\\n  border-radius: 20px;\\n  box-shadow: var(--shadow);\\n  position: relative;\\n}\\n\\n.reviews[_ngcontent-%COMP%]::before, .reviews[_ngcontent-%COMP%]::after {\\n  content: '\\\"';\\n  position: absolute;\\n  font-size: 100px;\\n  color: var(--primary);\\n  opacity: 0.2;\\n  font-family: serif;\\n  line-height: 1;\\n}\\n\\n.reviews[_ngcontent-%COMP%]::before {\\n  top: 20px;\\n  left: 20px;\\n}\\n\\n.reviews[_ngcontent-%COMP%]::after {\\n  bottom: 0;\\n  right: 20px;\\n  transform: rotate(180deg);\\n}\\n\\n.reviews[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 32px;\\n  margin-bottom: 40px;\\n  position: relative;\\n  display: inline-block;\\n  color: var(--primary);\\n  font-family: var(--font-cursive);\\n}\\n\\n.reviews[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]::after {\\n  content: '';\\n  position: absolute;\\n  bottom: -10px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  width: 70px;\\n  height: 3px;\\n  background: linear-gradient(to right, var(--secondary), var(--primary), var(--secondary));\\n}\\n\\n.reviews[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100px;\\n  height: 100px;\\n  border-radius: 50%;\\n  object-fit: cover;\\n  border: 5px solid white;\\n  box-shadow: 0 0 0 3px var(--secondary);\\n  margin: 20px auto;\\n}\\n\\n.reviews[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 16px;\\n  line-height: 1.8;\\n  margin-bottom: 20px;\\n  font-style: italic;\\n}\\n\\n.reviews[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 22px;\\n  color: var(--primary);\\n  margin-bottom: 5px;\\n}\\n\\n\\n.contact[_ngcontent-%COMP%] {\\n  padding: 80px 20px;\\n  background-color: white;\\n}\\n\\n.contact-info[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n  gap: 40px;\\n}\\n\\n.contact-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: var(--primary);\\n  font-size: 20px;\\n  text-transform: uppercase;\\n  margin-bottom: 20px;\\n  position: relative;\\n  display: inline-block;\\n}\\n\\n.contact-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]::after {\\n  content: '';\\n  position: absolute;\\n  bottom: -8px;\\n  left: 0;\\n  width: 40px;\\n  height: 2px;\\n  background: linear-gradient(to right, var(--primary), var(--secondary));\\n}\\n\\n.first-info[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 140px;\\n  margin-bottom: 20px;\\n}\\n\\n.contact-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  margin-bottom: 10px;\\n  transition: var(--transition);\\n  position: relative;\\n  padding-left: 25px;\\n}\\n\\n.contact-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]::before {\\n  content: '\\u2740';\\n  position: absolute;\\n  left: 0;\\n  color: var(--secondary);\\n  font-size: 14px;\\n}\\n\\n.contact-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:hover {\\n  color: var(--primary);\\n  transform: translateX(5px);\\n}\\n\\n.social-icon[_ngcontent-%COMP%] {\\n  display: flex;\\n  margin-top: 20px;\\n}\\n\\n.social-icon[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center\\n}\\n\\n\\n.contact[_ngcontent-%COMP%] {\\n  padding: 80px 20px;\\n  background-color: white;\\n}\\n\\n.contact-info[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n  gap: 40px;\\n}\\n\\n.contact-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: var(--primary);\\n  font-size: 18px;\\n  text-transform: uppercase;\\n  margin-bottom: 20px;\\n  position: relative;\\n}\\n\\n.contact-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]::after {\\n  content: '';\\n  position: absolute;\\n  bottom: -8px;\\n  left: 0;\\n  width: 40px;\\n  height: 2px;\\n  background-color: var(--accent);\\n}\\n\\n.first-info[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 140px;\\n  margin-bottom: 20px;\\n}\\n\\n.contact-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  margin-bottom: 10px;\\n  transition: var(--transition);\\n}\\n\\n.contact-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:hover {\\n  color: var(--accent);\\n  transform: translateX(5px);\\n}\\n\\n.social-icon[_ngcontent-%COMP%] {\\n  display: flex;\\n  margin-top: 20px;\\n}\\n\\n.social-icon[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 40px;\\n  height: 40px;\\n  background-color: var(--light);\\n  border-radius: 50%;\\n  margin-right: 10px;\\n  transition: var(--transition);\\n}\\n\\n.social-icon[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  background-color: var(--accent);\\n  color: white;\\n  transform: translateY(-5px);\\n}\\n\\n.social-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 20px;\\n}\\n\\n@media (max-width: 768px) {\\n  .section[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n    font-size: 2.5rem;\\n  }\\n  \\n  .grid-Products[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\\n  }\\n}\\n.end-text[_ngcontent-%COMP%]{\\n    background-color: #edfff1;\\n    text-align: center;\\n    padding: 20px;\\n}\\n.end-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{\\n    color: #111;\\n    text-transform: capitalize;\\n\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵpropertyInterpolate", "item_r2", "title", "ɵɵproperty", "imgSrc", "ɵɵsanitizeUrl", "ɵɵtextInterpolate", "ɵɵpipeBind1", "ɵɵtextInterpolate2", "ɵɵpipeBind3", "description", "length", "ɵɵtemplate", "HomeComponent_div_17_div_1_Template", "ctx_r0", "items", "HomeComponent", "constructor", "router", "http", "ngOnInit", "get", "subscribe", "next", "response", "products", "console", "log", "error", "err", "navigateToShop", "navigate", "ɵɵdirectiveInject", "i1", "Router", "i2", "HttpClient", "selectors", "decls", "vars", "consts", "template", "HomeComponent_Template", "rf", "ctx", "ɵɵlistener", "HomeComponent_Template_p_click_9_listener", "HomeComponent_div_17_Template"], "sources": ["C:\\Users\\<USER>\\Desktop\\projet angular\\Projet\\src\\home\\home.component.ts", "C:\\Users\\<USER>\\Desktop\\projet angular\\Projet\\src\\home\\home.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { HttpClient } from '@angular/common/http';\n\n// Define the Produit interface\ninterface Produit {\n  id: number;\n  title: string;\n  description: string;\n  imgSrc: string;\n}\n\n@Component({\n  selector: 'app-home',\n  templateUrl: './home.component.html',\n  styleUrls: ['./home.component.css']\n})\nexport class HomeComponent implements OnInit {\n  items: Produit[] = [];\n\n  constructor(private router: Router, private http: HttpClient) {}\n\n  ngOnInit() {\n    this.http.get<{products: Produit[]}>('assets/product.json').subscribe({\n      next: (response: {products: Produit[]}) => {\n        this.items = response.products;\n        console.log('Loaded items:', this.items);\n      },\n      error: (err: any) => {\n        console.error('Error loading JSON:', err);\n        // Add proper error handling\n        this.items = []; // Provide fallback empty array\n      }\n    });\n  }\n\n  navigateToShop() {\n    this.router.navigate(['/shop']);\n  }\n}\n\n\n\n", "<div class=\"section\">\n    <h5>2025 collection</h5>\n    <h1>New<br>collection 2025</h1>\n    <h6>There's nothing like trend</h6>\n    <p class=\"btn-shop\" (click)=\"navigateToShop()\">Shop now</p>\n</div>\n\n<div class=\"trending-product\">\n    <div class=\"center-text\">\n        <h2>Our tranding <span>Products</span></h2>\n    </div>\n    \n    <div class=\"grid-Products\" *ngIf=\"items.length > 0\">\n        <div class=\"grid-row\" *ngFor=\"let item of items\">\n            <img [src]=\"item.imgSrc\" alt=\"{{ item.title }}\">\n\n            <div class=\"product-text\">\n                <span class=\"badge\">Sale</span>\n            </div>\n\n            <div class=\"product-info\">\n                <div class=\"ratting\">\n                    <i class='bx bxs-star'></i>\n                    <i class='bx bxs-star'></i>\n                    <i class='bx bxs-star'></i>\n                    <i class='bx bxs-star'></i>\n                    <i class='bx bxs-star-half'></i>\n                </div>\n\n                <div class=\"price\">\n                    <h4>{{ item.title | titlecase }}</h4>\n                    <h4>{{ item.description | slice:0:50 }}{{ item.description.length > 50 ? '...' : '' }}</h4>\n                    <p>{{ 50 | currency:'TND':'symbol' }}</p>\n\n                </div>\n            </div>\n        </div>\n    </div>\n\n    <br><br>\n    <section class=\"client-reviews\">\n        <div class=\"reviews\">\n            <h3>Client Reviews</h3>\n            <img src=\"assets/woman.jpg\"  alt=\"\">\n            <p>Hello, my name is Sarah, and I’ve been shopping for stylish and comfortable women’s clothing online for years. I’m always looking for outfits that reflect my personality—elegant yet practical for my busy lifestyle. I appreciate a website that's easy to navigate with clear size guides, diverse collections, and quality fabrics. Fast delivery and excellent customer service are also a must for me. A good online shopping experience keeps me coming back for more!</p>\n            <h2>Sarah</h2>\n            <p>CEO of Addle</p>\n        </div>\n    </section>\n</div>\n<br><br>\n<section class=\"contact\">\n    <div class=\"contact-info\">\n        <div class=\"first-info\">\n            <img src=\"assets/women's wear.png\" alt=\"\">\n            <p>123 Olive Street, Sidi Bouzid, 9100<br> Tunisia</p>\n            <p>Herwardrobe&#64;gmail.com</p>\n            <div class=\"social-icon\">\n                <a href=\"https://www.facebook.com/facebook/\"><i class='bx bxl-facebook-circle'></i></a>\n                <a href=\"https://x.com/\"><i class='bx bxl-twitter'></i></a>\n                <a href=\"https://www.instagram.com/instagram/\"><i class='bx bxl-instagram'></i></a>\n                <a href=\"https://www.youtube.com/@YouTube\"><i class='bx bxl-youtube'></i></a>\n                <a href=\"https://www.linkedin.com/feed/\"><i class='bx bxl-linkedin'></i></a>\n            </div>\n        </div>\n        <div class=\"second-info\">\n            <h4>Support</h4>\n            <p>About us</p>\n            <p>Contact us</p>\n            <p>size guide</p>\n            <p>Privacy</p>\n            <p></p>\n        </div>\n        <div class=\"third-info\">\n            <h4>Company</h4>\n            <p>About</p>\n            <p>Blog</p>\n            <p>Affiliate</p>\n            <p>Login</p>\n        </div>\n    </div>\n</section>\n\n\n"], "mappings": ";;;;;;ICaQA,EAAA,CAAAC,cAAA,cAAiD;IAC7CD,EAAA,CAAAE,SAAA,cAAgD;IAEhDF,EAAA,CAAAC,cAAA,cAA0B;IACFD,EAAA,CAAAG,MAAA,WAAI;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAGnCJ,EAAA,CAAAC,cAAA,cAA0B;IAElBD,EAAA,CAAAE,SAAA,YAA2B;IAK/BF,EAAA,CAAAI,YAAA,EAAM;IAENJ,EAAA,CAAAC,cAAA,eAAmB;IACXD,EAAA,CAAAG,MAAA,IAA4B;;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACrCJ,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAG,MAAA,IAAkF;;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC3FJ,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAG,MAAA,IAAkC;;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;IAlBxBJ,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAAM,qBAAA,QAAAC,OAAA,CAAAC,KAAA,CAAsB;IAA1CR,EAAA,CAAAS,UAAA,QAAAF,OAAA,CAAAG,MAAA,EAAAV,EAAA,CAAAW,aAAA,CAAmB;IAgBZX,EAAA,CAAAK,SAAA,IAA4B;IAA5BL,EAAA,CAAAY,iBAAA,CAAAZ,EAAA,CAAAa,WAAA,QAAAN,OAAA,CAAAC,KAAA,EAA4B;IAC5BR,EAAA,CAAAK,SAAA,GAAkF;IAAlFL,EAAA,CAAAc,kBAAA,KAAAd,EAAA,CAAAe,WAAA,QAAAR,OAAA,CAAAS,WAAA,cAAAT,OAAA,CAAAS,WAAA,CAAAC,MAAA,uBAAkF;IACnFjB,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAAY,iBAAA,CAAAZ,EAAA,CAAAe,WAAA,8BAAkC;;;;;IApBrDf,EAAA,CAAAC,cAAA,cAAoD;IAChDD,EAAA,CAAAkB,UAAA,IAAAC,mCAAA,oBAuBM;IACVnB,EAAA,CAAAI,YAAA,EAAM;;;;IAxBqCJ,EAAA,CAAAK,SAAA,GAAQ;IAARL,EAAA,CAAAS,UAAA,YAAAW,MAAA,CAAAC,KAAA,CAAQ;;;ADIvD,OAAM,MAAOC,aAAa;EAGxBC,YAAoBC,MAAc,EAAUC,IAAgB;IAAxC,KAAAD,MAAM,GAANA,MAAM;IAAkB,KAAAC,IAAI,GAAJA,IAAI;IAFhD,KAAAJ,KAAK,GAAc,EAAE;EAE0C;EAE/DK,QAAQA,CAAA;IACN,IAAI,CAACD,IAAI,CAACE,GAAG,CAAwB,qBAAqB,CAAC,CAACC,SAAS,CAAC;MACpEC,IAAI,EAAGC,QAA+B,IAAI;QACxC,IAAI,CAACT,KAAK,GAAGS,QAAQ,CAACC,QAAQ;QAC9BC,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,IAAI,CAACZ,KAAK,CAAC;MAC1C,CAAC;MACDa,KAAK,EAAGC,GAAQ,IAAI;QAClBH,OAAO,CAACE,KAAK,CAAC,qBAAqB,EAAEC,GAAG,CAAC;QACzC;QACA,IAAI,CAACd,KAAK,GAAG,EAAE,CAAC,CAAC;MACnB;KACD,CAAC;EACJ;;EAEAe,cAAcA,CAAA;IACZ,IAAI,CAACZ,MAAM,CAACa,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC;EACjC;;;uBArBWf,aAAa,EAAAtB,EAAA,CAAAsC,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAxC,EAAA,CAAAsC,iBAAA,CAAAG,EAAA,CAAAC,UAAA;IAAA;EAAA;;;YAAbpB,aAAa;MAAAqB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCjB1BjD,EAAA,CAAAC,cAAA,aAAqB;UACbD,EAAA,CAAAG,MAAA,sBAAe;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACxBJ,EAAA,CAAAC,cAAA,SAAI;UAAAD,EAAA,CAAAG,MAAA,UAAG;UAAAH,EAAA,CAAAE,SAAA,SAAI;UAAAF,EAAA,CAAAG,MAAA,sBAAe;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAC/BJ,EAAA,CAAAC,cAAA,SAAI;UAAAD,EAAA,CAAAG,MAAA,iCAA0B;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACnCJ,EAAA,CAAAC,cAAA,WAA+C;UAA3BD,EAAA,CAAAmD,UAAA,mBAAAC,0CAAA;YAAA,OAASF,GAAA,CAAAd,cAAA,EAAgB;UAAA,EAAC;UAACpC,EAAA,CAAAG,MAAA,gBAAQ;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAG/DJ,EAAA,CAAAC,cAAA,cAA8B;UAElBD,EAAA,CAAAG,MAAA,qBAAa;UAAAH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAG,MAAA,gBAAQ;UAAAH,EAAA,CAAAI,YAAA,EAAO;UAG1CJ,EAAA,CAAAkB,UAAA,KAAAmC,6BAAA,iBAyBM;UAENrD,EAAA,CAAAE,SAAA,UAAI;UACJF,EAAA,CAAAC,cAAA,kBAAgC;UAEpBD,EAAA,CAAAG,MAAA,sBAAc;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACvBJ,EAAA,CAAAE,SAAA,cAAoC;UACpCF,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,yeAA6c;UAAAH,EAAA,CAAAI,YAAA,EAAI;UACpdJ,EAAA,CAAAC,cAAA,UAAI;UAAAD,EAAA,CAAAG,MAAA,aAAK;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACdJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,oBAAY;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAI/BJ,EAAA,CAAAE,SAAA,UAAI;UACJF,EAAA,CAAAC,cAAA,kBAAyB;UAGbD,EAAA,CAAAE,SAAA,eAA0C;UAC1CF,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,2CAAmC;UAAAH,EAAA,CAAAE,SAAA,UAAI;UAACF,EAAA,CAAAG,MAAA,gBAAO;UAAAH,EAAA,CAAAI,YAAA,EAAI;UACtDJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,6BAAyB;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAChCJ,EAAA,CAAAC,cAAA,eAAyB;UACwBD,EAAA,CAAAE,SAAA,aAAsC;UAAAF,EAAA,CAAAI,YAAA,EAAI;UACvFJ,EAAA,CAAAC,cAAA,aAAyB;UAAAD,EAAA,CAAAE,SAAA,aAA8B;UAAAF,EAAA,CAAAI,YAAA,EAAI;UAC3DJ,EAAA,CAAAC,cAAA,aAA+C;UAAAD,EAAA,CAAAE,SAAA,aAAgC;UAAAF,EAAA,CAAAI,YAAA,EAAI;UACnFJ,EAAA,CAAAC,cAAA,aAA2C;UAAAD,EAAA,CAAAE,SAAA,aAA8B;UAAAF,EAAA,CAAAI,YAAA,EAAI;UAC7EJ,EAAA,CAAAC,cAAA,aAAyC;UAAAD,EAAA,CAAAE,SAAA,aAA+B;UAAAF,EAAA,CAAAI,YAAA,EAAI;UAGpFJ,EAAA,CAAAC,cAAA,eAAyB;UACjBD,EAAA,CAAAG,MAAA,eAAO;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAChBJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,gBAAQ;UAAAH,EAAA,CAAAI,YAAA,EAAI;UACfJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,kBAAU;UAAAH,EAAA,CAAAI,YAAA,EAAI;UACjBJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,kBAAU;UAAAH,EAAA,CAAAI,YAAA,EAAI;UACjBJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,eAAO;UAAAH,EAAA,CAAAI,YAAA,EAAI;UACdJ,EAAA,CAAAE,SAAA,SAAO;UACXF,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAC,cAAA,eAAwB;UAChBD,EAAA,CAAAG,MAAA,eAAO;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAChBJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,aAAK;UAAAH,EAAA,CAAAI,YAAA,EAAI;UACZJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,YAAI;UAAAH,EAAA,CAAAI,YAAA,EAAI;UACXJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,iBAAS;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAChBJ,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAG,MAAA,aAAK;UAAAH,EAAA,CAAAI,YAAA,EAAI;;;UAlEQJ,EAAA,CAAAK,SAAA,IAAsB;UAAtBL,EAAA,CAAAS,UAAA,SAAAyC,GAAA,CAAA7B,KAAA,CAAAJ,MAAA,KAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}