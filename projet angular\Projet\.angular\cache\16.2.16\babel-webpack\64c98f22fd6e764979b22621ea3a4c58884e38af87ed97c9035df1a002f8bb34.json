{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/forms\";\nfunction LoginComponent_option_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"option\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const gov_r1 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(gov_r1);\n  }\n}\nexport class LoginComponent {\n  constructor() {\n    this.step = 1; // Tracks the current visible field\n    this.username = '';\n    this.profilePhoto = '';\n    this.governorate = '';\n    this.birthday = '';\n    this.gender = '';\n    this.email = '';\n    this.phone = '';\n    this.password = '';\n    this.confirmPassword = '';\n    this.isOver18 = false;\n    this.agreedToTerms = false;\n    this.governorates = ['<PERSON>na', '<PERSON><PERSON><PERSON>', '<PERSON> Arous', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>usse', '<PERSON>taouine', 'Tozeur', '<PERSON>nis', '<PERSON>aghouan'];\n  }\n  // Advances to the next step when user interacts with an input\n  checkStep(currentStep) {\n    // Ensure step is advanced only when the input is filled\n    if (currentStep === 1 && this.username) {\n      this.step = 2;\n    } else if (currentStep === 2 && this.profilePhoto) {\n      this.step = 3;\n    } else if (currentStep === 3 && this.governorate) {\n      this.step = 4;\n    } else if (currentStep === 4 && this.birthday && this.gender) {\n      this.step = 5;\n    } else if (currentStep === 5 && this.email && this.phone) {\n      this.step = 6;\n    } else if (currentStep === 6 && this.password && this.confirmPassword) {\n      this.step = 7;\n    }\n  }\n  // Check if form is ready to be submitted\n  canSubmit() {\n    return this.step >= 6 && this.password === this.confirmPassword && this.isOver18 && this.agreedToTerms;\n  }\n  static {\n    this.ɵfac = function LoginComponent_Factory(t) {\n      return new (t || LoginComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LoginComponent,\n      selectors: [[\"app-login\"]],\n      decls: 70,\n      vars: 49,\n      consts: [[\"align\", \"center\", 1, \"register\"], [\"src\", \"assets/logo.jpg\", \"alt\", \"Logo\"], [\"align\", \"center\", 1, \"container\"], [\"type\", \"text\", \"name\", \"username\", 3, \"ngModel\", \"ngModelChange\", \"input\"], [3, \"hidden\", \"disabled\", \"change\"], [\"type\", \"file\", 3, \"hidden\", \"disabled\", \"change\"], [\"colspan\", \"2\", \"align\", \"center\"], [\"name\", \"governorate\", 3, \"ngModel\", \"hidden\", \"disabled\", \"ngModelChange\", \"change\"], [4, \"ngFor\", \"ngForOf\"], [3, \"hidden\", \"disabled\", \"input\"], [\"type\", \"date\", 3, \"ngModel\", \"hidden\", \"disabled\", \"ngModelChange\", \"input\"], [3, \"hidden\", \"disabled\"], [\"type\", \"radio\", \"name\", \"gender\", \"value\", \"Male\", 3, \"ngModel\", \"hidden\", \"disabled\", \"ngModelChange\"], [\"type\", \"radio\", \"name\", \"gender\", \"value\", \"Female\", 3, \"ngModel\", \"hidden\", \"disabled\", \"ngModelChange\"], [\"type\", \"email\", 3, \"ngModel\", \"hidden\", \"disabled\", \"ngModelChange\", \"input\"], [\"type\", \"tel\", 3, \"ngModel\", \"hidden\", \"disabled\", \"ngModelChange\", \"input\"], [\"type\", \"password\", \"name\", \"password\", 3, \"ngModel\", \"hidden\", \"disabled\", \"ngModelChange\", \"input\"], [\"type\", \"password\", \"name\", \"confirmPassword\", 3, \"ngModel\", \"hidden\", \"disabled\", \"ngModelChange\", \"input\"], [\"type\", \"checkbox\", 3, \"ngModel\", \"hidden\", \"disabled\", \"ngModelChange\"], [2, \"color\", \"red\"], [\"type\", \"submit\", 2, \"padding\", \"5px 45px\", 3, \"disabled\"], [\"type\", \"button\", 2, \"padding\", \"5px 45px\"]],\n      template: function LoginComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵelement(1, \"img\", 1);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(2, \"div\", 2)(3, \"fieldset\")(4, \"legend\");\n          i0.ɵɵtext(5, \"Register\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"form\")(7, \"table\")(8, \"tr\")(9, \"td\")(10, \"p\");\n          i0.ɵɵtext(11, \"Username\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"input\", 3);\n          i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_12_listener($event) {\n            return ctx.username = $event;\n          })(\"input\", function LoginComponent_Template_input_input_12_listener() {\n            return ctx.checkStep(1);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(13, \"tr\")(14, \"td\")(15, \"p\", 4);\n          i0.ɵɵlistener(\"change\", function LoginComponent_Template_p_change_15_listener() {\n            return ctx.checkStep(2);\n          });\n          i0.ɵɵtext(16, \"Profile photo\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"input\", 5);\n          i0.ɵɵlistener(\"change\", function LoginComponent_Template_input_change_17_listener() {\n            return ctx.checkStep(2);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(18, \"tr\")(19, \"td\", 6)(20, \"p\", 4);\n          i0.ɵɵlistener(\"change\", function LoginComponent_Template_p_change_20_listener() {\n            return ctx.checkStep(3);\n          });\n          i0.ɵɵtext(21, \"Governorate\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"select\", 7);\n          i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_select_ngModelChange_22_listener($event) {\n            return ctx.governorate = $event;\n          })(\"change\", function LoginComponent_Template_select_change_22_listener() {\n            return ctx.checkStep(3);\n          });\n          i0.ɵɵtemplate(23, LoginComponent_option_23_Template, 2, 1, \"option\", 8);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(24, \"tr\")(25, \"td\")(26, \"p\", 9);\n          i0.ɵɵlistener(\"input\", function LoginComponent_Template_p_input_26_listener() {\n            return ctx.checkStep(4);\n          });\n          i0.ɵɵtext(27, \"Birthday\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"input\", 10);\n          i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_28_listener($event) {\n            return ctx.birthday = $event;\n          })(\"input\", function LoginComponent_Template_input_input_28_listener() {\n            return ctx.checkStep(4);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(29, \"td\")(30, \"p\", 11);\n          i0.ɵɵtext(31, \"Gender\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"input\", 12);\n          i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_32_listener($event) {\n            return ctx.gender = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(33, \" Male \");\n          i0.ɵɵelementStart(34, \"input\", 13);\n          i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_34_listener($event) {\n            return ctx.gender = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(35, \" Female \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(36, \"tr\")(37, \"td\")(38, \"p\", 9);\n          i0.ɵɵlistener(\"input\", function LoginComponent_Template_p_input_38_listener() {\n            return ctx.checkStep(5);\n          });\n          i0.ɵɵtext(39, \"Mail account\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"input\", 14);\n          i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_40_listener($event) {\n            return ctx.email = $event;\n          })(\"input\", function LoginComponent_Template_input_input_40_listener() {\n            return ctx.checkStep(5);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(41, \"td\")(42, \"p\", 9);\n          i0.ɵɵlistener(\"input\", function LoginComponent_Template_p_input_42_listener() {\n            return ctx.checkStep(5);\n          });\n          i0.ɵɵtext(43, \"Phone number\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(44, \"input\", 15);\n          i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_44_listener($event) {\n            return ctx.phone = $event;\n          })(\"input\", function LoginComponent_Template_input_input_44_listener() {\n            return ctx.checkStep(5);\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(45, \"tr\")(46, \"td\")(47, \"p\", 9);\n          i0.ɵɵlistener(\"input\", function LoginComponent_Template_p_input_47_listener() {\n            return ctx.checkStep(6);\n          });\n          i0.ɵɵtext(48, \"Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"input\", 16);\n          i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_49_listener($event) {\n            return ctx.password = $event;\n          })(\"input\", function LoginComponent_Template_input_input_49_listener() {\n            return ctx.checkStep(6);\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(50, \"td\")(51, \"p\");\n          i0.ɵɵtext(52, \"Confirm Password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(53, \"input\", 17);\n          i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_53_listener($event) {\n            return ctx.confirmPassword = $event;\n          })(\"input\", function LoginComponent_Template_input_input_53_listener() {\n            return ctx.checkStep(6);\n          });\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(54, \"div\")(55, \"input\", 18);\n          i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_55_listener($event) {\n            return ctx.isOver18 = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(56, \" By creating an account, you confirm that you are 18 years or older. \");\n          i0.ɵɵelement(57, \"br\");\n          i0.ɵɵelementStart(58, \"input\", 18);\n          i0.ɵɵlistener(\"ngModelChange\", function LoginComponent_Template_input_ngModelChange_58_listener($event) {\n            return ctx.agreedToTerms = $event;\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(59, \" By creating an account, you agree to \");\n          i0.ɵɵelementStart(60, \"span\", 19);\n          i0.ɵɵtext(61, \"Conditions of Use\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(62, \" and \");\n          i0.ɵɵelementStart(63, \"span\", 19);\n          i0.ɵɵtext(64, \"Privacy Notice\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(65, \"br\");\n          i0.ɵɵelementStart(66, \"button\", 20);\n          i0.ɵɵtext(67, \"Submit\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(68, \"button\", 21);\n          i0.ɵɵtext(69, \"Reset\");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"ngModel\", ctx.username);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"hidden\", !ctx.username)(\"disabled\", !ctx.username);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"hidden\", !ctx.username)(\"disabled\", !ctx.username);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"hidden\", !ctx.profilePhoto)(\"disabled\", !ctx.profilePhoto);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngModel\", ctx.governorate)(\"hidden\", !ctx.profilePhoto)(\"disabled\", !ctx.profilePhoto);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.governorates);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"hidden\", !ctx.governorate)(\"disabled\", !ctx.governorate);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngModel\", ctx.birthday)(\"hidden\", !ctx.governorate)(\"disabled\", !ctx.governorate);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"hidden\", !ctx.birthday)(\"disabled\", !ctx.birthday);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngModel\", ctx.gender)(\"hidden\", !ctx.birthday)(\"disabled\", !ctx.birthday);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngModel\", ctx.gender)(\"hidden\", !ctx.birthday)(\"disabled\", !ctx.birthday);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"hidden\", !ctx.gender)(\"disabled\", !ctx.gender);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngModel\", ctx.email)(\"hidden\", !ctx.gender)(\"disabled\", !ctx.gender);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"hidden\", !ctx.email)(\"disabled\", !ctx.email);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngModel\", ctx.phone)(\"hidden\", !ctx.email)(\"disabled\", !ctx.email);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"hidden\", !ctx.phone)(\"disabled\", !ctx.phone);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngModel\", ctx.password)(\"hidden\", !ctx.phone)(\"disabled\", !ctx.phone);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.confirmPassword)(\"hidden\", !ctx.password)(\"disabled\", !ctx.password);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngModel\", ctx.isOver18)(\"hidden\", !ctx.confirmPassword)(\"disabled\", !ctx.confirmPassword);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngModel\", ctx.agreedToTerms)(\"hidden\", !ctx.isOver18)(\"disabled\", !ctx.isOver18);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"disabled\", !ctx.canSubmit());\n        }\n      },\n      dependencies: [i1.NgForOf, i2.ɵNgNoValidate, i2.NgSelectOption, i2.ɵNgSelectMultipleOption, i2.DefaultValueAccessor, i2.CheckboxControlValueAccessor, i2.SelectControlValueAccessor, i2.RadioControlValueAccessor, i2.NgControlStatus, i2.NgControlStatusGroup, i2.NgModel, i2.NgForm],\n      styles: [\".register[_ngcontent-%COMP%]{\\n        padding-top: 80px;\\n}\\nimg[_ngcontent-%COMP%]{\\n    width: 150px;\\n    height: 150px;\\n} \\nlegend[_ngcontent-%COMP%]{\\n    font-size:50px;\\n    font-family: 'Trebuchet MS'; \\n    font-style: italic;\\n    font-weight:bold; \\n    color: rgb(22, 21, 21);\\n}\\np[_ngcontent-%COMP%]{\\n    font-size:15px;\\n    font-family: 'Lucida Sans Unicode';\\n    font-style: italic;  \\n    color: rgb(22, 21, 21);\\n    \\n}\\nselect[_ngcontent-%COMP%]{\\n    padding:1px 45px;\\n}\\n    \\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9sb2dpbi9sb2dpbi5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO1FBQ1EsaUJBQWlCO0FBQ3pCO0FBQ0E7SUFDSSxZQUFZO0lBQ1osYUFBYTtBQUNqQjtBQUNBO0lBQ0ksY0FBYztJQUNkLDJCQUEyQjtJQUMzQixrQkFBa0I7SUFDbEIsZ0JBQWdCO0lBQ2hCLHNCQUFzQjtBQUMxQjtBQUNBO0lBQ0ksY0FBYztJQUNkLGtDQUFrQztJQUNsQyxrQkFBa0I7SUFDbEIsc0JBQXNCOztBQUUxQjtBQUNBO0lBQ0ksZ0JBQWdCO0FBQ3BCIiwic291cmNlc0NvbnRlbnQiOlsiLnJlZ2lzdGVye1xyXG4gICAgICAgIHBhZGRpbmctdG9wOiA4MHB4O1xyXG59XHJcbmltZ3tcclxuICAgIHdpZHRoOiAxNTBweDtcclxuICAgIGhlaWdodDogMTUwcHg7XHJcbn0gXHJcbmxlZ2VuZHtcclxuICAgIGZvbnQtc2l6ZTo1MHB4O1xyXG4gICAgZm9udC1mYW1pbHk6ICdUcmVidWNoZXQgTVMnOyBcclxuICAgIGZvbnQtc3R5bGU6IGl0YWxpYztcclxuICAgIGZvbnQtd2VpZ2h0OmJvbGQ7IFxyXG4gICAgY29sb3I6IHJnYigyMiwgMjEsIDIxKTtcclxufVxyXG5we1xyXG4gICAgZm9udC1zaXplOjE1cHg7XHJcbiAgICBmb250LWZhbWlseTogJ0x1Y2lkYSBTYW5zIFVuaWNvZGUnO1xyXG4gICAgZm9udC1zdHlsZTogaXRhbGljOyAgXHJcbiAgICBjb2xvcjogcmdiKDIyLCAyMSwgMjEpO1xyXG4gICAgXHJcbn1cclxuc2VsZWN0e1xyXG4gICAgcGFkZGluZzoxcHggNDVweDtcclxufVxyXG4gICAgIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "gov_r1", "LoginComponent", "constructor", "step", "username", "profilePhoto", "governorate", "birthday", "gender", "email", "phone", "password", "confirmPassword", "isOver18", "agreedToTerms", "governorates", "checkStep", "currentStep", "canSubmit", "selectors", "decls", "vars", "consts", "template", "LoginComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵlistener", "LoginComponent_Template_input_ngModelChange_12_listener", "$event", "LoginComponent_Template_input_input_12_listener", "LoginComponent_Template_p_change_15_listener", "LoginComponent_Template_input_change_17_listener", "LoginComponent_Template_p_change_20_listener", "LoginComponent_Template_select_ngModelChange_22_listener", "LoginComponent_Template_select_change_22_listener", "ɵɵtemplate", "LoginComponent_option_23_Template", "LoginComponent_Template_p_input_26_listener", "LoginComponent_Template_input_ngModelChange_28_listener", "LoginComponent_Template_input_input_28_listener", "LoginComponent_Template_input_ngModelChange_32_listener", "LoginComponent_Template_input_ngModelChange_34_listener", "LoginComponent_Template_p_input_38_listener", "LoginComponent_Template_input_ngModelChange_40_listener", "LoginComponent_Template_input_input_40_listener", "LoginComponent_Template_p_input_42_listener", "LoginComponent_Template_input_ngModelChange_44_listener", "LoginComponent_Template_input_input_44_listener", "LoginComponent_Template_p_input_47_listener", "LoginComponent_Template_input_ngModelChange_49_listener", "LoginComponent_Template_input_input_49_listener", "LoginComponent_Template_input_ngModelChange_53_listener", "LoginComponent_Template_input_input_53_listener", "LoginComponent_Template_input_ngModelChange_55_listener", "LoginComponent_Template_input_ngModelChange_58_listener", "ɵɵproperty"], "sources": ["C:\\Users\\<USER>\\Desktop\\projet angular\\Projet\\src\\login\\login.component.ts", "C:\\Users\\<USER>\\Desktop\\projet angular\\Projet\\src\\login\\login.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-login',\n  templateUrl: './login.component.html',\n  styleUrls: ['./login.component.css']\n})\nexport class LoginComponent {\n  step: number = 1; // Tracks the current visible field\n  username: string = '';\n  profilePhoto: string = '';\n  governorate: string = '';\n  birthday: string = '';\n  gender: string = '';\n  email: string = '';\n  phone: string = '';\n  password: string = '';\n  confirmPassword: string = '';\n  isOver18: boolean = false;\n  agreedToTerms: boolean = false;\n\n  governorates: string[] = [\n    '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>',\n    '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>',\n    '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>',\n    '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>',\n  ];\n\n  // Advances to the next step when user interacts with an input\n  checkStep(currentStep: number): void {\n    // Ensure step is advanced only when the input is filled\n    if (currentStep === 1 && this.username) {\n      this.step = 2;\n    } else if (currentStep === 2 && this.profilePhoto) {\n      this.step = 3;\n    } else if (currentStep === 3 && this.governorate) {\n      this.step = 4;\n    } else if (currentStep === 4 && this.birthday && this.gender) {\n      this.step = 5;\n    } else if (currentStep === 5 && this.email && this.phone) {\n      this.step = 6;\n    } else if (currentStep === 6 && this.password && this.confirmPassword) {\n      this.step = 7;\n    }\n  }\n\n  // Check if form is ready to be submitted\n  canSubmit(): boolean {\n    return (\n      this.step >= 6 &&\n      this.password === this.confirmPassword &&\n      this.isOver18 &&\n      this.agreedToTerms\n    );\n}\n}\n", "<div class=\"register\" align=\"center\">\n    <img src=\"assets/logo.jpg\" alt=\"Logo\" />\n</div>\n<div class=\"container\" align=\"center\">\n    <fieldset>\n        <legend>Register</legend>\n        <form>\n            <table>\n                <!-- Username Field -->\n                <tr>\n                    <td>\n                        <p>Username</p>\n                        <input type=\"text\" name=\"username\" [(ngModel)]=\"username\" (input)=\"checkStep(1)\" />\n                    </td>\n                </tr>\n\n                <!-- Profile Photo Field -->\n                <tr>\n                    <td>\n                        <p [hidden]=\"!username\" [disabled]=\"!username\" (change)=\"checkStep(2)\">Profile photo</p>\n                        <input type=\"file\" [hidden]=\"!username\" [disabled]=\"!username\" (change)=\"checkStep(2)\" />\n                    </td>\n                </tr>\n\n                <!-- Governorate Field -->\n                <tr>\n                    <td colspan=\"2\" align=\"center\">\n                        <p [hidden]=\"!profilePhoto\" [disabled]=\"!profilePhoto\" (change)=\"checkStep(3)\">Governorate</p>\n                        <select name=\"governorate\" [(ngModel)]=\"governorate\" [hidden]=\"!profilePhoto\" [disabled]=\"!profilePhoto\" (change)=\"checkStep(3)\">\n                            <option *ngFor=\"let gov of governorates\">{{ gov }}</option>\n                        </select>\n                    </td>\n                </tr>\n\n                <!-- Birthday and Gender Fields -->\n                <tr>\n                    <td>\n                        <p [hidden]=\"!governorate\" [disabled]=\"!governorate\" (input)=\"checkStep(4)\">Birthday</p>\n                        <input type=\"date\" [(ngModel)]=\"birthday\" [hidden]=\"!governorate\" [disabled]=\"!governorate\" (input)=\"checkStep(4)\" />\n                    </td>\n                    <td>\n                        <p [hidden]=\"!birthday\" [disabled]=\"!birthday\">Gender</p>\n                        <input type=\"radio\" name=\"gender\" [(ngModel)]=\"gender\" value=\"Male\" [hidden]=\"!birthday\" [disabled]=\"!birthday\" /> Male\n                        <input type=\"radio\" name=\"gender\" [(ngModel)]=\"gender\" value=\"Female\" [hidden]=\"!birthday\" [disabled]=\"!birthday\" /> Female\n                    </td>\n                </tr>\n\n                <!-- Email and Phone Fields -->\n                <tr>\n                    <td>\n                        <p [hidden]=\"!gender\" [disabled]=\"!gender\" (input)=\"checkStep(5)\">Mail account</p>\n                        <input type=\"email\" [(ngModel)]=\"email\" [hidden]=\"!gender\" [disabled]=\"!gender\" (input)=\"checkStep(5)\" />\n                    </td>\n                    <td>\n                        <p [hidden]=\"!email\" [disabled]=\"!email\" (input)=\"checkStep(5)\">Phone number</p>\n                        <input type=\"tel\" [(ngModel)]=\"phone\" [hidden]=\"!email\" [disabled]=\"!email\" (input)=\"checkStep(5)\" />\n                    </td>\n                </tr>\n\n                <!-- Password Fields -->\n                <tr>\n                    <td>\n                        <p [hidden]=\"!phone\" [disabled]=\"!phone\" (input)=\"checkStep(6)\">Password</p>\n                        <input type=\"password\" name=\"password\" [(ngModel)]=\"password\" [hidden]=\"!phone\" [disabled]=\"!phone\" (input)=\"checkStep(6)\" />\n                    </td>\n                    <td>\n                        <p>Confirm Password</p>\n                        <input type=\"password\" name=\"confirmPassword\" [(ngModel)]=\"confirmPassword\" [hidden]=\"!password\" [disabled]=\"!password\" (input)=\"checkStep(6)\" />\n                    </td>\n                </tr>\n            </table>\n\n            <!-- Terms and Submit -->\n            <div>\n                <input type=\"checkbox\" [(ngModel)]=\"isOver18\" [hidden]=\"!confirmPassword\" [disabled]=\"!confirmPassword\" /> By creating an account, you confirm that you are 18 years or older. <br>\n                <input type=\"checkbox\" [(ngModel)]=\"agreedToTerms\" [hidden]=\"!isOver18\" [disabled]=\"!isOver18\" /> By creating an account, you agree to <span style=\"color: red;\">Conditions of Use</span> and <span style=\"color: red;\">Privacy Notice</span>\n                <br>\n                <button type=\"submit\" style=\"padding: 5px 45px;\" [disabled]=\"!canSubmit()\">Submit</button>\n                <button type=\"button\" style=\"padding: 5px 45px;\">Reset</button>\n            </div>\n\n        </form>\n    </fieldset>\n</div>\n"], "mappings": ";;;;;IC6B4BA,EAAA,CAAAC,cAAA,aAAyC;IAAAD,EAAA,CAAAE,MAAA,GAAS;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAAlBH,EAAA,CAAAI,SAAA,GAAS;IAATJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAS;;;ADtB9E,OAAM,MAAOC,cAAc;EAL3BC,YAAA;IAME,KAAAC,IAAI,GAAW,CAAC,CAAC,CAAC;IAClB,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAC,YAAY,GAAW,EAAE;IACzB,KAAAC,WAAW,GAAW,EAAE;IACxB,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAC,MAAM,GAAW,EAAE;IACnB,KAAAC,KAAK,GAAW,EAAE;IAClB,KAAAC,KAAK,GAAW,EAAE;IAClB,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAC,eAAe,GAAW,EAAE;IAC5B,KAAAC,QAAQ,GAAY,KAAK;IACzB,KAAAC,aAAa,GAAY,KAAK;IAE9B,KAAAC,YAAY,GAAa,CACvB,QAAQ,EAAE,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,UAAU,EACtE,UAAU,EAAE,WAAW,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,EACzE,UAAU,EAAE,QAAQ,EAAE,MAAM,EAAE,aAAa,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,EAC7E,QAAQ,EAAE,OAAO,EAAE,UAAU,CAC9B;;EAED;EACAC,SAASA,CAACC,WAAmB;IAC3B;IACA,IAAIA,WAAW,KAAK,CAAC,IAAI,IAAI,CAACb,QAAQ,EAAE;MACtC,IAAI,CAACD,IAAI,GAAG,CAAC;KACd,MAAM,IAAIc,WAAW,KAAK,CAAC,IAAI,IAAI,CAACZ,YAAY,EAAE;MACjD,IAAI,CAACF,IAAI,GAAG,CAAC;KACd,MAAM,IAAIc,WAAW,KAAK,CAAC,IAAI,IAAI,CAACX,WAAW,EAAE;MAChD,IAAI,CAACH,IAAI,GAAG,CAAC;KACd,MAAM,IAAIc,WAAW,KAAK,CAAC,IAAI,IAAI,CAACV,QAAQ,IAAI,IAAI,CAACC,MAAM,EAAE;MAC5D,IAAI,CAACL,IAAI,GAAG,CAAC;KACd,MAAM,IAAIc,WAAW,KAAK,CAAC,IAAI,IAAI,CAACR,KAAK,IAAI,IAAI,CAACC,KAAK,EAAE;MACxD,IAAI,CAACP,IAAI,GAAG,CAAC;KACd,MAAM,IAAIc,WAAW,KAAK,CAAC,IAAI,IAAI,CAACN,QAAQ,IAAI,IAAI,CAACC,eAAe,EAAE;MACrE,IAAI,CAACT,IAAI,GAAG,CAAC;;EAEjB;EAEA;EACAe,SAASA,CAAA;IACP,OACE,IAAI,CAACf,IAAI,IAAI,CAAC,IACd,IAAI,CAACQ,QAAQ,KAAK,IAAI,CAACC,eAAe,IACtC,IAAI,CAACC,QAAQ,IACb,IAAI,CAACC,aAAa;EAExB;;;uBA/Cab,cAAc;IAAA;EAAA;;;YAAdA,cAAc;MAAAkB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCP3B/B,EAAA,CAAAC,cAAA,aAAqC;UACjCD,EAAA,CAAAiC,SAAA,aAAwC;UAC5CjC,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,aAAsC;UAEtBD,EAAA,CAAAE,MAAA,eAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACzBH,EAAA,CAAAC,cAAA,WAAM;UAKaD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACfH,EAAA,CAAAC,cAAA,gBAAmF;UAAhDD,EAAA,CAAAkC,UAAA,2BAAAC,wDAAAC,MAAA;YAAA,OAAAJ,GAAA,CAAAtB,QAAA,GAAA0B,MAAA;UAAA,EAAsB,mBAAAC,gDAAA;YAAA,OAAUL,GAAA,CAAAV,SAAA,CAAU,CAAC,CAAC;UAAA,EAAtB;UAAzDtB,EAAA,CAAAG,YAAA,EAAmF;UAK3FH,EAAA,CAAAC,cAAA,UAAI;UAEmDD,EAAA,CAAAkC,UAAA,oBAAAI,6CAAA;YAAA,OAAUN,GAAA,CAAAV,SAAA,CAAU,CAAC,CAAC;UAAA,EAAC;UAACtB,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACxFH,EAAA,CAAAC,cAAA,gBAAyF;UAA1BD,EAAA,CAAAkC,UAAA,oBAAAK,iDAAA;YAAA,OAAUP,GAAA,CAAAV,SAAA,CAAU,CAAC,CAAC;UAAA,EAAC;UAAtFtB,EAAA,CAAAG,YAAA,EAAyF;UAKjGH,EAAA,CAAAC,cAAA,UAAI;UAE2DD,EAAA,CAAAkC,UAAA,oBAAAM,6CAAA;YAAA,OAAUR,GAAA,CAAAV,SAAA,CAAU,CAAC,CAAC;UAAA,EAAC;UAACtB,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC9FH,EAAA,CAAAC,cAAA,iBAAiI;UAAtGD,EAAA,CAAAkC,UAAA,2BAAAO,yDAAAL,MAAA;YAAA,OAAAJ,GAAA,CAAApB,WAAA,GAAAwB,MAAA;UAAA,EAAyB,oBAAAM,kDAAA;YAAA,OAA+DV,GAAA,CAAAV,SAAA,CAAU,CAAC,CAAC;UAAA,EAA3E;UAChDtB,EAAA,CAAA2C,UAAA,KAAAC,iCAAA,oBAA2D;UAC/D5C,EAAA,CAAAG,YAAA,EAAS;UAKjBH,EAAA,CAAAC,cAAA,UAAI;UAEyDD,EAAA,CAAAkC,UAAA,mBAAAW,4CAAA;YAAA,OAASb,GAAA,CAAAV,SAAA,CAAU,CAAC,CAAC;UAAA,EAAC;UAACtB,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACxFH,EAAA,CAAAC,cAAA,iBAAqH;UAAlGD,EAAA,CAAAkC,UAAA,2BAAAY,wDAAAV,MAAA;YAAA,OAAAJ,GAAA,CAAAnB,QAAA,GAAAuB,MAAA;UAAA,EAAsB,mBAAAW,gDAAA;YAAA,OAA4Df,GAAA,CAAAV,SAAA,CAAU,CAAC,CAAC;UAAA,EAAxE;UAAzCtB,EAAA,CAAAG,YAAA,EAAqH;UAEzHH,EAAA,CAAAC,cAAA,UAAI;UAC+CD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACzDH,EAAA,CAAAC,cAAA,iBAAkH;UAAhFD,EAAA,CAAAkC,UAAA,2BAAAc,wDAAAZ,MAAA;YAAA,OAAAJ,GAAA,CAAAlB,MAAA,GAAAsB,MAAA;UAAA,EAAoB;UAAtDpC,EAAA,CAAAG,YAAA,EAAkH;UAACH,EAAA,CAAAE,MAAA,cACnH;UAAAF,EAAA,CAAAC,cAAA,iBAAoH;UAAlFD,EAAA,CAAAkC,UAAA,2BAAAe,wDAAAb,MAAA;YAAA,OAAAJ,GAAA,CAAAlB,MAAA,GAAAsB,MAAA;UAAA,EAAoB;UAAtDpC,EAAA,CAAAG,YAAA,EAAoH;UAACH,EAAA,CAAAE,MAAA,gBACzH;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAITH,EAAA,CAAAC,cAAA,UAAI;UAE+CD,EAAA,CAAAkC,UAAA,mBAAAgB,4CAAA;YAAA,OAASlB,GAAA,CAAAV,SAAA,CAAU,CAAC,CAAC;UAAA,EAAC;UAACtB,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAClFH,EAAA,CAAAC,cAAA,iBAAyG;UAArFD,EAAA,CAAAkC,UAAA,2BAAAiB,wDAAAf,MAAA;YAAA,OAAAJ,GAAA,CAAAjB,KAAA,GAAAqB,MAAA;UAAA,EAAmB,mBAAAgB,gDAAA;YAAA,OAAkDpB,GAAA,CAAAV,SAAA,CAAU,CAAC,CAAC;UAAA,EAA9D;UAAvCtB,EAAA,CAAAG,YAAA,EAAyG;UAE7GH,EAAA,CAAAC,cAAA,UAAI;UACyCD,EAAA,CAAAkC,UAAA,mBAAAmB,4CAAA;YAAA,OAASrB,GAAA,CAAAV,SAAA,CAAU,CAAC,CAAC;UAAA,EAAC;UAACtB,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAChFH,EAAA,CAAAC,cAAA,iBAAqG;UAAnFD,EAAA,CAAAkC,UAAA,2BAAAoB,wDAAAlB,MAAA;YAAA,OAAAJ,GAAA,CAAAhB,KAAA,GAAAoB,MAAA;UAAA,EAAmB,mBAAAmB,gDAAA;YAAA,OAAgDvB,GAAA,CAAAV,SAAA,CAAU,CAAC,CAAC;UAAA,EAA5D;UAArCtB,EAAA,CAAAG,YAAA,EAAqG;UAK7GH,EAAA,CAAAC,cAAA,UAAI;UAE6CD,EAAA,CAAAkC,UAAA,mBAAAsB,4CAAA;YAAA,OAASxB,GAAA,CAAAV,SAAA,CAAU,CAAC,CAAC;UAAA,EAAC;UAACtB,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAC5EH,EAAA,CAAAC,cAAA,iBAA6H;UAAtFD,EAAA,CAAAkC,UAAA,2BAAAuB,wDAAArB,MAAA;YAAA,OAAAJ,GAAA,CAAAf,QAAA,GAAAmB,MAAA;UAAA,EAAsB,mBAAAsB,gDAAA;YAAA,OAAgD1B,GAAA,CAAAV,SAAA,CAAU,CAAC,CAAC;UAAA,EAA5D;UAA7DtB,EAAA,CAAAG,YAAA,EAA6H;UAEjIH,EAAA,CAAAC,cAAA,UAAI;UACGD,EAAA,CAAAE,MAAA,wBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACvBH,EAAA,CAAAC,cAAA,iBAAiJ;UAAnGD,EAAA,CAAAkC,UAAA,2BAAAyB,wDAAAvB,MAAA;YAAA,OAAAJ,GAAA,CAAAd,eAAA,GAAAkB,MAAA;UAAA,EAA6B,mBAAAwB,gDAAA;YAAA,OAAsD5B,GAAA,CAAAV,SAAA,CAAU,CAAC,CAAC;UAAA,EAAlE;UAA3EtB,EAAA,CAAAG,YAAA,EAAiJ;UAM7JH,EAAA,CAAAC,cAAA,WAAK;UACsBD,EAAA,CAAAkC,UAAA,2BAAA2B,wDAAAzB,MAAA;YAAA,OAAAJ,GAAA,CAAAb,QAAA,GAAAiB,MAAA;UAAA,EAAsB;UAA7CpC,EAAA,CAAAG,YAAA,EAA0G;UAACH,EAAA,CAAAE,MAAA,6EAAoE;UAAAF,EAAA,CAAAiC,SAAA,UAAI;UACnLjC,EAAA,CAAAC,cAAA,iBAAiG;UAA1ED,EAAA,CAAAkC,UAAA,2BAAA4B,wDAAA1B,MAAA;YAAA,OAAAJ,GAAA,CAAAZ,aAAA,GAAAgB,MAAA;UAAA,EAA2B;UAAlDpC,EAAA,CAAAG,YAAA,EAAiG;UAACH,EAAA,CAAAE,MAAA,8CAAqC;UAAAF,EAAA,CAAAC,cAAA,gBAA0B;UAAAD,EAAA,CAAAE,MAAA,yBAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAACH,EAAA,CAAAE,MAAA,aAAI;UAAAF,EAAA,CAAAC,cAAA,gBAA0B;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC7OH,EAAA,CAAAiC,SAAA,UAAI;UACJjC,EAAA,CAAAC,cAAA,kBAA2E;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAC1FH,EAAA,CAAAC,cAAA,kBAAiD;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAS;;;UAlEpBH,EAAA,CAAAI,SAAA,IAAsB;UAAtBJ,EAAA,CAAA+D,UAAA,YAAA/B,GAAA,CAAAtB,QAAA,CAAsB;UAOtDV,EAAA,CAAAI,SAAA,GAAoB;UAApBJ,EAAA,CAAA+D,UAAA,YAAA/B,GAAA,CAAAtB,QAAA,CAAoB,cAAAsB,GAAA,CAAAtB,QAAA;UACJV,EAAA,CAAAI,SAAA,GAAoB;UAApBJ,EAAA,CAAA+D,UAAA,YAAA/B,GAAA,CAAAtB,QAAA,CAAoB,cAAAsB,GAAA,CAAAtB,QAAA;UAOpCV,EAAA,CAAAI,SAAA,GAAwB;UAAxBJ,EAAA,CAAA+D,UAAA,YAAA/B,GAAA,CAAArB,YAAA,CAAwB,cAAAqB,GAAA,CAAArB,YAAA;UACAX,EAAA,CAAAI,SAAA,GAAyB;UAAzBJ,EAAA,CAAA+D,UAAA,YAAA/B,GAAA,CAAApB,WAAA,CAAyB,YAAAoB,GAAA,CAAArB,YAAA,eAAAqB,GAAA,CAAArB,YAAA;UACxBX,EAAA,CAAAI,SAAA,GAAe;UAAfJ,EAAA,CAAA+D,UAAA,YAAA/B,GAAA,CAAAX,YAAA,CAAe;UAQxCrB,EAAA,CAAAI,SAAA,GAAuB;UAAvBJ,EAAA,CAAA+D,UAAA,YAAA/B,GAAA,CAAApB,WAAA,CAAuB,cAAAoB,GAAA,CAAApB,WAAA;UACPZ,EAAA,CAAAI,SAAA,GAAsB;UAAtBJ,EAAA,CAAA+D,UAAA,YAAA/B,GAAA,CAAAnB,QAAA,CAAsB,YAAAmB,GAAA,CAAApB,WAAA,eAAAoB,GAAA,CAAApB,WAAA;UAGtCZ,EAAA,CAAAI,SAAA,GAAoB;UAApBJ,EAAA,CAAA+D,UAAA,YAAA/B,GAAA,CAAAnB,QAAA,CAAoB,cAAAmB,GAAA,CAAAnB,QAAA;UACWb,EAAA,CAAAI,SAAA,GAAoB;UAApBJ,EAAA,CAAA+D,UAAA,YAAA/B,GAAA,CAAAlB,MAAA,CAAoB,YAAAkB,GAAA,CAAAnB,QAAA,eAAAmB,GAAA,CAAAnB,QAAA;UACpBb,EAAA,CAAAI,SAAA,GAAoB;UAApBJ,EAAA,CAAA+D,UAAA,YAAA/B,GAAA,CAAAlB,MAAA,CAAoB,YAAAkB,GAAA,CAAAnB,QAAA,eAAAmB,GAAA,CAAAnB,QAAA;UAOnDb,EAAA,CAAAI,SAAA,GAAkB;UAAlBJ,EAAA,CAAA+D,UAAA,YAAA/B,GAAA,CAAAlB,MAAA,CAAkB,cAAAkB,GAAA,CAAAlB,MAAA;UACDd,EAAA,CAAAI,SAAA,GAAmB;UAAnBJ,EAAA,CAAA+D,UAAA,YAAA/B,GAAA,CAAAjB,KAAA,CAAmB,YAAAiB,GAAA,CAAAlB,MAAA,eAAAkB,GAAA,CAAAlB,MAAA;UAGpCd,EAAA,CAAAI,SAAA,GAAiB;UAAjBJ,EAAA,CAAA+D,UAAA,YAAA/B,GAAA,CAAAjB,KAAA,CAAiB,cAAAiB,GAAA,CAAAjB,KAAA;UACFf,EAAA,CAAAI,SAAA,GAAmB;UAAnBJ,EAAA,CAAA+D,UAAA,YAAA/B,GAAA,CAAAhB,KAAA,CAAmB,YAAAgB,GAAA,CAAAjB,KAAA,eAAAiB,GAAA,CAAAjB,KAAA;UAOlCf,EAAA,CAAAI,SAAA,GAAiB;UAAjBJ,EAAA,CAAA+D,UAAA,YAAA/B,GAAA,CAAAhB,KAAA,CAAiB,cAAAgB,GAAA,CAAAhB,KAAA;UACmBhB,EAAA,CAAAI,SAAA,GAAsB;UAAtBJ,EAAA,CAAA+D,UAAA,YAAA/B,GAAA,CAAAf,QAAA,CAAsB,YAAAe,GAAA,CAAAhB,KAAA,eAAAgB,GAAA,CAAAhB,KAAA;UAIfhB,EAAA,CAAAI,SAAA,GAA6B;UAA7BJ,EAAA,CAAA+D,UAAA,YAAA/B,GAAA,CAAAd,eAAA,CAA6B,YAAAc,GAAA,CAAAf,QAAA,eAAAe,GAAA,CAAAf,QAAA;UAO5DjB,EAAA,CAAAI,SAAA,GAAsB;UAAtBJ,EAAA,CAAA+D,UAAA,YAAA/B,GAAA,CAAAb,QAAA,CAAsB,YAAAa,GAAA,CAAAd,eAAA,eAAAc,GAAA,CAAAd,eAAA;UACtBlB,EAAA,CAAAI,SAAA,GAA2B;UAA3BJ,EAAA,CAAA+D,UAAA,YAAA/B,GAAA,CAAAZ,aAAA,CAA2B,YAAAY,GAAA,CAAAb,QAAA,eAAAa,GAAA,CAAAb,QAAA;UAEDnB,EAAA,CAAAI,SAAA,GAAyB;UAAzBJ,EAAA,CAAA+D,UAAA,cAAA/B,GAAA,CAAAR,SAAA,GAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}