{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/forms\";\nfunction ShopComponent_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"div\", 15)(2, \"div\", 16);\n    i0.ɵɵelement(3, \"img\", 17);\n    i0.ɵɵelementStart(4, \"div\", 18)(5, \"h4\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 19)(10, \"button\", 20);\n    i0.ɵɵtext(11, \"Add to Cart\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\");\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const item_r2 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate(\"alt\", item_r2.imgSrc);\n    i0.ɵɵproperty(\"src\", item_r2.imgSrc, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r2.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r2.description);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", item_r2.price, \" DT\");\n  }\n}\nfunction ShopComponent_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"p\");\n    i0.ɵɵtext(2, \"No items available for the selected category.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class ShopComponent {\n  constructor() {\n    this.selectedCategory = '';\n    this.searchQuery = '';\n    this.items = [{\n      id: 1,\n      title: 'Item 1',\n      description: 'Description 1',\n      price: 50,\n      category: '1',\n      imgSrc: 'assets/4.jpg'\n    }, {\n      id: 2,\n      title: 'Item 2',\n      description: 'Description 2',\n      price: 60,\n      category: '2',\n      imgSrc: 'assets/2.jpg'\n    }, {\n      id: 3,\n      title: 'Item 3',\n      description: 'Description 3',\n      price: 70,\n      category: '1',\n      imgSrc: 'assets/7.jpg'\n    }, {\n      id: 4,\n      title: 'Item 4',\n      description: 'Description 4',\n      price: 80,\n      category: '3',\n      imgSrc: 'assets/8.jpg'\n    }];\n  }\n  getFilteredItems() {\n    const query = this.searchQuery.toLowerCase(); // Search query (case-insensitive)\n    return this.items.filter(item => {\n      const matchesCategory = !this.selectedCategory || item.category === this.selectedCategory;\n      const matchesSearch = item.title.toLowerCase().includes(query) || item.description.toLowerCase().includes(query);\n      return matchesCategory && matchesSearch;\n    });\n  }\n  static {\n    this.ɵfac = function ShopComponent_Factory(t) {\n      return new (t || ShopComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ShopComponent,\n      selectors: [[\"app-shop\"]],\n      decls: 21,\n      vars: 4,\n      consts: [[1, \"box\", \"mt-5\"], [1, \"my-2\", \"w-25\"], [\"for\", \"categories\", 1, \"my-1\"], [\"id\", \"categories\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"value\", \"\"], [\"value\", \"1\"], [\"value\", \"2\"], [\"value\", \"3\"], [\"value\", \"4\"], [1, \"my-2\", \"w-50\"], [\"for\", \"search\", 1, \"my-1\"], [\"id\", \"search\", \"type\", \"text\", \"placeholder\", \"Search by title...\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"class\", \"row\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"text-center mt-4\", 4, \"ngIf\"], [1, \"row\"], [1, \"col-md-3\", \"col-sm-12\"], [1, \"item\", \"d-flex\"], [3, \"src\", \"alt\"], [1, \"body\"], [1, \"px-3\", \"mb-2\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"btn\", \"btn-success\"], [1, \"text-center\", \"mt-4\"]],\n      template: function ShopComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"label\", 2);\n          i0.ɵɵtext(3, \"Categories\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"select\", 3);\n          i0.ɵɵlistener(\"ngModelChange\", function ShopComponent_Template_select_ngModelChange_4_listener($event) {\n            return ctx.selectedCategory = $event;\n          });\n          i0.ɵɵelementStart(5, \"option\", 4);\n          i0.ɵɵtext(6, \"All Categories\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"option\", 5);\n          i0.ɵɵtext(8, \"Category 1\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"option\", 6);\n          i0.ɵɵtext(10, \"Category 2\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"option\", 7);\n          i0.ɵɵtext(12, \"Category 3\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"option\", 8);\n          i0.ɵɵtext(14, \"Category 4\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(15, \"div\", 9)(16, \"label\", 10);\n          i0.ɵɵtext(17, \"Search Items\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"input\", 11);\n          i0.ɵɵlistener(\"ngModelChange\", function ShopComponent_Template_input_ngModelChange_18_listener($event) {\n            return ctx.searchQuery = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(19, ShopComponent_div_19_Template, 14, 5, \"div\", 12);\n          i0.ɵɵtemplate(20, ShopComponent_div_20_Template, 3, 0, \"div\", 13);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.selectedCategory);\n          i0.ɵɵadvance(14);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchQuery);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.getFilteredItems());\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.getFilteredItems().length === 0);\n        }\n      },\n      dependencies: [i1.NgForOf, i1.NgIf, i2.NgSelectOption, i2.ɵNgSelectMultipleOption, i2.DefaultValueAccessor, i2.SelectControlValueAccessor, i2.NgControlStatus, i2.NgModel],\n      styles: [\"body[_ngcontent-%COMP%]{margin-top: 100px;}\\n\\n\\n\\n.item[_ngcontent-%COMP%] {\\n    border-radius: 10px; \\n\\n    display: flex;\\n    margin: 15px;\\n    width: 100%;\\n    text-decoration: none;\\n    color: #333;\\n    gap:20px;\\n    align-items: flex-start;\\n    box-shadow: 0px 2px 5px rgba(222, 212, 211, 0.3); \\n\\n    transition: all 0.3s ease-in-out; \\n}\\n.row[_ngcontent-%COMP%]{   display: grid;\\n    grid-template-columns: repeat(auto-fit,minmax(160px,auto));\\n    gap:3rem\\n}\\n\\n\\n.item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n    width: 100%; \\n\\n    height: auto; \\n\\n    border-radius: 5px; \\n\\n}\\n\\n\\n\\n.item[_ngcontent-%COMP%]:hover {\\n    transform: scale(1.05); \\n\\n    box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.2); \\n\\n}\\n\\n    .body[_ngcontent-%COMP%]{padding-top: 0;\\n        flex-grow: 1;\\n        font-size: 1.1rem; \\n\\n  padding: 10px; \\n\\n  background-color: #f9f9f9; \\n\\n  border-radius: 5px; \\n\\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1); \\n\\n    }\\n    p[_ngcontent-%COMP%]{\\n        margin: 15 px 0;\\n        height: 100px;\\n        overflow: hidden;\\n        font-size: 16px;\\n        padding: 5px 15px;\\n    }\\n    h4[_ngcontent-%COMP%]{\\n        height: 75px;\\n        overflow: hidden;\\n        padding: 5px 15px;\\n        font-size: 18px;\\n        background-color: #eee;\\n        font-weight: 600;\\n        margin-top: 10px;\\n    }\\n    &[_ngcontent-%COMP%]:hover{\\n        transform: scale(1.1);\\n    }\\n    span[_ngcontent-%COMP%]{\\n        background-color: #f1b4b4;\\n        padding: 3px 7px;\\n        border-radius: 30px;\\n    }\\n    .end-text[_ngcontent-%COMP%]{\\n        background-color: #edfff1;\\n        text-align: center;\\n        padding: 20px;\\n    }\\n    .end-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{\\n        color: #111;\\n        text-transform: capitalize;\\n    \\n    }\\n    .px-3[_ngcontent-%COMP%] {\\n        display: flex; \\n\\n        justify-content: space-between; \\n\\n        align-items: center; \\n\\n        gap: 10px; \\n\\n      }\\n      \\n      .btn[_ngcontent-%COMP%] {\\n        flex-shrink: 0; \\n\\n      }\\n      \\n      span[_ngcontent-%COMP%] {\\n        margin-left: auto; \\n\\n      }\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵpropertyInterpolate", "item_r2", "imgSrc", "ɵɵproperty", "ɵɵsanitizeUrl", "ɵɵtextInterpolate", "title", "description", "ɵɵtextInterpolate1", "price", "ShopComponent", "constructor", "selectedCate<PERSON><PERSON>", "searchQuery", "items", "id", "category", "getFilteredItems", "query", "toLowerCase", "filter", "item", "matchesCategory", "matchesSearch", "includes", "selectors", "decls", "vars", "consts", "template", "ShopComponent_Template", "rf", "ctx", "ɵɵlistener", "ShopComponent_Template_select_ngModelChange_4_listener", "$event", "ShopComponent_Template_input_ngModelChange_18_listener", "ɵɵtemplate", "ShopComponent_div_19_Template", "ShopComponent_div_20_Template", "length"], "sources": ["C:\\Users\\<USER>\\Desktop\\projet angular\\Projet\\src\\shop\\shop.component.ts", "C:\\Users\\<USER>\\Desktop\\projet angular\\Projet\\src\\shop\\shop.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-shop',\n  templateUrl: './shop.component.html',\n  styleUrls: ['./shop.component.css']\n})\nexport class ShopComponent {\n  selectedCategory: string = '';\n  searchQuery: string = '';\n  items = [\n    { id: 1, title: 'Item 1', description: 'Description 1', price: 50, category: '1', imgSrc:'assets/4.jpg'},\n    { id: 2, title: 'Item 2', description: 'Description 2', price: 60, category: '2', imgSrc:'assets/2.jpg'  },\n    { id: 3, title: 'Item 3', description: 'Description 3', price: 70, category: '1', imgSrc:'assets/7.jpg'  },\n    { id: 4, title: 'Item 4', description: 'Description 4', price: 80, category: '3', imgSrc:'assets/8.jpg'  }\n  ];\n\n  getFilteredItems() {\n    const query = this.searchQuery.toLowerCase(); // Search query (case-insensitive)\n    return this.items.filter(item => {\n      const matchesCategory = !this.selectedCategory || item.category === this.selectedCategory;\n      const matchesSearch = item.title.toLowerCase().includes(query) || item.description.toLowerCase().includes(query);\n      return matchesCategory && matchesSearch;\n    });\n  }\n\n}\n", "<div class=\"box mt-5\">\n    <div class=\"my-2 w-25\">\n      <label for=\"categories\" class=\"my-1\">Categories</label>\n      <select\n        id=\"categories\"\n        class=\"form-control\"\n        [(ngModel)]=\"selectedCategory\"\n      >\n        <option value=\"\">All Categories</option>\n        <option value=\"1\">Category 1</option>\n        <option value=\"2\">Category 2</option>\n        <option value=\"3\">Category 3</option>\n        <option value=\"4\">Category 4</option>\n      </select>\n    </div>\n    <div class=\"my-2 w-50\">\n      <label for=\"search\" class=\"my-1\">Search Items</label>\n      <input\n        id=\"search\"\n        type=\"text\"\n        class=\"form-control\"\n        placeholder=\"Search by title...\"\n        [(ngModel)]=\"searchQuery\"\n      />\n    </div>\n    <div class=\"row\" *ngFor=\"let item of getFilteredItems()\">\n      <div class=\"col-md-3 col-sm-12\">\n      <div class=\"item d-flex\">\n          <img [src]=\"item.imgSrc\" alt={{item.imgSrc}}>\n          <div class=\"body\">\n            <h4>{{ item.title }}</h4>\n            <p>{{ item.description }}</p>\n          </div>\n          <div class=\"px-3 mb-2 d-flex justify-content-between align-items-center\">\n            <button class=\"btn btn-success\">Add to Cart</button>\n            <span>{{ item.price }} DT</span>\n          </div>\n        </div>\n      </div>\n    </div>\n  \n    <!-- Show a message if no items match the filter -->\n    <div *ngIf=\"getFilteredItems().length === 0\" class=\"text-center mt-4\">\n      <p>No items available for the selected category.</p>\n    </div>\n  </div>\n  \n"], "mappings": ";;;;;ICyBIA,EAAA,CAAAC,cAAA,cAAyD;IAGnDD,EAAA,CAAAE,SAAA,cAA6C;IAC7CF,EAAA,CAAAC,cAAA,cAAkB;IACZD,EAAA,CAAAG,MAAA,GAAgB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACzBJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,GAAsB;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAE/BJ,EAAA,CAAAC,cAAA,cAAyE;IACvCD,EAAA,CAAAG,MAAA,mBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACpDJ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAG,MAAA,IAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IAPTJ,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAM,qBAAA,QAAAC,OAAA,CAAAC,MAAA,CAAmB;IAAvCR,EAAA,CAAAS,UAAA,QAAAF,OAAA,CAAAC,MAAA,EAAAR,EAAA,CAAAU,aAAA,CAAmB;IAElBV,EAAA,CAAAK,SAAA,GAAgB;IAAhBL,EAAA,CAAAW,iBAAA,CAAAJ,OAAA,CAAAK,KAAA,CAAgB;IACjBZ,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAAW,iBAAA,CAAAJ,OAAA,CAAAM,WAAA,CAAsB;IAInBb,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAc,kBAAA,KAAAP,OAAA,CAAAQ,KAAA,QAAmB;;;;;IAOjCf,EAAA,CAAAC,cAAA,cAAsE;IACjED,EAAA,CAAAG,MAAA,oDAA6C;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;ADpC1D,OAAM,MAAOY,aAAa;EAL1BC,YAAA;IAME,KAAAC,gBAAgB,GAAW,EAAE;IAC7B,KAAAC,WAAW,GAAW,EAAE;IACxB,KAAAC,KAAK,GAAG,CACN;MAAEC,EAAE,EAAE,CAAC;MAAET,KAAK,EAAE,QAAQ;MAAEC,WAAW,EAAE,eAAe;MAAEE,KAAK,EAAE,EAAE;MAAEO,QAAQ,EAAE,GAAG;MAAEd,MAAM,EAAC;IAAc,CAAC,EACxG;MAAEa,EAAE,EAAE,CAAC;MAAET,KAAK,EAAE,QAAQ;MAAEC,WAAW,EAAE,eAAe;MAAEE,KAAK,EAAE,EAAE;MAAEO,QAAQ,EAAE,GAAG;MAAEd,MAAM,EAAC;IAAc,CAAG,EAC1G;MAAEa,EAAE,EAAE,CAAC;MAAET,KAAK,EAAE,QAAQ;MAAEC,WAAW,EAAE,eAAe;MAAEE,KAAK,EAAE,EAAE;MAAEO,QAAQ,EAAE,GAAG;MAAEd,MAAM,EAAC;IAAc,CAAG,EAC1G;MAAEa,EAAE,EAAE,CAAC;MAAET,KAAK,EAAE,QAAQ;MAAEC,WAAW,EAAE,eAAe;MAAEE,KAAK,EAAE,EAAE;MAAEO,QAAQ,EAAE,GAAG;MAAEd,MAAM,EAAC;IAAc,CAAG,CAC3G;;EAEDe,gBAAgBA,CAAA;IACd,MAAMC,KAAK,GAAG,IAAI,CAACL,WAAW,CAACM,WAAW,EAAE,CAAC,CAAC;IAC9C,OAAO,IAAI,CAACL,KAAK,CAACM,MAAM,CAACC,IAAI,IAAG;MAC9B,MAAMC,eAAe,GAAG,CAAC,IAAI,CAACV,gBAAgB,IAAIS,IAAI,CAACL,QAAQ,KAAK,IAAI,CAACJ,gBAAgB;MACzF,MAAMW,aAAa,GAAGF,IAAI,CAACf,KAAK,CAACa,WAAW,EAAE,CAACK,QAAQ,CAACN,KAAK,CAAC,IAAIG,IAAI,CAACd,WAAW,CAACY,WAAW,EAAE,CAACK,QAAQ,CAACN,KAAK,CAAC;MAChH,OAAOI,eAAe,IAAIC,aAAa;IACzC,CAAC,CAAC;EACJ;;;uBAjBWb,aAAa;IAAA;EAAA;;;YAAbA,aAAa;MAAAe,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCP1BrC,EAAA,CAAAC,cAAA,aAAsB;UAEqBD,EAAA,CAAAG,MAAA,iBAAU;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UACvDJ,EAAA,CAAAC,cAAA,gBAIC;UADCD,EAAA,CAAAuC,UAAA,2BAAAC,uDAAAC,MAAA;YAAA,OAAAH,GAAA,CAAApB,gBAAA,GAAAuB,MAAA;UAAA,EAA8B;UAE9BzC,EAAA,CAAAC,cAAA,gBAAiB;UAAAD,EAAA,CAAAG,MAAA,qBAAc;UAAAH,EAAA,CAAAI,YAAA,EAAS;UACxCJ,EAAA,CAAAC,cAAA,gBAAkB;UAAAD,EAAA,CAAAG,MAAA,iBAAU;UAAAH,EAAA,CAAAI,YAAA,EAAS;UACrCJ,EAAA,CAAAC,cAAA,gBAAkB;UAAAD,EAAA,CAAAG,MAAA,kBAAU;UAAAH,EAAA,CAAAI,YAAA,EAAS;UACrCJ,EAAA,CAAAC,cAAA,iBAAkB;UAAAD,EAAA,CAAAG,MAAA,kBAAU;UAAAH,EAAA,CAAAI,YAAA,EAAS;UACrCJ,EAAA,CAAAC,cAAA,iBAAkB;UAAAD,EAAA,CAAAG,MAAA,kBAAU;UAAAH,EAAA,CAAAI,YAAA,EAAS;UAGzCJ,EAAA,CAAAC,cAAA,cAAuB;UACYD,EAAA,CAAAG,MAAA,oBAAY;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UACrDJ,EAAA,CAAAC,cAAA,iBAME;UADAD,EAAA,CAAAuC,UAAA,2BAAAG,uDAAAD,MAAA;YAAA,OAAAH,GAAA,CAAAnB,WAAA,GAAAsB,MAAA;UAAA,EAAyB;UAL3BzC,EAAA,CAAAI,YAAA,EAME;UAEJJ,EAAA,CAAA2C,UAAA,KAAAC,6BAAA,mBAcM;UAGN5C,EAAA,CAAA2C,UAAA,KAAAE,6BAAA,kBAEM;UACR7C,EAAA,CAAAI,YAAA,EAAM;;;UAvCAJ,EAAA,CAAAK,SAAA,GAA8B;UAA9BL,EAAA,CAAAS,UAAA,YAAA6B,GAAA,CAAApB,gBAAA,CAA8B;UAgB9BlB,EAAA,CAAAK,SAAA,IAAyB;UAAzBL,EAAA,CAAAS,UAAA,YAAA6B,GAAA,CAAAnB,WAAA,CAAyB;UAGKnB,EAAA,CAAAK,SAAA,GAAqB;UAArBL,EAAA,CAAAS,UAAA,YAAA6B,GAAA,CAAAf,gBAAA,GAAqB;UAiBjDvB,EAAA,CAAAK,SAAA,GAAqC;UAArCL,EAAA,CAAAS,UAAA,SAAA6B,GAAA,CAAAf,gBAAA,GAAAuB,MAAA,OAAqC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}