*{
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    scroll-behavior: smooth;
    font-family: 'sans-serif';
    list-style: none;
    text-decoration: none;
}
/* Hero Section */
.section {
  padding: 5% 10%;
  width: 100%;
  min-height: 80vh;
  background-image: linear-gradient(rgba(240, 98, 146, 0.8), rgba(173, 20, 87, 0.8)), url('images/banner-3.png');
  background-position: center;
  background-size: cover;
  display: flex;
  flex-direction: column;
  justify-content: center;
  position: relative;
}

.section::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 70px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1440 320'%3E%3Cpath fill='%23FCE4EC' fill-opacity='1' d='M0,96L48,112C96,128,192,160,288,186.7C384,213,480,235,576,224C672,213,768,171,864,149.3C960,128,1056,128,1152,149.3C1248,171,1344,213,1392,234.7L1440,256L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z'%3E%3C/path%3E%3C/svg%3E");
  background-size: cover;
  background-repeat: no-repeat;
}

.section h5 {
  color: var(--white);
  font-size: 18px;
  text-transform: uppercase;
  letter-spacing: 3px;
  font-weight: 500;
  position: relative;
  display: inline-block;
  margin-bottom: 10px;
}

.section h5::after {
  content: '♥';
  position: absolute;
  right: -25px;
  color: var(--light-pink);
}

.section h1 {
  color: var(--white);
  font-size: 4rem;
  line-height: 1.1;
  font-weight: 700;
  margin: 15px 0;
  font-family: var(--font-cursive);
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
}

.section h6 {
  color: var(--light-pink);
  font-size: 22px;
  font-style: italic;
  margin-bottom: 30px;
}

.section .btn-shop {
  display: inline-block;
  padding: 14px 35px;
  background-color: var(--white);
  color: var(--dark-pink);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  cursor: pointer;
  transition: var(--transition);
  border-radius: 50px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.section .btn-shop::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 0%;
  height: 100%;
  background-color: var(--light-pink);
  transition: all 0.5s ease;
  z-index: -1;
  border-radius: 50px;
}

.section .btn-shop:hover::before {
  width: 100%;
}

.section .btn-shop:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}
/* Products Section */
.center-text {
  text-align: center;
  margin: 70px 0 50px;
}

.center-text h2 {
  font-size: 36px;
  position: relative;
  display: inline-block;
  color: var(--dark-pink);
  font-family: var(--font-cursive);
}

.center-text h2::before, .center-text h2::after {
  content: '♥';
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  color: var(--primary);
  font-size: 24px;
}

.center-text h2::before {
  left: -40px;
}

.center-text h2::after {
  right: -40px;
}

.center-text span {
  color: var(--accent);
}

.grid-Products {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 30px;
  padding: 0 20px;
}

.grid-row {
  background-color: var(--white);
  border-radius: 15px;
  overflow: hidden;
  box-shadow: var(--shadow);
  transition: var(--transition);
  position: relative;
}

.grid-row::before {
  content: '';
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  background: linear-gradient(45deg, var(--primary), var(--secondary), var(--accent));
  z-index: -1;
  border-radius: 20px;
  opacity: 0;
  transition: var(--transition);
}

.grid-row:hover::before {
  opacity: 1;
}

.grid-row:hover {
  transform: translateY(-10px) scale(1.02);
  box-shadow: 0 15px 30px rgba(240, 98, 146, 0.2);
}

.grid-row img {
  width: 100%;
  height: 250px;
  object-fit: cover;
  transition: var(--transition);
}

.grid-row:hover img {
  transform: scale(1.05);
}

.product-text {
  position: absolute;
  top: 15px;
  right: 15px;
}

.badge {
  background-color: var(--primary);
  color: var(--white);
  padding: 5px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  box-shadow: 0 3px 8px rgba(240, 98, 146, 0.3);
}

.product-info {
  padding: 15px;
}

.ratting {
  margin-bottom: 10px;
}

.ratting i {
  color: var(--accent);
  font-size: 16px;
  margin-right: 2px;
}

.price h4 {
  font-size: 18px;
  color: var(--dark-pink);
  margin-bottom: 5px;
  text-transform: capitalize;
}

.price p {
  color: var(--primary);
  font-weight: 600;
  font-size: 18px;
}
/* Client Reviews */
.client-reviews {
  background-color: var(--medium-pink);
  padding: 80px 20px;
  margin-top: 80px;
  position: relative;
}

.client-reviews::before, .client-reviews::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 30px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1440 320'%3E%3Cpath fill='%23FCE4EC' fill-opacity='1' d='M0,96L48,112C96,128,192,160,288,186.7C384,213,480,235,576,224C672,213,768,171,864,149.3C960,128,1056,128,1152,149.3C1248,171,1344,213,1392,234.7L1440,256L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z'%3E%3C/path%3E%3C/svg%3E");
  background-size: cover;
  background-repeat: no-repeat;
}

.client-reviews::before {
  top: -30px;
  transform: rotate(180deg);
}

.client-reviews::after {
  bottom: -30px;
}

.reviews {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
  background-color: var(--white);
  padding: 40px;
  border-radius: 20px;
  box-shadow: var(--shadow);
  position: relative;
}

.reviews::before, .reviews::after {
  content: '"';
  position: absolute;
  font-size: 100px;
  color: var(--primary);
  opacity: 0.2;
  font-family: serif;
  line-height: 1;
}

.reviews::before {
  top: 20px;
  left: 20px;
}

.reviews::after {
  bottom: 0;
  right: 20px;
  transform: rotate(180deg);
}

.reviews h3 {
  font-size: 32px;
  margin-bottom: 40px;
  position: relative;
  display: inline-block;
  color: var(--primary);
  font-family: var(--font-cursive);
}

.reviews h3::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 70px;
  height: 3px;
  background: linear-gradient(to right, var(--secondary), var(--primary), var(--secondary));
}

.reviews img {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  object-fit: cover;
  border: 5px solid white;
  box-shadow: 0 0 0 3px var(--secondary);
  margin: 20px auto;
}

.reviews p {
  color: #666;
  font-size: 16px;
  line-height: 1.8;
  margin-bottom: 20px;
  font-style: italic;
}

.reviews h2 {
  font-size: 22px;
  color: var(--primary);
  margin-bottom: 5px;
}
/* Contact Section */
.contact {
  padding: 80px 20px;
  background-color: white;
}

.contact-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
}

.contact-info h4 {
  color: var(--primary);
  font-size: 20px;
  text-transform: uppercase;
  margin-bottom: 20px;
  position: relative;
  display: inline-block;
}

.contact-info h4::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 40px;
  height: 2px;
  background: linear-gradient(to right, var(--primary), var(--secondary));
}

.first-info img {
  width: 140px;
  margin-bottom: 20px;
}

.contact-info p {
  color: #666;
  margin-bottom: 10px;
  transition: var(--transition);
  position: relative;
  padding-left: 25px;
}

.contact-info p::before {
  content: '❀';
  position: absolute;
  left: 0;
  color: var(--secondary);
  font-size: 14px;
}

.contact-info p:hover {
  color: var(--primary);
  transform: translateX(5px);
}

.social-icon {
  display: flex;
  margin-top: 20px;
}

.social-icon a {
  display: flex;
  align-items: center
}
/* Contact Section */
.contact {
  padding: 80px 20px;
  background-color: white;
}

.contact-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
}

.contact-info h4 {
  color: var(--primary);
  font-size: 18px;
  text-transform: uppercase;
  margin-bottom: 20px;
  position: relative;
}

.contact-info h4::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 40px;
  height: 2px;
  background-color: var(--accent);
}

.first-info img {
  width: 140px;
  margin-bottom: 20px;
}

.contact-info p {
  color: #666;
  margin-bottom: 10px;
  transition: var(--transition);
}

.contact-info p:hover {
  color: var(--accent);
  transform: translateX(5px);
}

.social-icon {
  display: flex;
  margin-top: 20px;
}

.social-icon a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: var(--light);
  border-radius: 50%;
  margin-right: 10px;
  transition: var(--transition);
}

.social-icon a:hover {
  background-color: var(--accent);
  color: white;
  transform: translateY(-5px);
}

.social-icon i {
  font-size: 20px;
}

@media (max-width: 768px) {
  .section h1 {
    font-size: 2.5rem;
  }
  
  .grid-Products {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  }
}
.end-text{
    background-color: #edfff1;
    text-align: center;
    padding: 20px;
}
.end-text p{
    color: #111;
    text-transform: capitalize;

}
