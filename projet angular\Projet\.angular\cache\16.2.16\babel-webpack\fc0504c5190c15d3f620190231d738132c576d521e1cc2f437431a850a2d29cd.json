{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/forms\";\nexport class LognComponent {\n  static {\n    this.ɵfac = function LognComponent_Factory(t) {\n      return new (t || LognComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LognComponent,\n      selectors: [[\"app-logn\"]],\n      decls: 25,\n      vars: 0,\n      consts: [[\"align\", \"center\", 1, \"register\"], [\"src\", \"assets/women's wear.png\", \"alt\", \"Logo\"], [\"align\", \"center\", 1, \"container\"], [\"type\", \"text\", \"name\", \"username\"], [\"type\", \"password\", \"name\", \"password\"], [\"routerLink\", \"/login\"], [\"type\", \"button\", 2, \"padding\", \"5px 45px\"]],\n      template: function LognComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵelement(1, \"img\", 1);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(2, \"div\", 2)(3, \"fieldset\")(4, \"legend\");\n          i0.ɵɵtext(5, \"Log in\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"form\")(7, \"table\")(8, \"tr\")(9, \"td\")(10, \"p\");\n          i0.ɵɵtext(11, \"Username\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(12, \"input\", 3);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(13, \"tr\")(14, \"td\")(15, \"p\");\n          i0.ɵɵtext(16, \"password\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(17, \"input\", 4);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(18, \"a\", 5);\n          i0.ɵɵtext(19, \"You already have an account?click here\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"div\")(21, \"button\", 6);\n          i0.ɵɵtext(22, \"Submit\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"button\", 6);\n          i0.ɵɵtext(24, \"Reset\");\n          i0.ɵɵelementEnd()()()()();\n        }\n      },\n      dependencies: [i1.RouterLink, i2.ɵNgNoValidate, i2.NgControlStatusGroup, i2.NgForm],\n      styles: [\".register[_ngcontent-%COMP%]{\\n    padding-top: 80px;\\n}\\nimg[_ngcontent-%COMP%]{\\nwidth: 200px;\\nheight: 200px;\\n} \\nlegend[_ngcontent-%COMP%]{\\nfont-size:50px;\\nfont-family: 'Trebuchet MS'; \\nfont-style: italic;\\nfont-weight:bold; \\ncolor: rgb(22, 21, 21);\\n}\\np[_ngcontent-%COMP%]{\\nfont-size:15px;\\nfont-family: 'Lucida Sans Unicode';\\nfont-style: italic;  \\ncolor: rgb(22, 21, 21);\\n\\n}\\nselect[_ngcontent-%COMP%]{\\npadding:1px 45px;\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9sb2duL2xvZ24uY29tcG9uZW50LmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtJQUNJLGlCQUFpQjtBQUNyQjtBQUNBO0FBQ0EsWUFBWTtBQUNaLGFBQWE7QUFDYjtBQUNBO0FBQ0EsY0FBYztBQUNkLDJCQUEyQjtBQUMzQixrQkFBa0I7QUFDbEIsZ0JBQWdCO0FBQ2hCLHNCQUFzQjtBQUN0QjtBQUNBO0FBQ0EsY0FBYztBQUNkLGtDQUFrQztBQUNsQyxrQkFBa0I7QUFDbEIsc0JBQXNCOztBQUV0QjtBQUNBO0FBQ0EsZ0JBQWdCO0FBQ2hCIiwic291cmNlc0NvbnRlbnQiOlsiLnJlZ2lzdGVye1xyXG4gICAgcGFkZGluZy10b3A6IDgwcHg7XHJcbn1cclxuaW1ne1xyXG53aWR0aDogMjAwcHg7XHJcbmhlaWdodDogMjAwcHg7XHJcbn0gXHJcbmxlZ2VuZHtcclxuZm9udC1zaXplOjUwcHg7XHJcbmZvbnQtZmFtaWx5OiAnVHJlYnVjaGV0IE1TJzsgXHJcbmZvbnQtc3R5bGU6IGl0YWxpYztcclxuZm9udC13ZWlnaHQ6Ym9sZDsgXHJcbmNvbG9yOiByZ2IoMjIsIDIxLCAyMSk7XHJcbn1cclxucHtcclxuZm9udC1zaXplOjE1cHg7XHJcbmZvbnQtZmFtaWx5OiAnTHVjaWRhIFNhbnMgVW5pY29kZSc7XHJcbmZvbnQtc3R5bGU6IGl0YWxpYzsgIFxyXG5jb2xvcjogcmdiKDIyLCAyMSwgMjEpO1xyXG5cclxufVxyXG5zZWxlY3R7XHJcbnBhZGRpbmc6MXB4IDQ1cHg7XHJcbn1cclxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["LognComponent", "selectors", "decls", "vars", "consts", "template", "LognComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext"], "sources": ["C:\\Users\\<USER>\\Desktop\\projet angular\\Projet\\src\\logn\\logn.component.ts", "C:\\Users\\<USER>\\Desktop\\projet angular\\Projet\\src\\logn\\logn.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-logn',\n  templateUrl: './logn.component.html',\n  styleUrls: ['./logn.component.css']\n})\nexport class LognComponent {\n\n}\n", "<div class=\"register\" align=\"center\">\n    <img src=\"assets/women's wear.png\" alt=\"Logo\" />\n</div>\n<div class=\"container\" align=\"center\">\n    <fieldset>\n        <legend>Log in</legend>\n        <form>\n            <table>\n                <tr>\n                    <td>\n                        <p>Username</p>\n                        <input type=\"text\" name=\"username\"/>\n                    </td>\n                </tr>\n            \n                <!-- Governorate Field -->\n                <tr>\n                    <td>\n                        <p>password</p>\n                        <input type=\"password\" name=\"password\"/>\n                    </td>\n                </tr>\n            </table>\n            <a routerLink=\"/login\">You already have an account?click here</a>\n            <div>\n                <button type=\"button\" style=\"padding: 5px 45px;\">Submit</button>\n                <button type=\"button\" style=\"padding: 5px 45px;\">Reset</button>\n            </div>\n        </form>\n    </fieldset>\n</div>\n\n\n"], "mappings": ";;;AAOA,OAAM,MAAOA,aAAa;;;uBAAbA,aAAa;IAAA;EAAA;;;YAAbA,aAAa;MAAAC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCP1BE,EAAA,CAAAC,cAAA,aAAqC;UACjCD,EAAA,CAAAE,SAAA,aAAgD;UACpDF,EAAA,CAAAG,YAAA,EAAM;UACNH,EAAA,CAAAC,cAAA,aAAsC;UAEtBD,EAAA,CAAAI,MAAA,aAAM;UAAAJ,EAAA,CAAAG,YAAA,EAAS;UACvBH,EAAA,CAAAC,cAAA,WAAM;UAIaD,EAAA,CAAAI,MAAA,gBAAQ;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UACfH,EAAA,CAAAE,SAAA,gBAAoC;UACxCF,EAAA,CAAAG,YAAA,EAAK;UAITH,EAAA,CAAAC,cAAA,UAAI;UAEOD,EAAA,CAAAI,MAAA,gBAAQ;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UACfH,EAAA,CAAAE,SAAA,gBAAwC;UAC5CF,EAAA,CAAAG,YAAA,EAAK;UAGbH,EAAA,CAAAC,cAAA,YAAuB;UAAAD,EAAA,CAAAI,MAAA,8CAAsC;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UACjEH,EAAA,CAAAC,cAAA,WAAK;UACgDD,EAAA,CAAAI,MAAA,cAAM;UAAAJ,EAAA,CAAAG,YAAA,EAAS;UAChEH,EAAA,CAAAC,cAAA,iBAAiD;UAAAD,EAAA,CAAAI,MAAA,aAAK;UAAAJ,EAAA,CAAAG,YAAA,EAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}