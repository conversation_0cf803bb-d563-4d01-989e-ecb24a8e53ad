{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/forms\";\nfunction ShopComponent_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"div\", 15)(2, \"div\", 16);\n    i0.ɵɵelement(3, \"img\", 17);\n    i0.ɵɵelementStart(4, \"div\", 18)(5, \"h4\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 19)(10, \"button\", 20);\n    i0.ɵɵtext(11, \"Add to Cart\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\");\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const item_r2 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate(\"alt\", item_r2.imgSrc);\n    i0.ɵɵproperty(\"src\", item_r2.imgSrc, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r2.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r2.description);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", item_r2.price, \" DT\");\n  }\n}\nfunction ShopComponent_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"p\");\n    i0.ɵɵtext(2, \"No items available for the selected category.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class ShopComponent {\n  constructor() {\n    this.selectedCategory = '';\n    this.searchQuery = '';\n    this.items = [{\n      id: 1,\n      title: 'dress',\n      description: 'Description 1',\n      price: 50,\n      category: '1',\n      imgSrc: 'assets/4.jpg'\n    }, {\n      id: 2,\n      title: 'shorts',\n      description: 'Description 2',\n      price: 60,\n      category: '2',\n      imgSrc: 'assets/2.jpg'\n    }, {\n      id: 3,\n      title: 'shirt',\n      description: 'Description 3',\n      price: 70,\n      category: '1',\n      imgSrc: 'assets/7.jpg'\n    }, {\n      id: 4,\n      title: 'jacket',\n      description: 'Description 4',\n      price: 80,\n      category: '3',\n      imgSrc: 'assets/8.jpg'\n    }, {\n      id: 4,\n      title: 'pants',\n      description: 'Description 5',\n      price: 50,\n      category: '1',\n      imgSrc: 'assets/5.jpg'\n    }, {\n      id: 4,\n      title: 'jacket',\n      description: 'Description 6',\n      price: 70,\n      category: '1',\n      imgSrc: 'assets/1.jpg'\n    }, {\n      id: 4,\n      title: 'pink shorts ',\n      description: 'Description 7',\n      price: 40,\n      category: '2',\n      imgSrc: 'assets/3.jpg'\n    }];\n  }\n  getFilteredItems() {\n    const query = this.searchQuery.toLowerCase();\n    return this.items.filter(item => {\n      const matchesCategory = !this.selectedCategory || item.category === this.selectedCategory;\n      const matchesSearch = item.title.toLowerCase().includes(query) || item.description.toLowerCase().includes(query);\n      return matchesCategory && matchesSearch;\n    });\n  }\n  static {\n    this.ɵfac = function ShopComponent_Factory(t) {\n      return new (t || ShopComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ShopComponent,\n      selectors: [[\"app-shop\"]],\n      decls: 21,\n      vars: 4,\n      consts: [[1, \"box\", \"mt-5\"], [1, \"my-2\", \"w-25\"], [\"for\", \"categories\", 1, \"my-1\"], [\"id\", \"categories\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"value\", \"\"], [\"value\", \"1\"], [\"value\", \"2\"], [\"value\", \"3\"], [\"value\", \"4\"], [1, \"my-2\", \"w-50\"], [\"for\", \"search\", 1, \"my-1\"], [\"id\", \"search\", \"type\", \"text\", \"placeholder\", \"Search by title...\", 1, \"form-control\", 3, \"ngModel\", \"ngModelChange\"], [\"class\", \"row\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"text-center mt-4\", 4, \"ngIf\"], [1, \"row\"], [1, \"col-md-3\", \"col-sm-12\"], [1, \"item\", \"d-flex\"], [3, \"src\", \"alt\"], [1, \"body\"], [1, \"px-3\", \"mb-2\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"btn\", \"btn-success\"], [1, \"text-center\", \"mt-4\"]],\n      template: function ShopComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"label\", 2);\n          i0.ɵɵtext(3, \"Categories\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"select\", 3);\n          i0.ɵɵlistener(\"ngModelChange\", function ShopComponent_Template_select_ngModelChange_4_listener($event) {\n            return ctx.selectedCategory = $event;\n          });\n          i0.ɵɵelementStart(5, \"option\", 4);\n          i0.ɵɵtext(6, \"All Categories\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"option\", 5);\n          i0.ɵɵtext(8, \"for summer\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"option\", 6);\n          i0.ɵɵtext(10, \"shorts\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(11, \"option\", 7);\n          i0.ɵɵtext(12, \"jackets\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"option\", 8);\n          i0.ɵɵtext(14, \"shoes\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(15, \"div\", 9)(16, \"label\", 10);\n          i0.ɵɵtext(17, \"Search Items\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"input\", 11);\n          i0.ɵɵlistener(\"ngModelChange\", function ShopComponent_Template_input_ngModelChange_18_listener($event) {\n            return ctx.searchQuery = $event;\n          });\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(19, ShopComponent_div_19_Template, 14, 5, \"div\", 12);\n          i0.ɵɵtemplate(20, ShopComponent_div_20_Template, 3, 0, \"div\", 13);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngModel\", ctx.selectedCategory);\n          i0.ɵɵadvance(14);\n          i0.ɵɵproperty(\"ngModel\", ctx.searchQuery);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngForOf\", ctx.getFilteredItems());\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.getFilteredItems().length === 0);\n        }\n      },\n      dependencies: [i1.NgForOf, i1.NgIf, i2.NgSelectOption, i2.ɵNgSelectMultipleOption, i2.DefaultValueAccessor, i2.SelectControlValueAccessor, i2.NgControlStatus, i2.NgModel],\n      styles: [\"body[_ngcontent-%COMP%]{margin-top: 100px;}\\n\\n.shop-container[_ngcontent-%COMP%] {\\n  padding: 80px 20px;\\n}\\n\\n.shop-header[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 40px;\\n}\\n\\n.shop-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 32px;\\n  color: var(--dark-pink);\\n  position: relative;\\n  display: inline-block;\\n  font-family: var(--font-cursive);\\n}\\n\\n.shop-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]::after {\\n  content: '';\\n  position: absolute;\\n  bottom: -10px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  width: 60px;\\n  height: 3px;\\n  background-color: var(--primary);\\n}\\n\\n.filters[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  margin-bottom: 30px;\\n  flex-wrap: wrap;\\n}\\n\\n.filter-btn[_ngcontent-%COMP%] {\\n  padding: 8px 20px;\\n  margin: 0 10px 10px;\\n  background-color: var(--white);\\n  border: none;\\n  border-radius: 30px;\\n  cursor: pointer;\\n  transition: var(--transition);\\n  color: var(--dark-pink);\\n  box-shadow: 0 3px 10px rgba(240, 98, 146, 0.1);\\n}\\n\\n.filter-btn[_ngcontent-%COMP%]:hover, .filter-btn.active[_ngcontent-%COMP%] {\\n  background-color: var(--primary);\\n  color: var(--white);\\n  box-shadow: 0 5px 15px rgba(240, 98, 146, 0.2);\\n}\\n\\n.row[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\\n  gap: 30px;\\n}\\n\\n.item[_ngcontent-%COMP%] {\\n  background-color: white;\\n  border-radius: var(--border-radius);\\n  overflow: hidden;\\n  box-shadow: var(--shadow);\\n  transition: var(--transition);\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.item[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-10px);\\n  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);\\n}\\n\\n.item[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 280px;\\n  object-fit: cover;\\n}\\n\\n.body[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  flex-grow: 1;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.item[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  color: var(--primary);\\n  margin-bottom: 10px;\\n  height: auto;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  display: -webkit-box;\\n  -webkit-line-clamp: 2;\\n  -webkit-box-orient: vertical;\\n  background-color: transparent;\\n  padding: 0;\\n}\\n\\n.item[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 14px;\\n  margin-bottom: 15px;\\n  height: 60px;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  display: -webkit-box;\\n  -webkit-line-clamp: 3;\\n  -webkit-box-orient: vertical;\\n  padding: 0;\\n}\\n\\n.px-3[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-top: auto;\\n}\\n\\n.btn[_ngcontent-%COMP%] {\\n  padding: 8px 15px;\\n  background-color: var(--primary);\\n  color: white;\\n  border: none;\\n  border-radius: var(--border-radius);\\n  cursor: pointer;\\n  transition: var(--transition);\\n}\\n\\n.btn[_ngcontent-%COMP%]:hover {\\n  background-color: var(--accent);\\n}\\n\\nspan.price[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: var(--accent);\\n  font-size: 18px;\\n}\\n\\n@media (max-width: 768px) {\\n  .row[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵpropertyInterpolate", "item_r2", "imgSrc", "ɵɵproperty", "ɵɵsanitizeUrl", "ɵɵtextInterpolate", "title", "description", "ɵɵtextInterpolate1", "price", "ShopComponent", "constructor", "selectedCate<PERSON><PERSON>", "searchQuery", "items", "id", "category", "getFilteredItems", "query", "toLowerCase", "filter", "item", "matchesCategory", "matchesSearch", "includes", "selectors", "decls", "vars", "consts", "template", "ShopComponent_Template", "rf", "ctx", "ɵɵlistener", "ShopComponent_Template_select_ngModelChange_4_listener", "$event", "ShopComponent_Template_input_ngModelChange_18_listener", "ɵɵtemplate", "ShopComponent_div_19_Template", "ShopComponent_div_20_Template", "length"], "sources": ["C:\\Users\\<USER>\\Desktop\\projet angular\\Projet\\src\\shop\\shop.component.ts", "C:\\Users\\<USER>\\Desktop\\projet angular\\Projet\\src\\shop\\shop.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-shop',\n  templateUrl: './shop.component.html',\n  styleUrls: ['./shop.component.css']\n})\nexport class ShopComponent {\n  selectedCategory: string = '';\n  searchQuery: string = '';\n  items = [\n    { id: 1, title: 'dress', description: 'Description 1', price: 50, category: '1', imgSrc:'assets/4.jpg'},\n    { id: 2, title: 'shorts', description: 'Description 2', price: 60, category: '2', imgSrc:'assets/2.jpg'  },\n    { id: 3, title: 'shirt', description: 'Description 3', price: 70, category: '1', imgSrc:'assets/7.jpg'  },\n    { id: 4, title: 'jacket', description: 'Description 4', price: 80, category: '3', imgSrc:'assets/8.jpg'  },\n    { id: 4, title: 'pants', description: 'Description 5', price: 50, category: '1', imgSrc:'assets/5.jpg'  },\n    { id: 4, title: 'jacket', description: 'Description 6', price: 70, category: '1', imgSrc:'assets/1.jpg'  },\n    { id: 4, title: 'pink shorts ', description: 'Description 7', price: 40, category: '2', imgSrc:'assets/3.jpg'  }\n\n  ];\n\n  getFilteredItems() {\n    const query = this.searchQuery.toLowerCase();\n    return this.items.filter(item => {\n      const matchesCategory = !this.selectedCategory || item.category === this.selectedCategory;\n      const matchesSearch = item.title.toLowerCase().includes(query) || item.description.toLowerCase().includes(query);\n      return matchesCategory && matchesSearch;\n    });\n  }\n\n}\n", "<div class=\"box mt-5\">\n    <div class=\"my-2 w-25\">\n      <label for=\"categories\" class=\"my-1\">Categories</label>\n      <select\n        id=\"categories\"\n        class=\"form-control\"\n        [(ngModel)]=\"selectedCategory\"\n      >\n        <option value=\"\">All Categories</option>\n        <option value=\"1\">for summer</option>\n        <option value=\"2\">shorts</option>\n        <option value=\"3\">jackets</option>\n        <option value=\"4\">shoes</option>\n      </select>\n    </div>\n    <div class=\"my-2 w-50\">\n      <label for=\"search\" class=\"my-1\">Search Items</label>\n      <input\n        id=\"search\"\n        type=\"text\"\n        class=\"form-control\"\n        placeholder=\"Search by title...\"\n        [(ngModel)]=\"searchQuery\"\n      />\n    </div>\n    <div class=\"row\" *ngFor=\"let item of getFilteredItems()\">\n      <div class=\"col-md-3 col-sm-12\">\n      <div class=\"item d-flex\">\n          <img [src]=\"item.imgSrc\" alt={{item.imgSrc}}>\n          <div class=\"body\">\n            <h4>{{ item.title }}</h4>\n            <p>{{ item.description }}</p>\n          </div>\n          <div class=\"px-3 mb-2 d-flex justify-content-between align-items-center\">\n            <button class=\"btn btn-success\">Add to Cart</button>\n            <span>{{ item.price }} DT</span>\n          </div>\n        </div>\n      </div>\n    </div>\n  \n    <!-- Show a message if no items match the filter -->\n    <div *ngIf=\"getFilteredItems().length === 0\" class=\"text-center mt-4\">\n      <p>No items available for the selected category.</p>\n    </div>\n  </div>\n  \n"], "mappings": ";;;;;ICyBIA,EAAA,CAAAC,cAAA,cAAyD;IAGnDD,EAAA,CAAAE,SAAA,cAA6C;IAC7CF,EAAA,CAAAC,cAAA,cAAkB;IACZD,EAAA,CAAAG,MAAA,GAAgB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACzBJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,GAAsB;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAE/BJ,EAAA,CAAAC,cAAA,cAAyE;IACvCD,EAAA,CAAAG,MAAA,mBAAW;IAAAH,EAAA,CAAAI,YAAA,EAAS;IACpDJ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAG,MAAA,IAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IAPTJ,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAM,qBAAA,QAAAC,OAAA,CAAAC,MAAA,CAAmB;IAAvCR,EAAA,CAAAS,UAAA,QAAAF,OAAA,CAAAC,MAAA,EAAAR,EAAA,CAAAU,aAAA,CAAmB;IAElBV,EAAA,CAAAK,SAAA,GAAgB;IAAhBL,EAAA,CAAAW,iBAAA,CAAAJ,OAAA,CAAAK,KAAA,CAAgB;IACjBZ,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAAW,iBAAA,CAAAJ,OAAA,CAAAM,WAAA,CAAsB;IAInBb,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAc,kBAAA,KAAAP,OAAA,CAAAQ,KAAA,QAAmB;;;;;IAOjCf,EAAA,CAAAC,cAAA,cAAsE;IACjED,EAAA,CAAAG,MAAA,oDAA6C;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;ADpC1D,OAAM,MAAOY,aAAa;EAL1BC,YAAA;IAME,KAAAC,gBAAgB,GAAW,EAAE;IAC7B,KAAAC,WAAW,GAAW,EAAE;IACxB,KAAAC,KAAK,GAAG,CACN;MAAEC,EAAE,EAAE,CAAC;MAAET,KAAK,EAAE,OAAO;MAAEC,WAAW,EAAE,eAAe;MAAEE,KAAK,EAAE,EAAE;MAAEO,QAAQ,EAAE,GAAG;MAAEd,MAAM,EAAC;IAAc,CAAC,EACvG;MAAEa,EAAE,EAAE,CAAC;MAAET,KAAK,EAAE,QAAQ;MAAEC,WAAW,EAAE,eAAe;MAAEE,KAAK,EAAE,EAAE;MAAEO,QAAQ,EAAE,GAAG;MAAEd,MAAM,EAAC;IAAc,CAAG,EAC1G;MAAEa,EAAE,EAAE,CAAC;MAAET,KAAK,EAAE,OAAO;MAAEC,WAAW,EAAE,eAAe;MAAEE,KAAK,EAAE,EAAE;MAAEO,QAAQ,EAAE,GAAG;MAAEd,MAAM,EAAC;IAAc,CAAG,EACzG;MAAEa,EAAE,EAAE,CAAC;MAAET,KAAK,EAAE,QAAQ;MAAEC,WAAW,EAAE,eAAe;MAAEE,KAAK,EAAE,EAAE;MAAEO,QAAQ,EAAE,GAAG;MAAEd,MAAM,EAAC;IAAc,CAAG,EAC1G;MAAEa,EAAE,EAAE,CAAC;MAAET,KAAK,EAAE,OAAO;MAAEC,WAAW,EAAE,eAAe;MAAEE,KAAK,EAAE,EAAE;MAAEO,QAAQ,EAAE,GAAG;MAAEd,MAAM,EAAC;IAAc,CAAG,EACzG;MAAEa,EAAE,EAAE,CAAC;MAAET,KAAK,EAAE,QAAQ;MAAEC,WAAW,EAAE,eAAe;MAAEE,KAAK,EAAE,EAAE;MAAEO,QAAQ,EAAE,GAAG;MAAEd,MAAM,EAAC;IAAc,CAAG,EAC1G;MAAEa,EAAE,EAAE,CAAC;MAAET,KAAK,EAAE,cAAc;MAAEC,WAAW,EAAE,eAAe;MAAEE,KAAK,EAAE,EAAE;MAAEO,QAAQ,EAAE,GAAG;MAAEd,MAAM,EAAC;IAAc,CAAG,CAEjH;;EAEDe,gBAAgBA,CAAA;IACd,MAAMC,KAAK,GAAG,IAAI,CAACL,WAAW,CAACM,WAAW,EAAE;IAC5C,OAAO,IAAI,CAACL,KAAK,CAACM,MAAM,CAACC,IAAI,IAAG;MAC9B,MAAMC,eAAe,GAAG,CAAC,IAAI,CAACV,gBAAgB,IAAIS,IAAI,CAACL,QAAQ,KAAK,IAAI,CAACJ,gBAAgB;MACzF,MAAMW,aAAa,GAAGF,IAAI,CAACf,KAAK,CAACa,WAAW,EAAE,CAACK,QAAQ,CAACN,KAAK,CAAC,IAAIG,IAAI,CAACd,WAAW,CAACY,WAAW,EAAE,CAACK,QAAQ,CAACN,KAAK,CAAC;MAChH,OAAOI,eAAe,IAAIC,aAAa;IACzC,CAAC,CAAC;EACJ;;;uBArBWb,aAAa;IAAA;EAAA;;;YAAbA,aAAa;MAAAe,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCP1BrC,EAAA,CAAAC,cAAA,aAAsB;UAEqBD,EAAA,CAAAG,MAAA,iBAAU;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UACvDJ,EAAA,CAAAC,cAAA,gBAIC;UADCD,EAAA,CAAAuC,UAAA,2BAAAC,uDAAAC,MAAA;YAAA,OAAAH,GAAA,CAAApB,gBAAA,GAAAuB,MAAA;UAAA,EAA8B;UAE9BzC,EAAA,CAAAC,cAAA,gBAAiB;UAAAD,EAAA,CAAAG,MAAA,qBAAc;UAAAH,EAAA,CAAAI,YAAA,EAAS;UACxCJ,EAAA,CAAAC,cAAA,gBAAkB;UAAAD,EAAA,CAAAG,MAAA,iBAAU;UAAAH,EAAA,CAAAI,YAAA,EAAS;UACrCJ,EAAA,CAAAC,cAAA,gBAAkB;UAAAD,EAAA,CAAAG,MAAA,cAAM;UAAAH,EAAA,CAAAI,YAAA,EAAS;UACjCJ,EAAA,CAAAC,cAAA,iBAAkB;UAAAD,EAAA,CAAAG,MAAA,eAAO;UAAAH,EAAA,CAAAI,YAAA,EAAS;UAClCJ,EAAA,CAAAC,cAAA,iBAAkB;UAAAD,EAAA,CAAAG,MAAA,aAAK;UAAAH,EAAA,CAAAI,YAAA,EAAS;UAGpCJ,EAAA,CAAAC,cAAA,cAAuB;UACYD,EAAA,CAAAG,MAAA,oBAAY;UAAAH,EAAA,CAAAI,YAAA,EAAQ;UACrDJ,EAAA,CAAAC,cAAA,iBAME;UADAD,EAAA,CAAAuC,UAAA,2BAAAG,uDAAAD,MAAA;YAAA,OAAAH,GAAA,CAAAnB,WAAA,GAAAsB,MAAA;UAAA,EAAyB;UAL3BzC,EAAA,CAAAI,YAAA,EAME;UAEJJ,EAAA,CAAA2C,UAAA,KAAAC,6BAAA,mBAcM;UAGN5C,EAAA,CAAA2C,UAAA,KAAAE,6BAAA,kBAEM;UACR7C,EAAA,CAAAI,YAAA,EAAM;;;UAvCAJ,EAAA,CAAAK,SAAA,GAA8B;UAA9BL,EAAA,CAAAS,UAAA,YAAA6B,GAAA,CAAApB,gBAAA,CAA8B;UAgB9BlB,EAAA,CAAAK,SAAA,IAAyB;UAAzBL,EAAA,CAAAS,UAAA,YAAA6B,GAAA,CAAAnB,WAAA,CAAyB;UAGKnB,EAAA,CAAAK,SAAA,GAAqB;UAArBL,EAAA,CAAAS,UAAA,YAAA6B,GAAA,CAAAf,gBAAA,GAAqB;UAiBjDvB,EAAA,CAAAK,SAAA,GAAqC;UAArCL,EAAA,CAAAS,UAAA,SAAA6B,GAAA,CAAAf,gBAAA,GAAAuB,MAAA,OAAqC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}