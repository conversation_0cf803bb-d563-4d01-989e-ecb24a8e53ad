{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class AppComponent {\n  constructor() {\n    this.title = 'Projet';\n  }\n  static {\n    this.ɵfac = function AppComponent_Factory(t) {\n      return new (t || AppComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppComponent,\n      selectors: [[\"app-root\"]],\n      decls: 14,\n      vars: 0,\n      consts: [[1, \"container\"], [1, \"content\"], [\"src\", \"assets/logo.jpg\", \"alt\", \"\", \"routerLink\", \"/home\", 1, \"logo\"], [\"routerLink\", \"/home\"], [\"routerLink\", \"/shop\"], [\"routerLink\", \"/log\"]],\n      template: function AppComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"nav\")(1, \"div\", 0)(2, \"div\", 1);\n          i0.ɵɵelement(3, \"img\", 2);\n          i0.ɵɵelementStart(4, \"ul\")(5, \"li\", 3);\n          i0.ɵɵtext(6, \"Home\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"li\", 4);\n          i0.ɵɵtext(8, \"Shop\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"ul\")(10, \"li\", 5);\n          i0.ɵɵtext(11, \"Login\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(12, \"div\", 0);\n          i0.ɵɵelement(13, \"router-outlet\");\n          i0.ɵɵelementEnd();\n        }\n      },\n      dependencies: [i1.RouterOutlet, i1.RouterLink],\n      styles: [\"nav[_ngcontent-%COMP%]{\\n    color:#fff;\\n    position: fixed;\\n    top: 0;\\n    z-index: 1000;\\n    width: 100%;\\n    img{\\n        width: 100px;\\n    }\\n    .content{\\n        display: flex;\\n        justify-content: space-between;\\n        align-items: center;\\n        padding:10px 0;\\n        ul{\\n            list-style: none;\\n            display: flex;\\n            justify-content: space-between;\\n            width: auto;\\n            margin: 0;\\n            padding: 0;\\n            li{\\n                cursor: pointer;\\n                color:#000;\\n                text-decoration: none;\\n                margin: 0 10px;\\n                \\n            }\\n            li:hover{\\n                color: rgb(238, 54, 66);\\n                transform: scale(1.1);\\n            }\\n        }\\n    }\\n}\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvYXBwLmNvbXBvbmVudC5jc3MiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7SUFDSSxVQUFVO0lBQ1YsZUFBZTtJQUNmLE1BQU07SUFDTixhQUFhO0lBQ2IsV0FBVztJQUNYO1FBQ0ksWUFBWTtJQUNoQjtJQUNBO1FBQ0ksYUFBYTtRQUNiLDhCQUE4QjtRQUM5QixtQkFBbUI7UUFDbkIsY0FBYztRQUNkO1lBQ0ksZ0JBQWdCO1lBQ2hCLGFBQWE7WUFDYiw4QkFBOEI7WUFDOUIsV0FBVztZQUNYLFNBQVM7WUFDVCxVQUFVO1lBQ1Y7Z0JBQ0ksZUFBZTtnQkFDZixVQUFVO2dCQUNWLHFCQUFxQjtnQkFDckIsY0FBYzs7WUFFbEI7WUFDQTtnQkFDSSx1QkFBdUI7Z0JBQ3ZCLHFCQUFxQjtZQUN6QjtRQUNKO0lBQ0o7QUFDSiIsInNvdXJjZXNDb250ZW50IjpbIm5hdntcclxuICAgIGNvbG9yOiNmZmY7XHJcbiAgICBwb3NpdGlvbjogZml4ZWQ7XHJcbiAgICB0b3A6IDA7XHJcbiAgICB6LWluZGV4OiAxMDAwO1xyXG4gICAgd2lkdGg6IDEwMCU7XHJcbiAgICBpbWd7XHJcbiAgICAgICAgd2lkdGg6IDEwMHB4O1xyXG4gICAgfVxyXG4gICAgLmNvbnRlbnR7XHJcbiAgICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47XHJcbiAgICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcclxuICAgICAgICBwYWRkaW5nOjEwcHggMDtcclxuICAgICAgICB1bHtcclxuICAgICAgICAgICAgbGlzdC1zdHlsZTogbm9uZTtcclxuICAgICAgICAgICAgZGlzcGxheTogZmxleDtcclxuICAgICAgICAgICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xyXG4gICAgICAgICAgICB3aWR0aDogYXV0bztcclxuICAgICAgICAgICAgbWFyZ2luOiAwO1xyXG4gICAgICAgICAgICBwYWRkaW5nOiAwO1xyXG4gICAgICAgICAgICBsaXtcclxuICAgICAgICAgICAgICAgIGN1cnNvcjogcG9pbnRlcjtcclxuICAgICAgICAgICAgICAgIGNvbG9yOiMwMDA7XHJcbiAgICAgICAgICAgICAgICB0ZXh0LWRlY29yYXRpb246IG5vbmU7XHJcbiAgICAgICAgICAgICAgICBtYXJnaW46IDAgMTBweDtcclxuICAgICAgICAgICAgICAgIFxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIGxpOmhvdmVye1xyXG4gICAgICAgICAgICAgICAgY29sb3I6IHJnYigyMzgsIDU0LCA2Nik7XHJcbiAgICAgICAgICAgICAgICB0cmFuc2Zvcm06IHNjYWxlKDEuMSk7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcbiAgICB9XHJcbn1cclxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["AppComponent", "constructor", "title", "selectors", "decls", "vars", "consts", "template", "AppComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd"], "sources": ["C:\\Users\\<USER>\\Desktop\\projet angular\\Projet\\src\\app\\app.component.ts", "C:\\Users\\<USER>\\Desktop\\projet angular\\Projet\\src\\app\\app.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-root',\n  templateUrl: './app.component.html',\n  styleUrls: ['./app.component.css']\n})\nexport class AppComponent {\n  title = 'Projet';\n}\n", "<nav>\n    <div class=\"container\">\n        <div class=\"content\">\n            <img class=\"logo\" src=\"assets/logo.jpg\" alt=\"\"routerLink=\"/home\">\n            <ul>\n                <li routerLink=\"/home\">Home</li>\n                <li routerLink=\"/shop\" >Shop</li>\n            </ul>\n            <ul> \n                <li routerLink=\"/log\">Login</li>\n            </ul>\n        </div>\n    </div>\n</nav>\n<div class=\"container\"><router-outlet></router-outlet>\n</div>"], "mappings": ";;AAOA,OAAM,MAAOA,YAAY;EALzBC,YAAA;IAME,KAAAC,KAAK,GAAG,QAAQ;;;;uBADLF,YAAY;IAAA;EAAA;;;YAAZA,YAAY;MAAAG,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCPzBE,EAAA,CAAAC,cAAA,UAAK;UAGOD,EAAA,CAAAE,SAAA,aAAiE;UACjEF,EAAA,CAAAC,cAAA,SAAI;UACuBD,EAAA,CAAAG,MAAA,WAAI;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAChCJ,EAAA,CAAAC,cAAA,YAAwB;UAAAD,EAAA,CAAAG,MAAA,WAAI;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAErCJ,EAAA,CAAAC,cAAA,SAAI;UACsBD,EAAA,CAAAG,MAAA,aAAK;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAKhDJ,EAAA,CAAAC,cAAA,cAAuB;UAAAD,EAAA,CAAAE,SAAA,qBAA+B;UACtDF,EAAA,CAAAI,YAAA,EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}