{"ast": null, "code": "export function createObject(keys, values) {\n  return keys.reduce((result, key, i) => (result[key] = values[i], result), {});\n}", "map": {"version": 3, "names": ["createObject", "keys", "values", "reduce", "result", "key", "i"], "sources": ["C:/Users/<USER>/Desktop/projet angular/Projet/node_modules/rxjs/dist/esm/internal/util/createObject.js"], "sourcesContent": ["export function createObject(keys, values) {\n    return keys.reduce((result, key, i) => ((result[key] = values[i]), result), {});\n}\n"], "mappings": "AAAA,OAAO,SAASA,YAAYA,CAACC,IAAI,EAAEC,MAAM,EAAE;EACvC,OAAOD,IAAI,CAACE,MAAM,CAAC,CAACC,MAAM,EAAEC,GAAG,EAAEC,CAAC,MAAOF,MAAM,CAACC,GAAG,CAAC,GAAGH,MAAM,CAACI,CAAC,CAAC,EAAGF,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;AACnF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}