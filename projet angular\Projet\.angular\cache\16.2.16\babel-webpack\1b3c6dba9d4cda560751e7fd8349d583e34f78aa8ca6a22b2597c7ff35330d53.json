{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class AppComponent {\n  constructor() {\n    this.title = 'Projet';\n  }\n  static {\n    this.ɵfac = function AppComponent_Factory(t) {\n      return new (t || AppComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppComponent,\n      selectors: [[\"app-root\"]],\n      decls: 18,\n      vars: 0,\n      consts: [[1, \"container\"], [1, \"content\"], [\"src\", \"assets/women's wear.png\", \"alt\", \"\", \"routerLink\", \"/home\", 1, \"logo\"], [1, \"navmenu\"], [\"routerLink\", \"/home\"], [\"routerLink\", \"/shop\"], [\"routerLink\", \"/login\"], [1, \"end-text\"]],\n      template: function AppComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"nav\")(1, \"div\", 0)(2, \"div\", 1);\n          i0.ɵɵelement(3, \"img\", 2);\n          i0.ɵɵelementStart(4, \"ul\", 3)(5, \"li\", 4);\n          i0.ɵɵtext(6, \"Home\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"li\", 5);\n          i0.ɵɵtext(8, \"Shop\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(9, \"ul\")(10, \"li\", 6);\n          i0.ɵɵtext(11, \"Log in\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(12, \"div\", 0);\n          i0.ɵɵelement(13, \"router-outlet\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(14, \"br\");\n          i0.ɵɵelementStart(15, \"div\", 7)(16, \"p\");\n          i0.ɵɵtext(17, \"Copyright \\u00A9 2024 .All Rights Reserved.Designed By ... \");\n          i0.ɵɵelementEnd()();\n        }\n      },\n      dependencies: [i1.RouterOutlet, i1.RouterLink],\n      styles: [\"nav[_ngcontent-%COMP%]{\\n    color:right(#dbdfdc,#edfff1);\\n    top: 0;\\n    z-index: 1000;\\n    width: 100%;\\n    img{\\n        width: 100px;\\n    }\\n    .content{\\n        display: flex;\\n        justify-content: space-between;\\n        align-items: center;\\n        padding:10px 0;\\n        ul{\\n            list-style: none;\\n            display: flex;\\n            justify-content: space-between;\\n            width: auto;\\n            margin: 0;\\n            padding: 0;\\n            li{\\n                cursor: pointer;\\n                color:#000;\\n                text-decoration: none;\\n                margin: 0 10px;\\n                \\n            }\\n            li:hover{\\n                color: #a4936d;\\n                transform: scale(1.1);\\n            }\\n        }\\n    }\\n}\\n.end-text[_ngcontent-%COMP%]{\\n    background-color: #edfff1;\\n    text-align: center;\\n    padding: 20px;\\n}\\n.end-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{\\n    color: #111;\\n    text-transform: capitalize;\\n\\n}\\n@media(max-width:890px){\\n    nav[_ngcontent-%COMP%]{\\n        padding:20px 3%;\\n        transition: .4s;\\n    }\\n}\\n@media(max-width:750px){\\n    .navmenu[_ngcontent-%COMP%]{\\n        position: absolute;\\n        top:100%;\\n        right: 0;\\n        width: 300px;\\n        height: 130vh;\\n        background: #dbdfdc;\\n        display: flex;\\n        flex-direction: column;\\n        align-items: center;\\n        padding: 120px 30px;\\n        transition: all .42s;\\n        a{\\n            display: block;\\n            margin: 18px 0;\\n        }\\n    }\\n}\\n        \\n\\n\\n\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["AppComponent", "constructor", "title", "selectors", "decls", "vars", "consts", "template", "AppComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd"], "sources": ["C:\\Users\\<USER>\\Desktop\\projet angular\\Projet\\src\\app\\app.component.ts", "C:\\Users\\<USER>\\Desktop\\projet angular\\Projet\\src\\app\\app.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\n\n@Component({\n  selector: 'app-root',\n  templateUrl: './app.component.html',\n  styleUrls: ['./app.component.css']\n})\nexport class AppComponent {\n  title = 'Projet';\n}\n", "<nav>\n    <div class=\"container\">\n        <div class=\"content\">\n            <img class=\"logo\" src=\"assets/women's wear.png\" alt=\"\"routerLink=\"/home\">\n            <ul class=\"navmenu\">\n                <li routerLink=\"/home\">Home</li>\n                <li routerLink=\"/shop\" >Shop</li>\n            </ul>\n            <ul> \n                <li routerLink=\"/login\">Log in</li>\n            </ul>\n        </div>\n    </div>\n</nav>\n<div class=\"container\"><router-outlet></router-outlet>\n</div>\n<br>\n<div class=\"end-text\">\n    <p>Copyright © 2024 .All Rights Reserved.Designed By ... </p>\n</div>"], "mappings": ";;AAOA,OAAM,MAAOA,YAAY;EALzBC,YAAA;IAME,KAAAC,KAAK,GAAG,QAAQ;;;;uBADLF,YAAY;IAAA;EAAA;;;YAAZA,YAAY;MAAAG,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCPzBE,EAAA,CAAAC,cAAA,UAAK;UAGOD,EAAA,CAAAE,SAAA,aAAyE;UACzEF,EAAA,CAAAC,cAAA,YAAoB;UACOD,EAAA,CAAAG,MAAA,WAAI;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAChCJ,EAAA,CAAAC,cAAA,YAAwB;UAAAD,EAAA,CAAAG,MAAA,WAAI;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAErCJ,EAAA,CAAAC,cAAA,SAAI;UACwBD,EAAA,CAAAG,MAAA,cAAM;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAKnDJ,EAAA,CAAAC,cAAA,cAAuB;UAAAD,EAAA,CAAAE,SAAA,qBAA+B;UACtDF,EAAA,CAAAI,YAAA,EAAM;UACNJ,EAAA,CAAAE,SAAA,UAAI;UACJF,EAAA,CAAAC,cAAA,cAAsB;UACfD,EAAA,CAAAG,MAAA,mEAAsD;UAAAH,EAAA,CAAAI,YAAA,EAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}