body{margin-top: 100px;}

.shop-container {
  padding: 60px 20px;
  background-color: var(--light-pink);
  min-height: 100vh;
}

.shop-header {
  text-align: center;
  margin-bottom: 50px;
}

.shop-header h2 {
  font-size: 38px;
  color: var(--dark-pink);
  position: relative;
  display: inline-block;
  font-family: var(--font-cursive);
  padding: 0 15px;
}

.shop-header h2::before,
.shop-header h2::after {
  content: '♥';
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  color: var(--primary);
  font-size: 24px;
}

.shop-header h2::before {
  left: -30px;
}

.shop-header h2::after {
  right: -30px;
}

.filters {
  display: flex;
  justify-content: center;
  margin-bottom: 40px;
  flex-wrap: wrap;
}

.filter-btn {
  padding: 10px 25px;
  margin: 0 12px 12px;
  background-color: var(--white);
  border: none;
  border-radius: 30px;
  cursor: pointer;
  transition: var(--transition);
  color: var(--dark-pink);
  box-shadow: 0 3px 10px rgba(240, 98, 146, 0.1);
  font-weight: 500;
}

.filter-btn:hover, 
.filter-btn.active {
  background-color: var(--primary);
  color: var(--white);
  box-shadow: 0 5px 15px rgba(240, 98, 146, 0.2);
  transform: translateY(-3px);
}

.row {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 40px;
  margin-bottom: 60px;
}

.item {
  background-color: var(--white);
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 10px 25px rgba(240, 98, 146, 0.15);
  transition: all 0.4s ease;
  display: flex;
  flex-direction: column;
  position: relative;
  height: 700px;
  width: 400px;
  border: 1px solid rgba(240, 98, 146, 0.1);
}

.item:hover {
  transform: translateY(-12px);
  box-shadow: 0 15px 35px rgba(240, 98, 146, 0.25);
  border-color: var(--primary);
}

.item img {
  width: 100%;
  height: 380px;
  object-fit: cover;
  transition: all 0.5s ease;
  border-bottom: 3px solid var(--primary);
}

.item:hover img {
  transform: scale(1.05);
}

.body {
  padding: 30px;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  background: linear-gradient(to bottom, var(--white), var(--light-pink));
}

.item h4 {
  font-size: 24px;
  color: var(--dark-pink);
  margin-bottom: 18px;
  font-weight: 600;
  position: relative;
  padding-bottom: 12px;
}

.item h4::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 2px;
  background-color: var(--primary);
}

.item p {
  color: #666;
  font-size: 18px;
  margin-bottom: 25px;
  height: 80px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.px-3 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
  padding: 0 25px 25px;
}

.btn {
  padding: 12px 25px;
  background-color: var(--primary);
  color: white;
  border: none;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 18px;
  font-weight: 500;
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 0;
  height: 100%;
  background-color: var(--accent);
  transition: all 0.4s ease;
  z-index: -1;
  border-radius: 25px;
}

.btn:hover::before {
  width: 100%;
}

.btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(240, 98, 146, 0.3);
}

span.price {
  font-weight: 600;
  color: var(--accent);
  font-size: 24px;
  position: relative;
}

span.price::before {
  content: 'DT';
  position: absolute;
  right: -28px;
  font-size: 18px;
  top: 0;
  color: var(--dark-pink);
}

@media (max-width: 1400px) {
  .row {
    grid-template-columns: repeat(2, 1fr);
    gap: 35px;
  }
  
  .item {
    min-height: 580px;
  }
  
  .item img {
    height: 360px;
  }
}

@media (max-width: 992px) {
  .row {
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
  }
  
  .item {
    min-height: 550px;
  }
  
  .item img {
    height: 340px;
  }
}

@media (max-width: 768px) {
  .row {
    grid-template-columns: 1fr;
  }
  
  .item {
    min-height: 520px;
  }
  
  .item img {
    height: 320px;
  }
  
  .shop-header h2 {
    font-size: 32px;
  }
}
